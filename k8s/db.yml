---
apiVersion: v1
kind: Secret
metadata:
  name: demo-db
type: servicebinding.io/postgresql
stringData:
  type: "postgresql"
  provider: "postgresql"
  host: "demo-db"
  port: "5432"
  database: "petclinic"
  username: "user"
  password: "pass"

---
apiVersion: v1
kind: Service
metadata:
  name: demo-db
spec:
  ports:
    - port: 5432
  selector:
    app: demo-db

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-db
  labels:
    app: demo-db
spec:
  selector:
    matchLabels:
      app: demo-db
  template:
    metadata:
      labels:
        app: demo-db
    spec:
      containers:
        - image: postgres:17
          name: postgresql
          env:
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: demo-db
                  key: username
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: demo-db
                  key: password
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: demo-db
                  key: database
          ports:
            - containerPort: 5432
              name: postgresql
          livenessProbe:
            tcpSocket:
              port: postgresql
          readinessProbe:
            tcpSocket:
              port: postgresql
          startupProbe:
            tcpSocket:
              port: postgresql
