@font-face {
  font-family: 'varela_roundregular';

  src: url('../fonts/varela_round-webfont.eot');
  src: url('../fonts/varela_round-webfont.eot?#iefix') format('embedded-opentype'),
       url('../fonts/varela_round-webfont.woff') format('woff'),
       url('../fonts/varela_round-webfont.ttf') format('truetype'),
       url('../fonts/varela_round-webfont.svg#varela_roundregular') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'montserratregular';
  src: url('../fonts/montserrat-webfont.eot');
  src: url('../fonts/montserrat-webfont.eot?#iefix') format('embedded-opentype'),
       url('../fonts/montserrat-webfont.woff') format('woff'),
       url('../fonts/montserrat-webfont.ttf') format('truetype'),
       url('../fonts/montserrat-webfont.svg#montserratregular') format('svg');
  font-weight: normal;
  font-style: normal;
}

body, h1, h2, h3, p, input {
  margin: 0;
  font-weight: 400;
  font-family: "varela_roundregular", sans-serif;
  color: #34302d;
}

h1 {
  font-size: 24px;
  line-height: 30px;
  font-family: "montserratregular", sans-serif;
}

h2 {
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
  margin-bottom: 10px;
  font-family: "montserratregular", sans-serif;
}

h3 {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 10px;
  font-weight: 700;
}

p {
  //font-size: 15px;
  //line-height: 24px;
}

strong {
  font-weight: 700;
  font-family: "montserratregular", sans-serif;
}
