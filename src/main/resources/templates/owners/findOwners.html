<html xmlns:th="https://www.thymeleaf.org"
  th:replace="~{fragments/layout :: layout (~{::body},'owners')}">

<body>

  <h2>Find Owners</h2>

  <form th:object="${owner}" th:action="@{/owners}" method="get"
    class="form-horizontal" id="search-owner-form">
    <div class="form-group">
      <div class="control-group" id="lastNameGroup">
        <label class="col-sm-2 control-label">Last name </label>
        <div class="col-sm-10">
          <input class="form-control" th:field="*{lastName}" size="30"
            maxlength="80" /> <span class="help-inline"><div
              th:if="${#fields.hasAnyErrors()}">
              <p th:each="err : ${#fields.allErrors()}" th:text="${err}">Error</p>
            </div></span>
        </div>
      </div>
    </div>
    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10">
        <button type="submit" class="btn btn-primary">Find
          Owner</button>
      </div>
    </div>

    <a class="btn btn-primary" th:href="@{/owners/new}">Add Owner</a>

  </form>

</body>
</html>
