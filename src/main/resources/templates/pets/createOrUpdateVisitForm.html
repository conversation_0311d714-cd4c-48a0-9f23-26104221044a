<html xmlns:th="https://www.thymeleaf.org"
  th:replace="~{fragments/layout :: layout (~{::body},'owners')}">

<body>

  <h2>
    <th:block th:if="${visit['new']}">New </th:block>
    Visit
  </h2>

  <b>Pet</b>
  <table class="table table-striped">
    <thead>
      <tr>
        <th>Name</th>
        <th>Birth Date</th>
        <th>Type</th>
        <th>Owner</th>
      </tr>
    </thead>
    <tr>
      <td th:text="${pet.name}"></td>
      <td
        th:text="${#temporals.format(pet.birthDate, 'yyyy-MM-dd')}"></td>
      <td th:text="${pet.type}"></td>
      <td
        th:text="${owner?.firstName + ' ' + owner?.lastName}"></td>
    </tr>
  </table>

  <form th:object="${visit}" class="form-horizontal" method="post">
    <div class="form-group has-feedback">
      <input
        th:replace="~{fragments/inputField :: input ('Date', 'date', 'date')}"  />
      <input
        th:replace="~{fragments/inputField :: input ('Description', 'description', 'text')}" />
    </div>

    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10">
        <input type="hidden" name="petId" th:value="${pet.id}" />
        <button class="btn btn-primary" type="submit">Add Visit</button>
      </div>
    </div>
  </form>

  <br />
  <b>Previous Visits</b>
  <table class="table table-striped">
    <tr>
      <th>Date</th>
      <th>Description</th>
    </tr>
    <tr th:if="${!visit['new']}" th:each="visit : ${pet.visits}">
      <td th:text="${#temporals.format(visit.date, 'yyyy-MM-dd')}"></td>
      <td th:text=" ${visit.description}"></td>
    </tr>
  </table>

</body>
</html>
