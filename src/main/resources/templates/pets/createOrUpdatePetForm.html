<html xmlns:th="https://www.thymeleaf.org"
  th:replace="~{fragments/layout :: layout (~{::body},'owners')}">

<body>

  <h2>
    <th:block th:if="${pet['new']}">New </th:block>
    Pet
  </h2>
  <form th:object="${pet}" class="form-horizontal" method="post">
    <input type="hidden" name="id" th:value="*{id}" />
    <div class="form-group has-feedback">
      <div class="form-group">
        <label class="col-sm-2 control-label">Owner</label>
        <div class="col-sm-10">
          <span th:text="${owner?.firstName + ' ' + owner?.lastName}" />
        </div>
      </div>
      <input
        th:replace="~{fragments/inputField :: input ('Name', 'name', 'text')}" />
      <input
        th:replace="~{fragments/inputField :: input ('Birth Date', 'birthDate', 'date')}" />
      <input
        th:replace="~{fragments/selectField :: select ('Type', 'type', ${types})}" />
    </div>
    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10">
        <button
          th:with="text=${pet['new']} ? 'Add Pet' : 'Update Pet'"
          class="btn btn-primary" type="submit" th:text="${text}">Add
          Pet</button>
      </div>
    </div>
  </form>

</body>

</html>
