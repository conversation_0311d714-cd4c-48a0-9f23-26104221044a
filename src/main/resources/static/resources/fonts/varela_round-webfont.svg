<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="varela_roundregular" horiz-adv-x="1286" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="532" />
<glyph horiz-adv-x="2048" />
<glyph horiz-adv-x="2048" />
<glyph unicode="&#xd;" horiz-adv-x="681" />
<glyph unicode=" "  horiz-adv-x="532" />
<glyph unicode="&#x09;" horiz-adv-x="532" />
<glyph unicode="&#xa0;" horiz-adv-x="532" />
<glyph unicode="!" horiz-adv-x="575" d="M168 113.5q0 50.5 36 86.5t86 36t86 -36t36 -86.5t-36 -86t-86 -35.5t-86 35.5t-36 86zM182 1130v203q0 45 31 76t76 31t74.5 -31t29.5 -76v-203l-37 -675q-4 -60 -67 -60q-66 0 -70 60z" />
<glyph unicode="&#x22;" horiz-adv-x="841" d="M123 1418q0 50 31.5 81t72.5 31t72 -31q33 -31 33 -81t-3 -93t-10.5 -92t-16.5 -95t-15 -73q-10 -57 -60 -57q-25 0 -40 15t-20 42t-14.5 73t-16.5 95t-10 92t-3 93zM510 1418q0 50 31.5 81t72.5 31t72 -31q33 -31 33 -81t-3 -93t-10.5 -92t-16.5 -95t-13 -73 q-12 -57 -62 -57q-25 0 -40 15t-20 42t-14.5 73t-16.5 95t-10 92t-3 93z" />
<glyph unicode="#" horiz-adv-x="1253" d="M74 444q0 29 21.5 50.5t49.5 21.5h181l49 311h-158q-29 0 -50.5 21.5t-21.5 50.5t21.5 50.5t50.5 21.5h180l52 323q4 23 23.5 40.5t50 17.5t50 -20.5t19.5 -37t-1 -23.5t-5 -37t-15 -91l-28 -172h260l51 323q4 23 23.5 40.5t50 17.5t50 -20.5t19.5 -37t-1 -23.5t-5 -37 t-14 -91l-29 -172h158q29 0 50.5 -21.5t21.5 -50.5t-21.5 -50.5t-50.5 -21.5h-180l-49 -311h157q29 0 50.5 -21.5t21.5 -50.5t-21.5 -50t-50.5 -21h-180l-51 -324q-4 -23 -23.5 -41t-48.5 -18t-50.5 20.5t-21.5 39t50 323.5h-260l-52 -324q-4 -23 -23.5 -41t-47.5 -18 q-29 0 -50.5 20.5t-21.5 39t49 323.5h-158q-29 0 -50 21.5t-21 49.5zM471 516h260l49 311h-260z" />
<glyph unicode="$" horiz-adv-x="1253" d="M96.5 149q-0.5 44 22 68.5t52.5 24.5t71 -19q143 -63 350 -69v469q-227 59 -311 114.5t-127 123t-43 159.5t30.5 164.5t91.5 130.5q131 121 359 133v160q0 20 12 33.5t33 13.5q20 0 33.5 -13.5t13.5 -33.5v-158q207 -8 365 -68q57 -20 57 -82q0 -31 -21.5 -57t-47 -26 t-68.5 16q-125 43 -285 47v-487q86 -20 172 -47t156 -72q154 -102 153 -295q0 -166 -131 -277t-350 -122v-160q0 -20 -13.5 -32.5t-33.5 -12.5q-45 0 -45 45v160q-231 10 -438 98q-57 27 -57.5 71zM295 1044q0 -135 180 -196q55 -18 117 -33v461q-129 -12 -213 -71t-84 -161 zM684 156q211 14 274 133q23 43 23 95t-23.5 84t-64.5 56q-59 37 -209 76v-444z" />
<glyph unicode="%" horiz-adv-x="1667" d="M88 1098q0 252 186 338q62 28 144 28q158 0 246 -106q84 -100 84 -260t-84 -260q-88 -106 -246 -107q-156 0 -246 107q-84 96 -84 260zM231 1098q0 -98 47.5 -166t139.5 -68t139 67.5t47 166.5q0 98 -47 165.5t-139 67.5t-139.5 -67.5t-47.5 -165.5zM350 42q0 24 10 40 l834 1333q20 33 54 33t53.5 -16.5t19.5 -42t-14 -48.5l-828 -1325q-23 -37 -55.5 -36.5t-53 19.5t-20.5 43zM920 330q0 252 186 338q61 28 143 28q158 0 246 -106q84 -100 84 -260t-84 -260q-88 -106 -246 -107q-156 0 -245 107q-84 96 -84 260zM1063 330q0 -98 47 -166 t139 -68t139.5 67.5t47.5 166.5q0 98 -47.5 165.5t-139.5 67.5t-139 -67.5t-47 -165.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1499" d="M145 401q0 139 84 256q66 88 148 138q-53 66 -89 132t-36 151t26.5 150.5t75.5 114.5q106 106 281 107q137 0 272 -90q37 -20 37 -55t-20.5 -56.5t-44 -21.5t-54.5 16q-88 47 -161.5 47t-114.5 -16.5t-70 -42.5q-55 -53 -55 -114.5t11 -96.5t36 -70q35 -53 158 -166 l428 -403q35 53 56.5 100t43 61.5t53 14.5t56 -23.5t24.5 -52.5q0 -66 -108 -217l137 -131q33 -29 33 -63.5t-21.5 -60t-60.5 -25.5t-66 24l-137 131q-186 -160 -420 -159q-252 0 -387 135q-115 115 -115 286zM322 393.5q0 -55.5 24.5 -103.5t65.5 -81q86 -72 202.5 -72 t194.5 34t137 81l-457 428q-113 -72 -153 -188q-14 -43 -14 -98.5z" />
<glyph unicode="'" horiz-adv-x="487" d="M129 1042.5q0 28.5 31 47.5q45 27 74.5 66.5t29.5 123.5h-8q-47 0 -81 34t-34 81v12q0 47 34 81t81 34h20q48 0 81.5 -34t33.5 -81v-121q0 -109 -55 -192q-39 -61 -113 -107q-10 -10 -35.5 -10t-42 18.5t-16.5 47z" />
<glyph unicode="(" horiz-adv-x="638" d="M104 629q0 258 71 494.5t218 412.5q14 16 27.5 26.5t43.5 10.5t54.5 -22.5t24.5 -52.5t-13 -50q-240 -401 -239 -819q-1 -418 239 -819q12 -23 12.5 -51.5t-24 -51t-54.5 -22.5t-43 10t-28 26q-147 176 -218 413t-71 495z" />
<glyph unicode=")" horiz-adv-x="638" d="M98 -241.5q0 28.5 13 51.5q240 401 239 819q0 418 -239 819q-12 20 -12.5 50t24 52.5t54.5 22.5t43 -10.5t28 -26.5q147 -176 218 -412.5t71 -494.5t-71 -494.5t-218 -413.5q-14 -16 -27.5 -26t-43.5 -10t-54.5 22.5t-24.5 51z" />
<glyph unicode="*" horiz-adv-x="854" d="M135 977q0 41 29 55q2 0 6 2t23.5 10.5t60.5 23.5t115 44l-205 80q-29 18 -29 43q0 55 45 70q23 8 45 -7q2 0 56.5 -44t115.5 -93q-33 207 -32 211v6q0 27 19 43.5t42 16.5t42 -16.5t19 -31t-1 -18.5t-3 -24l-10 -66q-6 -43 -18 -121l172 137q23 14 45 7q45 -14 45 -54.5 t-29 -58.5q-2 0 -6 -2t-23.5 -10.5t-60.5 -23.5t-115 -44l205 -80q29 -14 29 -41q0 -55 -45 -69q-29 -10 -51 8q-6 4 -55.5 44t-110.5 89q12 -78 18 -121l10 -65q2 -20 3 -24.5t1 -19t-19 -31t-42 -16.5t-42 16.5t-19 31.5v18q0 10 10 71.5t22 139.5q-172 -137 -183 -143.5 t-34 2.5q-45 14 -45 55z" />
<glyph unicode="+" horiz-adv-x="1150" d="M82 707q0 35 21.5 57t56.5 22h336v336q0 35 22.5 57.5t56.5 22.5q35 0 57.5 -22.5t22.5 -57.5v-336h334q35 0 57.5 -22.5t22.5 -56.5q0 -35 -22.5 -56.5t-57.5 -21.5h-334v-336q0 -35 -22.5 -57.5t-57.5 -22.5t-57 22.5t-22 57.5v336h-336q-35 0 -56.5 21.5t-21.5 56.5z " />
<glyph unicode="," horiz-adv-x="532" d="M129 -243.5q0 28.5 31 46.5q45 27 74.5 67t29.5 124h-8q-47 0 -81 33.5t-34 81.5v12q0 47 34 81t81 34h20q48 0 81.5 -34t33.5 -81v-121q0 -109 -55 -193q-39 -61 -113 -106q-10 -10 -35.5 -10t-42 18.5t-16.5 47z" />
<glyph unicode="-" horiz-adv-x="835" d="M141 573q0 35 23.5 59.5t58.5 24.5h387q35 0 59.5 -24.5t24.5 -59.5t-24.5 -58t-59.5 -23h-387q-35 0 -58.5 23.5t-23.5 57.5z" />
<glyph unicode="." horiz-adv-x="532" d="M141 109v12q0 47 34 81t81 34h20q48 0 81.5 -34t33.5 -81v-12q0 -47 -33.5 -81t-81.5 -34h-20q-47 0 -81 33.5t-34 81.5z" />
<glyph unicode="/" horiz-adv-x="907" d="M98 -123q0 31 6 43l543 1587q18 57 82 58q35 0 58.5 -24.5t23.5 -45.5q0 -33 -6 -45l-543 -1587q-18 -57 -80 -58q-37 0 -60.5 25t-23.5 47z" />
<glyph unicode="0" d="M88 715q0 338 139 530q146 205 416 205q137 0 241.5 -53.5t174.5 -151.5q139 -193 139 -530q0 -340 -139 -533q-147 -203 -415.5 -202.5t-416.5 202.5q-139 193 -139 533zM274 715q0 -262 95 -412q94 -151 274 -151t275 151q94 150 94 412q0 260 -94 409 q-94 152 -275.5 152t-275 -151.5t-93.5 -409.5z" />
<glyph unicode="1" d="M262 1195q0 48 53 75l279 145q29 14 57 15h43q41 0 70 -29t29 -70v-1157h202q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-633q-37 0 -61.5 24.5t-24.5 61.5t25 62.5t61 25.5h244v1038l-209 -102l-20 -8t-38 -4t-52.5 24.5t-24.5 72.5z" />
<glyph unicode="2" d="M133 106q0 109 60.5 195t151.5 160t197.5 139.5t197.5 131t151.5 135t60.5 139t-17 115.5t-54 79q-82 74 -252 74q-182 0 -350 -74q-20 -10 -47 -10t-48.5 22.5t-21.5 65.5t45 68q209 104 455 104q201 0 333 -115q137 -117 138 -301q0 -186 -187 -358 q-82 -74 -179 -139.5t-187.5 -126t-162 -119t-98.5 -117.5h713q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-790q-47 0 -78 29.5t-31 76.5z" />
<glyph unicode="3" d="M121 139q0 33 22.5 59.5t50 26.5t68.5 -16q127 -51 305 -51q256 0 346 116q33 43 33 111q0 258 -452 258h-33q-35 0 -60.5 25.5t-25.5 60.5t25.5 60.5t60.5 25.5h12q422 0 422 248q0 96 -79 154.5t-245 58.5q-168 0 -317 -68q-16 -8 -42 -8t-48.5 20.5t-22.5 61.5t41 64 q193 104 418 104t353 -96.5t128 -272.5q0 -248 -247 -342q195 -47 268 -204q31 -66 31 -162.5t-41 -170t-113 -122.5q-145 -100 -377 -100q-225 0 -428 81q-53 25 -53 78z" />
<glyph unicode="4" d="M51 477v19q0 43 25 73l643 822q35 49 102 49h41q53 0 86 -35t33 -84v-795h156q35 0 59.5 -24.5t24.5 -59.5t-25 -58.5t-59 -23.5h-156v-278q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5v278h-625q-49 0 -84 34t-35 83zM250 526h545v703z" />
<glyph unicode="5" d="M131 165q0 42 22.5 68.5t52.5 26.5t73 -22q141 -74 305 -74q293 0 370 149q29 55 29 140q0 127 -85 200.5t-210 73.5q-190 0 -338 -88q-43 -25 -86 -25t-82 32t-39 87q0 29 4 49l93 545q6 47 35.5 75t72.5 28h658q37 0 62.5 -26t25.5 -63t-26 -61.5t-62 -24.5h-594 l-66 -434q154 84 346 84q227 0 357 -131q121 -125 120 -332q0 -324 -309 -426q-106 -37 -250 -36q-250 0 -438 118q-41 25 -41 67z" />
<glyph unicode="6" d="M121 737q0 94 29.5 225.5t98 239t182.5 178t263.5 70.5t235.5 -27.5t147 -60.5q43 -25 43 -66t-22.5 -66.5t-52 -25.5t-62.5 17q-111 49 -249 49t-220 -56.5t-129 -136.5q-74 -133 -86 -332q90 119 270 164q66 16 160 16.5t180 -33.5t148 -97q131 -133 131 -351 q0 -199 -135 -329q-139 -135 -371 -135q-233 0 -371 126q-162 154 -184 455q-6 82 -6 176zM309 530q37 -266 211 -346q68 -31 153 -30.5t143 24t99 64.5q86 86 86 214t-75.5 214t-229.5 86q-160 0 -266 -82q-74 -55 -121 -144z" />
<glyph unicode="7" d="M123 1339q0 37 25.5 64t62.5 27h836q49 0 81.5 -31t32.5 -77t-20 -91l-582 -1190q-29 -51 -79 -51t-75.5 25.5t-25.5 58t12 57.5l563 1120h-743q-37 0 -62.5 26t-25.5 62z" />
<glyph unicode="8" d="M96 377q0 160 84 251t221 138q-164 57 -217 197q-20 51 -20 112.5t24.5 127t81.5 120.5q129 127 373 127t373 -127q106 -105 106 -248q0 -61 -19.5 -111.5t-51.5 -89.5q-61 -72 -166 -108q137 -47 221 -138t84 -249q0 -162 -121 -268q-147 -131 -426 -131t-426 131 q-121 106 -121 266zM283 399q0 -90 77 -161q97 -95 283 -95t283 95q78 72 78 151.5t-30 130.5t-81 86q-98 70 -248.5 70t-251.5 -70q-111 -76 -110 -207zM350 1068q0 -87 70.5 -155.5t222.5 -68.5t229 76q63 61 63.5 148t-71 152.5t-221.5 65.5t-221.5 -65.5t-71.5 -152.5z " />
<glyph unicode="9" d="M98 985q0 201 135 330q143 135 371 135q233 0 371 -127q162 -154 184 -455q6 -82 6 -177t-29.5 -225t-98 -237.5t-183.5 -178t-263.5 -70.5t-234.5 27.5t-147 60.5q-43 25 -43 65.5t22.5 66t51 25.5t63.5 -16q115 -49 250 -49t218 56t130 136q74 133 86 332 q-90 -119 -270 -164q-66 -16 -160 -16t-180 33.5t-148 97.5q-131 133 -131 350zM285 974q0 -128 75.5 -214t229.5 -86q160 0 266 82q74 55 121 143q-37 266 -211 346q-68 31 -153 31t-143 -23.5t-99 -64.5q-86 -86 -86 -214z" />
<glyph unicode=":" horiz-adv-x="532" d="M141 109v12q0 47 34 81t81 34h20q47 0 81 -34t34 -81v-12q0 -47 -33.5 -81t-81.5 -34h-20q-47 0 -81 33.5t-34 81.5zM141 924v12q0 47 34 81t81 34h20q47 0 81 -34t34 -81v-12q0 -47 -33.5 -81t-81.5 -34h-20q-47 0 -81 34t-34 81z" />
<glyph unicode=";" horiz-adv-x="532" d="M129 -243.5q0 28.5 31 46.5q45 27 74.5 67t29.5 124h-8q-47 0 -81 33.5t-34 81.5v12q0 47 34 81t81 34h20q47 0 81 -34t34 -81v-121q0 -109 -55 -193q-39 -61 -113 -106q-10 -10 -35.5 -10t-42 18.5t-16.5 47zM141 924v12q0 47 34 81t81 34h20q47 0 81 -34t34 -81v-12 q0 -47 -33.5 -81t-81.5 -34h-20q-47 0 -81 34t-34 81z" />
<glyph unicode="&#x3c;" horiz-adv-x="1187" d="M72 598v16q0 72 73 107l818 366q14 6 40.5 6.5t50 -23t23.5 -54.5q0 -59 -55 -82l-737 -328l737 -327q25 -10 40 -32t15 -51.5t-23.5 -53t-50 -23.5t-40.5 6l-818 367q-74 35 -73 106z" />
<glyph unicode="=" horiz-adv-x="1458" d="M150 449q0 35 22.5 57t56.5 22h1000q35 0 57.5 -22.5t22.5 -56.5q0 -35 -22.5 -57.5t-57.5 -22.5h-1000q-35 0 -57 22.5t-22 57.5zM150 907q0 35 22.5 57.5t56.5 22.5h1000q35 0 57.5 -22.5t22.5 -57.5t-22.5 -57.5t-57.5 -22.5h-1000q-35 0 -57 22.5t-22 57.5z" />
<glyph unicode="&#x3e;" horiz-adv-x="1187" d="M111 197q0 59 55 82l737 327l-737 328q-25 10 -40 31.5t-15 51.5t23.5 53.5t49 23.5t41.5 -7l817 -366q74 -35 74 -107v-16q0 -72 -74 -106l-817 -367q-16 -6 -41.5 -6t-49 23.5t-23.5 54.5z" />
<glyph unicode="?" horiz-adv-x="1017" d="M53 1270q0 51 51 82q160 98 361 98q211 0 334 -104q127 -106 127 -283q0 -184 -193 -326q-113 -84 -162 -164q-29 -47 -28.5 -97t-27.5 -72.5t-64.5 -22.5t-62.5 23.5t-26 56.5q0 203 191 350q123 98 155.5 144.5t32.5 97.5q0 223 -292 223q-160 0 -263 -68 q-27 -16 -56.5 -16t-53 23.5t-23.5 54.5zM328 113.5q0 50.5 35.5 86.5t86 36t86 -36t35.5 -86.5t-35.5 -86t-86 -35.5t-86 35.5t-35.5 86z" />
<glyph unicode="@" horiz-adv-x="1839" d="M100 555q0 158 70 315.5t187.5 277.5t275.5 190.5t325.5 70.5t310 -52t249 -145.5t169 -223.5t62.5 -288q0 -205 -86 -344q-104 -162 -295 -161q-154 0 -209 96q-14 29 -20 55q-127 -154 -338 -153q-164 0 -260 102q-88 98 -88 237.5t42 232.5t109 157q137 129 334 129 q152 0 262 -105l6 35q4 29 25.5 46t51.5 17t51.5 -21.5t21.5 -39.5v-22q0 -4 -2 -15l-68 -389q-14 -86 -14 -136t23.5 -90t78.5 -40q129 0 196.5 112.5t67.5 278.5t-58 278.5t-155 188.5q-190 152 -456 151q-156 0 -294 -61t-242.5 -165.5t-165 -243t-60.5 -271.5t40 -240.5 t118 -191.5q174 -190 454 -190q133 0 209 20q94 25 163 51.5t85 26.5t30.5 -13.5t14.5 -41t-29 -41.5q-217 -109 -473 -109q-164 0 -297 58.5t-227 155.5t-144.5 225t-50.5 286zM610 573q0 -121 70 -184q61 -55 166 -55q174 0 254 145q31 55 45 142l33 190q-88 94 -232 94 q-139 0 -233 -86q-102 -94 -103 -246z" />
<glyph unicode="A" horiz-adv-x="1427" d="M53 70.5q0 27.5 10 52.5l523 1233q37 84 119 84h20q82 0 119 -84l522 -1233q10 -25 10 -52.5t-24.5 -54t-77.5 -26.5t-78 57l-133 320h-696l-134 -320q-25 -57 -78 -57t-77.5 26.5t-24.5 54zM438 537h553l-276 665z" />
<glyph unicode="B" horiz-adv-x="1419" d="M199 92v1243q0 39 26.5 67t65.5 28h483q225 0 342 -102.5t117 -268.5q0 -221 -201 -316q195 -66 258 -215q23 -57 23 -137t-29 -153.5t-92 -126.5q-131 -111 -406 -111h-495q-39 0 -65.5 26.5t-26.5 65.5zM385 174h383q291 0 346 139q18 43 19 97q0 123 -92.5 178 t-270.5 55h-385v-469zM385 811h340q264 0 315 141q15 43 15 92q0 96 -77 153.5t-218 57.5h-375v-444z" />
<glyph unicode="C" horiz-adv-x="1429" d="M319 1253q199 197 512 197q152 0 261.5 -37t168 -68.5t70.5 -51t12 -49.5t-21.5 -53.5t-51 -23.5t-45.5 9q-82 43 -168 69.5t-214 26.5t-231.5 -43t-173.5 -119q-137 -150 -137 -395q0 -246 137 -396q149 -161 409 -161h3q121 0 207 26.5t168 69.5q16 8 45.5 8t51 -23.5 t21.5 -54.5q0 -55 -45 -78q-96 -53 -205.5 -89.5t-262 -36.5t-283.5 50t-228 146q-204 200 -204 536q0 341 204 541z" />
<glyph unicode="D" horiz-adv-x="1536" d="M199 92v1243q0 39 26.5 67t65.5 28h411q342 0 545 -230q90 -100 134 -228t44 -257t-44 -257t-134 -229q-203 -229 -545 -229h-411q-39 0 -65.5 26.5t-26.5 65.5zM385 174h295q254 0 410 150q152 150 152 389q0 242 -152 393q-155 149 -407 149h-3h-295v-1081z" />
<glyph unicode="E" horiz-adv-x="1306" d="M199 92v1243q0 39 26.5 67t65.5 28h821q37 0 62.5 -26t25.5 -63t-25.5 -61.5t-62.5 -24.5h-727v-452h653q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-653v-455h727q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-821q-39 0 -65.5 26.5t-26.5 65.5z " />
<glyph unicode="F" horiz-adv-x="1249" d="M199 82v1253q0 39 26.5 67t65.5 28h796q37 0 63 -26t26 -63t-26 -61.5t-63 -24.5h-702v-452h631q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-631v-547q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="G" horiz-adv-x="1523" d="M113 713q0 326 206 536q199 201 506 201q174 0 294 -43t216 -104q41 -23 41 -65t-22.5 -67.5t-54 -25.5t-52.5 10q-199 115 -401 115q-238 0 -389 -144q-158 -152 -158 -413q0 -242 147 -398q152 -159 410 -159q193 0 358 90v370h-337q-35 0 -59.5 25t-24.5 59 q0 35 24.5 59.5t59.5 24.5h403q41 0 69.5 -28.5t28.5 -69.5v-465q0 -43 -21.5 -76.5t-56.5 -50.5q-227 -114 -465 -114h-4q-322 0 -520 204q-199 207 -198 529z" />
<glyph unicode="H" horiz-adv-x="1546" d="M199 82v1264q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-545h776v545q0 39 26.5 66.5t65.5 27.5t67 -28t28 -66v-1264q0 -39 -28 -65.5t-67 -26.5t-65.5 26.5t-26.5 65.5v545h-776v-545q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="I" horiz-adv-x="583" d="M199 82v1264q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-1264q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="J" horiz-adv-x="954" d="M25 116q0 42 21.5 67.5t49 25.5t56.5 -14q78 -39 150.5 -39t117.5 11t80 44q78 74 78 248v887q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-875q0 -268 -133 -393q-106 -98 -289 -98q-150 0 -272 69q-45 25 -45 67z" />
<glyph unicode="K" horiz-adv-x="1372" d="M199 82v1264q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-652l700 713q27 33 67 33t64.5 -23.5t24.5 -61.5t-22 -61l-474 -471l543 -684q23 -29 23 -63.5t-25.5 -60t-68.5 -25.5t-70 37l-529 673l-233 -235v-383q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z " />
<glyph unicode="L" horiz-adv-x="1179" d="M199 129v1217q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-1160h670q39 0 66.5 -27.5t27.5 -66.5t-27.5 -65.5t-66.5 -26.5h-727q-53 0 -91 38t-38 91z" />
<glyph unicode="M" horiz-adv-x="1978" d="M199 82v1223q0 55 38.5 95t94.5 40h67q41 0 75 -24.5t50 -61.5l465 -1159l465 1159q16 37 50 61.5t75 24.5h68q55 0 94 -40t39 -95v-1223q0 -39 -27 -65.5t-65 -26.5q-39 0 -67 26.5t-28 65.5v1163l-473 -1169q-16 -39 -51 -62.5t-80 -23.5t-80 23.5t-51 62.5l-473 1169 v-1163q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="N" horiz-adv-x="1644" d="M199 82v1223q0 55 38.5 95t94.5 40h26q63 0 113 -62l789 -1138v1106q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-1223q0 -55 -40 -94t-95 -39h-25q-68 0 -108 55l-793 1145v-1108q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="O" horiz-adv-x="1660" d="M113 715q4 324 198 528q194 207 520 207q324 0 521 -207q190 -203 198 -528q-8 -326 -198 -529q-196 -206 -518 -206h-3q-326 0 -520 206q-195 205 -198 529zM299 715q0 -244 139 -400q143 -161 393 -161t394 161q139 156 139 400t-139 399q-143 162 -394 162 q-250 0 -393 -162q-139 -156 -139 -399z" />
<glyph unicode="P" horiz-adv-x="1370" d="M199 82v1253q0 39 26.5 67t65.5 28h467q285 0 428 -148q119 -119 119 -307t-119 -307q-143 -147 -428 -148h-373v-438q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5zM385 696h354q211 0 305 90q74 70 74 189q0 190 -190 254q-71 24 -180 24h-9h-354v-557z" />
<glyph unicode="Q" horiz-adv-x="1662" d="M113 715q4 324 198 528q195 207 520 207q324 0 521 -207q190 -203 198 -528q-4 -242 -114.5 -420t-309.5 -260l181 -199q25 -25 25 -59q0 -36 -26 -63q-26 -25 -64 -25t-64 32l-224 267q-57 -8 -123 -8q-326 0 -520 206q-195 205 -198 529zM299 715q0 -244 139 -400 q143 -161 393 -161t394 161q139 156 139 400t-139 399q-143 162 -394 162q-250 0 -393 -162q-139 -156 -139 -399z" />
<glyph unicode="R" horiz-adv-x="1458" d="M199 82v1253q0 39 26.5 67t65.5 28h477q281 0 424 -119q133 -113 133 -297q0 -119 -66.5 -221.5t-201.5 -149.5q147 -39 213 -213q43 -117 43 -254v-94q0 -39 -26.5 -65.5t-67.5 -26.5t-67 25.5t-26 66.5v61q0 166 -36.5 244t-100 124t-180.5 46h-424v-475 q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5zM385 733h371q172 0 277.5 67.5t105.5 198.5q0 123 -82 185q-97 69 -306 69h-6h-360v-520z" />
<glyph unicode="S" horiz-adv-x="1261" d="M104 165q0 32 23 57.5t52.5 25.5t73.5 -19.5t144.5 -46t233.5 -26.5q358 0 358 235q0 98 -137 158q-59 25 -138 44t-160 41.5t-160 51.5t-140 74q-135 102 -135 270q0 180 135 295q147 125 395 125q223 0 408 -68q57 -20 57 -82q0 -31 -21.5 -58.5t-47 -27.5t-70.5 17 q-133 47 -305 47q-158 0 -260 -59q-104 -61 -105 -175q0 -117 135 -176q61 -29 139 -47t160 -38.5t160 -48t139 -72.5q135 -98 136 -283q0 -176 -142 -287q-144 -112 -379 -112h-4q-260 0 -487 98q-23 10 -40.5 32.5t-17.5 54.5z" />
<glyph unicode="T" horiz-adv-x="1251" d="M29 1341q0 37 24.5 63t61.5 26h1020q36 0 62 -26t26 -63t-26 -61.5t-62 -24.5h-416v-1173q0 -39 -27.5 -65.5t-66.5 -26.5t-66 26.5t-27 65.5v1173h-417q-37 0 -61.5 25t-24.5 61z" />
<glyph unicode="U" horiz-adv-x="1552" d="M188 537v809q0 39 27 66.5t66 27.5t66.5 -28t27.5 -66v-816q0 -182 125 -286q115 -94 276 -94q162 0 277 94q125 104 125 286v816q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-809q0 -268 -193 -422q-166 -135 -395 -135q-233 0 -397 135q-190 154 -191 422z" />
<glyph unicode="V" horiz-adv-x="1421" d="M49 1355.5q0 30.5 24.5 57.5t61.5 27q68 0 94 -64l482 -1200l481 1200q27 64 79 64h2q51 0 75 -27t24 -57.5t-6 -44.5l-506 -1227q-43 -94 -127 -94h-45q-84 0 -127 94l-506 1227q-6 14 -6 44.5z" />
<glyph unicode="W" horiz-adv-x="2062" d="M53 1365.5q0 27.5 24.5 51t61.5 23.5q74 0 97 -74l344 -1130l346 1126q10 35 39.5 56.5t66.5 21.5t66.5 -21.5t40.5 -56.5l346 -1126l344 1130q23 74 92 74q41 0 65.5 -23.5t24.5 -51.5t-6 -50l-401 -1233q-33 -92 -121 -92q-41 0 -75 25.5t-46 66.5l-330 1044 l-330 -1044q-12 -41 -45.5 -66.5t-74.5 -25.5q-88 0 -121 92l-402 1233q-6 23 -6 50.5z" />
<glyph unicode="X" horiz-adv-x="1370" d="M88 75q0 34 18 58l474 582l-469 577q-19 26 -19 62q0 34 26 60t69.5 26t70.5 -37l432 -549l432 553q23 33 64 33t67.5 -25.5t26.5 -58.5t-16 -56l-473 -583l473 -582q18 -23 18 -56.5t-24.5 -61t-57.5 -27.5q-55 0 -84 39l-438 551l-432 -553q-31 -37 -69 -37t-63.5 25.5 t-25.5 59.5z" />
<glyph unicode="Y" horiz-adv-x="1320" d="M61 1353q0 34 25 60q25 27 71 27t76 -47l429 -623l428 623q31 47 76.5 47t70.5 -27t25 -61.5t-17 -61.5l-491 -686v-522q0 -39 -27 -65.5t-65 -26.5q-39 0 -67 26.5t-28 65.5v522l-489 686q-17 28 -17 63z" />
<glyph unicode="Z" horiz-adv-x="1306" d="M96 117q0 47 25 78l856 1056h-789q-37 0 -62.5 26t-25.5 62q0 37 26 64t62 27h889q59 0 92 -38t33 -86.5t-33 -84.5l-845 -1047h802q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-911q-49 0 -84 35t-35 82z" />
<glyph unicode="[" horiz-adv-x="659" d="M178 -180v1589q0 37 26.5 63.5t63.5 26.5h260q31 0 52.5 -21.5t21.5 -52.5t-21.5 -52t-52.5 -21h-188v-1477h188q31 0 52.5 -21.5t21.5 -50.5t-21.5 -51t-52.5 -22h-260q-37 0 -63.5 26.5t-26.5 63.5z" />
<glyph unicode="\" horiz-adv-x="907" d="M96 1489q0 27 23.5 51.5t56.5 24.5q66 0 84 -58l543 -1587q6 -12 6 -38.5t-23.5 -51.5t-56.5 -25q-66 0 -84 58l-543 1587q-6 12 -6 39z" />
<glyph unicode="]" horiz-adv-x="659" d="M57 -197q0 29 21.5 50.5t52.5 21.5h188v1477h-188q-31 0 -52.5 21.5t-21.5 51.5q0 31 21.5 52.5t52.5 21.5h260q37 0 63.5 -26.5t26.5 -63.5v-1589q0 -37 -26.5 -63.5t-63.5 -26.5h-260q-31 0 -52.5 22.5t-21.5 50.5z" />
<glyph unicode="^" horiz-adv-x="1073" d="M168 914.5q0 19.5 4 27.5t8 19l215 407q16 29 38 49.5t63 20.5h77q41 0 62.5 -20.5t38.5 -49.5l215 -407q4 -10 8 -18.5t4 -28t-20.5 -42t-53.5 -22.5q-41 0 -61 33q-4 8 -26.5 46t-70.5 115.5t-134 217.5q-86 -139 -133.5 -216t-71 -115t-27.5 -46q-20 -35 -57 -35 t-57.5 22.5t-20.5 42z" />
<glyph unicode="_" horiz-adv-x="1357" d="M106 -233q0 29 19.5 48t48.5 19h1010q28 0 47.5 -19.5t19.5 -47.5q0 -29 -19.5 -48.5t-47.5 -19.5h-1010q-29 0 -48.5 19.5t-19.5 48.5z" />
<glyph unicode="`" horiz-adv-x="503" d="M141 1403q0 76 82 76q41 0 62 -39l104 -185q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51z" />
<glyph unicode="a" horiz-adv-x="1136" d="M78 287q0 152 115 237q125 94 356 94h248v17q0 137 -67.5 195.5t-207.5 58.5q-119 0 -272 -49q-16 -4 -36.5 -4t-43.5 22q-23 21 -23 64q0 46 54 69q172 74 356 74q209 0 324 -127q102 -111 102 -276v-580q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5v53 q-55 -57 -148.5 -106t-236.5 -49q-141 0 -236 81q-98 86 -98 226zM264 307q0 -164 217 -164q96 0 179 47.5t137 106.5v172h-224q-145 0 -227 -37t-82 -125z" />
<glyph unicode="b" horiz-adv-x="1292" d="M162 82v1329q0 41 26.5 67.5t66.5 26.5t66.5 -26.5t26.5 -67.5v-502q152 156 350.5 156t339.5 -141q156 -156 156 -402q0 -250 -156 -403q-143 -139 -340.5 -139t-349.5 155v-53q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5zM348 307q138 -153 322 -153h2 q233 0 311 208q25 70 25 160q0 176 -95 277q-90 92 -241 92q-184 0 -324 -154v-430z" />
<glyph unicode="c" horiz-adv-x="1097" d="M94 522q0 248 164 400q156 143 383 143q123 0 202 -32t126 -62.5t47 -67.5t-22.5 -63.5t-45.5 -26.5q-35 0 -71 21q-104 57 -220 57q-180 0 -278 -101.5t-98 -267.5t98 -267t278 -101q115 0 220 57q37 20 65.5 20t51 -26.5t22.5 -63.5t-47 -67q-145 -94 -328 -94 q-227 0 -383 143q-164 152 -164 399z" />
<glyph unicode="d" horiz-adv-x="1292" d="M98 522q0 246 156 402q141 141 340 141t350 -156v502q0 41 26.5 67.5t66.5 26.5t66.5 -26.5t26.5 -67.5v-1329q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5v53q-151 -155 -347 -155h-3q-197 0 -340 139q-156 154 -156 403zM285 522q0 -176 96 -276 q88 -92 240 -92q184 0 323 153v430q-139 154 -323 154q-233 0 -310 -209q-26 -68 -26 -154v-6z" />
<glyph unicode="e" horiz-adv-x="1167" d="M100 522q0 225 117 375q131 168 373 168q229 0 362 -156q125 -145 125 -360q0 -39 -25.5 -65.5t-68.5 -26.5h-694q16 -140 104 -221.5t246 -81.5t264 53q37 18 63.5 18t51 -22.5t24.5 -50.5q0 -53 -44 -77t-78.5 -40.5t-75.5 -28.5q-89 -26 -204 -26h-9 q-252 0 -391.5 141t-139.5 401zM289 608h604q-8 145 -107 230q-81 69 -196 69q-182 0 -266 -162q-31 -57 -35 -137z" />
<glyph unicode="f" horiz-adv-x="778" d="M39 965q0 35 22.5 57t57.5 22h121v119q0 178 100 275q82 78 207 78q96 0 178 -41q47 -27 47 -64t-21.5 -61.5t-54.5 -24.5l-102 12q-78 0 -123 -35.5t-45 -144.5v-113h256q35 0 57.5 -22.5t22.5 -56.5q0 -35 -22.5 -57.5t-57.5 -22.5h-256v-803q0 -39 -26.5 -65.5 t-66.5 -26.5t-66.5 26.5t-26.5 65.5v803h-121q-35 0 -57.5 22.5t-22.5 57.5z" />
<glyph unicode="g" horiz-adv-x="1292" d="M98 532q0 252 162 402q139 131 339 131t345 -156v52q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-916q0 -260 -124.5 -398.5t-356.5 -138.5q-246 0 -438 107q-37 23 -37 62.5t23.5 66.5t49 27t46.5 -9q154 -77 333 -77h5q313 0 313 333v138q-145 -156 -350 -156 q-195 0 -334 131q-162 150 -162 401zM285 532q0 -264 211 -339q63 -23 153 -23q150 0 295 152v421q-57 59 -131 105.5t-164 46.5t-153.5 -22.5t-110.5 -65.5q-100 -94 -100 -275z" />
<glyph unicode="h" horiz-adv-x="1247" d="M162 82v1329q0 41 26.5 67.5t65.5 26.5t66.5 -26.5t27.5 -67.5v-534q82 86 141 120q117 68 222.5 68t173 -32t114.5 -87q96 -113 97 -282v-582q0 -39 -28 -65.5t-67 -26.5t-65.5 26.5t-26.5 65.5v561q0 117 -52 183.5t-173 66.5q-180 0 -336 -197v-614q0 -39 -27.5 -65.5 t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="i" horiz-adv-x="526" d="M154 1362v8q0 43 30.5 74t73.5 31h12q43 0 74 -31t31 -74v-8q0 -43 -31 -74t-74 -31h-12q-43 0 -73.5 31t-30.5 74zM170 82v879q0 40 26.5 67t66.5 27t66.5 -27t26.5 -67v-879q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5z" />
<glyph unicode="j" horiz-adv-x="526" d="M-94 -399q0 31 21.5 54t56.5 23l61 -6q63 0 84 17q41 27 41 104v1165q0 41 26.5 68t66.5 27t66.5 -27t26.5 -68v-1169q0 -199 -143 -260q-51 -20 -125 -21q-74 0 -128 17.5t-54 75.5zM154 1362v8q0 43 30.5 74t73.5 31h12q43 0 74 -31t31 -74v-8q0 -43 -31 -74t-74 -31 h-12q-43 0 -73.5 31t-30.5 74z" />
<glyph unicode="k" horiz-adv-x="1118" d="M162 82v1329q0 39 26.5 66.5t65.5 27.5t66.5 -27.5t27.5 -66.5v-838l488 451q33 27 73.5 27t59 -30t18.5 -50q0 -45 -35 -80l-295 -264l371 -492q21 -28 21 -63q0 -34 -27 -58t-59 -24q-49 0 -84 39l-353 479l-178 -162v-264q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5 t-26.5 65.5z" />
<glyph unicode="l" horiz-adv-x="542" d="M178 82v1329q0 41 26.5 67.5t66.5 26.5t67 -26.5t27 -67.5v-1329q0 -39 -27 -65.5t-67 -26.5t-66.5 26.5t-26.5 65.5z" />
<glyph unicode="m" horiz-adv-x="1882" d="M162 82v879q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-84q125 188 348 188q111 0 201 -66q74 -51 113 -145q57 90 112 127q117 84 264 84q166 0 261 -127q82 -109 82 -266v-590q0 -39 -28 -65.5t-67 -26.5t-65.5 26.5t-26.5 65.5v553q0 129 -46 193.5t-163 64.5 q-180 0 -295 -197v-614q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5v553q0 129 -46 193.5t-163 64.5q-180 0 -295 -197v-614q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="n" horiz-adv-x="1247" d="M162 82v879q0 41 26.5 67.5t65.5 26.5t66.5 -27t27.5 -67v-84q82 86 141 120q117 68 222.5 68t173 -32t114.5 -87q96 -113 97 -282v-582q0 -39 -28 -65.5t-67 -26.5t-65.5 26.5t-26.5 65.5v561q0 117 -52 183.5t-173 66.5q-180 0 -336 -197v-614q0 -39 -27.5 -65.5 t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="o" horiz-adv-x="1245" d="M98 522q0 244 144 393q141 150 381 150q238 0 381 -150q143 -152 143 -393q0 -242 -143 -393q-142 -149 -378 -149h-3q-240 0 -381 149q-143 150 -144 393zM316 350q30 -72 78 -116t108.5 -64t119 -20t119 20t109.5 63q111 100 111 291q0 184 -111 287q-92 84 -227 84 q-221 0 -308 -199q-31 -71 -31 -170q1 -103 32 -176z" />
<glyph unicode="p" horiz-adv-x="1292" d="M162 -387v1350q0 39 26.5 65.5t66.5 26.5t66.5 -27t26.5 -65v-54q152 156 350.5 156t339.5 -141q156 -156 156 -402q0 -250 -156 -403q-143 -139 -340.5 -139t-349.5 155v-522q0 -41 -26.5 -67.5t-66.5 -26.5t-66.5 26.5t-26.5 67.5zM348 307q138 -153 322 -153h2 q233 0 311 208q25 70 25 160q0 176 -95 277q-90 92 -241 92q-184 0 -324 -154v-430z" />
<glyph unicode="q" horiz-adv-x="1292" d="M98 522q0 246 156 402q141 141 340 141t350 -156v54q0 39 26.5 65.5t66.5 26.5t66.5 -27t26.5 -65v-1350q0 -41 -26.5 -67.5t-66.5 -26.5t-66.5 26.5t-26.5 67.5v522q-151 -155 -347 -155h-3q-197 0 -340 139q-156 154 -156 403zM285 522q0 -176 96 -276q88 -92 240 -92 q184 0 323 153v430q-139 154 -323 154q-233 0 -310 -209q-26 -68 -26 -154v-6z" />
<glyph unicode="r" horiz-adv-x="792" d="M162 82v879q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-89q143 193 322 193h10q39 0 64.5 -26.5t25.5 -66.5t-26.5 -63.5t-67.5 -23.5h-10q-102 0 -183.5 -50.5t-134.5 -129.5v-623q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="s" horiz-adv-x="983" d="M98 133.5q0 36.5 22.5 62t49.5 25.5t63 -16q120 -53 261 -53q227 0 227 145q0 45 -45 68.5t-113.5 42t-148.5 38t-148.5 54.5t-114 91t-45.5 138t25 141.5t74 102.5q109 92 301 92q154 0 297 -51q29 -10 44 -33t15 -50.5t-23.5 -52t-46 -24.5t-55.5 10q-98 31 -214.5 31 t-177 -41t-60.5 -98.5t46 -85t113.5 -46t148.5 -37t148 -50.5q160 -82 160 -254q0 -131 -98 -213q-106 -90 -297 -90q-207 0 -373 92q-35 25 -35 61.5z" />
<glyph unicode="t" horiz-adv-x="786" d="M45 924q0 18 18 34l279 273q18 18 34.5 18t28 -12t11.5 -29v-164h231q35 0 57.5 -22.5t22.5 -56.5q0 -35 -22.5 -57.5t-57.5 -22.5h-231v-619q0 -78 47 -106q27 -16 81 -16q3 0 5 -1q51 -1 73 9q24 10 51 10t49.5 -20.5t22.5 -61.5t-40 -60q-87 -41 -149 -41l-2 1 q-62 0 -113.5 6t-98.5 34q-113 66 -113 230v635h-145q-39 0 -39 39z" />
<glyph unicode="u" horiz-adv-x="1249" d="M154 381v582q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-562q0 -117 52 -183t173 -66q180 0 336 196v615q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-879q0 -41 -26.5 -67.5t-65.5 -26.5t-66.5 26.5t-27.5 67.5v84q-82 -86 -141 -121q-116 -68 -221 -68l-1 1 q-106 0 -173.5 31.5t-114.5 86.5q-96 113 -96 283z" />
<glyph unicode="v" horiz-adv-x="1085" d="M53 971q0 28 26.5 56t59.5 28q66 0 90 -54l314 -794l313 794q25 53 90 54q33 0 59.5 -28t26.5 -52q0 -33 -10 -57l-356 -852q-14 -31 -44 -53.5t-69 -22.5h-21q-39 0 -68.5 22.5t-43.5 53.5l-357 852q-10 25 -10 53z" />
<glyph unicode="w" horiz-adv-x="1607" d="M51 973.5q0 27.5 26.5 54.5t61.5 27q72 0 88 -56l240 -788l238 770q10 33 37.5 53.5t62.5 20.5t62.5 -20.5t37.5 -53.5l238 -770l239 788q16 55 88 56q35 0 62 -27t27 -54.5t-7 -45.5l-290 -852q-12 -35 -45 -60.5t-74 -25.5t-74 25.5t-47 62.5l-217 678l-217 -678 q-14 -37 -47 -62.5t-74 -25.5t-74 25.5t-45 60.5l-291 852q-6 18 -6 45.5z" />
<glyph unicode="x" horiz-adv-x="1110" d="M104 969q0 37 27 61.5t71 24.5t68 -35l287 -354l285 354q25 35 68.5 35t70.5 -25.5t27 -59.5t-21 -61l-323 -381l319 -385q23 -27 23 -60.5t-24 -63t-70 -29.5t-72 37l-291 348l-275 -344q-31 -41 -75.5 -41t-68.5 25.5t-24 62.5t21 63l317 375l-317 387q-23 29 -23 66z " />
<glyph unicode="y" horiz-adv-x="1091" d="M49 970.5q0 28.5 26.5 56.5t59.5 28q68 0 92 -58l316 -782l321 778q27 61 86 62q39 0 65.5 -28t26.5 -57.5t-6 -43.5l-559 -1344q-27 -63 -86 -63q-37 0 -62.5 26.5t-25.5 53t6 43.5l133 342l-385 938q-8 20 -8 48.5z" />
<glyph unicode="z" horiz-adv-x="1021" d="M80 105.5q0 50.5 33 87.5l589 677h-518q-37 0 -61.5 25t-24.5 61q0 37 24.5 62.5t61.5 25.5h621q53 0 86 -31.5t33 -81.5t-29 -85l-573 -672h526q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-639q-53 0 -91 27.5t-38 78z" />
<glyph unicode="{" horiz-adv-x="704" d="M6 629q0 70 68.5 82t106.5 29.5t57 37.5q27 33 26 99v432q0 201 174 235q51 10 113 10h24q29 0 50.5 -21.5t21.5 -49.5q0 -29 -21.5 -50.5t-50.5 -21.5h-30q-111 0 -119 -59q-2 -14 -2 -27v-455q0 -173 -184 -241q184 -68 184 -242v-455q0 -39 14 -53q29 -33 107 -33h30 q29 0 50.5 -21.5t21.5 -49.5q0 -29 -21.5 -50.5t-50.5 -21.5h-24q-137 0 -205 47q-82 59 -82 199v432q0 66 -22 92q-43 51 -166 74q-70 12 -70 82z" />
<glyph unicode="|" horiz-adv-x="518" d="M176 -319v1753q0 35 23.5 59.5t58.5 24.5t59.5 -25t24.5 -59v-1753q0 -35 -24.5 -58.5t-59.5 -23.5t-58.5 23.5t-23.5 58.5z" />
<glyph unicode="}" horiz-adv-x="704" d="M57 -225q0 29 21.5 50t50.5 21h31q111 0 119 60q2 14 2 26v455q0 174 184 242q-184 68 -184 241v455q0 39 -15 53q-29 33 -106 33h-31q-29 0 -50.5 21.5t-21.5 50.5t21.5 50t50.5 21h25q137 0 204 -47q82 -59 82 -198v-432q0 -66 23 -93q43 -51 166 -73q70 -12 69 -82 q0 -70 -68.5 -82t-106.5 -29.5t-56 -38.5q-27 -33 -27 -98v-432q0 -201 -174 -236q-51 -10 -112 -10h-25q-29 0 -50.5 21.5t-21.5 50.5z" />
<glyph unicode="~" horiz-adv-x="1306" d="M152 494.5q0 35.5 20.5 84t56.5 85.5q82 88 173.5 88t160 -25t129 -53.5t115.5 -53t102 -24.5t68.5 9t36 24.5t24 38t24.5 52.5t44 30q57 0 57 -48q0 -8 -2 -16q-25 -135 -95.5 -199.5t-158.5 -64.5t-155.5 24.5t-128 53t-115.5 53.5t-115 25q-106 0 -139 -111 q-10 -39 -41 -39t-46 15.5t-15 51z" />
<glyph unicode="&#xa1;" horiz-adv-x="530" d="M148 930.5q0 -50.5 36 -86.5t86 -36t86 36t36 86.5t-36 86t-86 35.5t-86 -35.5t-36 -86zM162 -86v-203q0 -45 31 -76t76 -31t74.5 31t29.5 76v203l-37 675q-4 60 -67 60q-66 0 -70 -60z" />
<glyph unicode="&#xa2;" horiz-adv-x="1128" d="M90 522q0 248 164 400q76 69 175 106t182 37t132 -8l60 174q14 39 48 39t49 -18.5t15 -34t-4 -27.5l-55 -164q63 -23 115 -59q41 -27 41 -64t-22.5 -63.5t-45.5 -26.5q-35 0 -55 12l-88 41l-242 -704q39 -8 106.5 -8t119 18t83.5 37q43 23 71 22.5t50.5 -27t22.5 -63.5 t-47 -67q-145 -94 -328 -94q-66 0 -135 16l-72 -211q-12 -37 -46 -37t-50.5 17.5t-16.5 31.5q0 23 7 35l67 199q-133 59 -217 184t-84 307zM276 522q0 -229 175 -321l235 688q-8 2 -16 2h-17q-180 0 -278.5 -101.5t-98.5 -267.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1236" d="M80 674q0 20 12 33.5t33 13.5h197q-23 70 -44.5 142.5t-21.5 168.5t36 174t101 133q137 111 361 111q154 0 295 -59q61 -25 61 -82q0 -35 -23.5 -60.5t-48.5 -25.5q-37 0 -71 12q-98 35 -199.5 35t-163 -20.5t-96.5 -53.5q-66 -61 -66 -150.5t25 -167t47 -157.5h363 q20 0 33.5 -13.5t13.5 -33.5t-13.5 -32.5t-33.5 -12.5h-342q8 -41 8 -94q0 -158 -86 -293l51 2q78 0 166 -29t135 -42t92 -13q72 0 107 12q55 18 77.5 18t47 -23.5t24.5 -64.5q0 -70 -149 -108q-55 -14 -118 -14t-110 14t-92 31q-115 47 -192 47q-127 0 -232 -70 q-23 -16 -50.5 -16t-50 21.5t-22.5 55t31 61.5t64.5 71t60.5 96q61 119 61 231q0 53 -12 107h-221q-45 0 -45 45z" />
<glyph unicode="&#xa4;" horiz-adv-x="1368" d="M135 242q0 31 23 53l133 133q-92 131 -92 287t92 286l-133 134q-23 23 -23 53q0 31 20.5 53.5t45.5 22.5q37 0 63 -23l135 -133q127 92 285 92t285 -92l135 133q27 23 57.5 23t51 -22.5t20.5 -53.5t-23 -53l-133 -134q92 -131 92 -286q0 -156 -92 -287l133 -133 q23 -23 23 -53q0 -31 -20.5 -53.5t-45.5 -22.5q-37 0 -63 22l-135 134q-127 -93 -285 -93t-285 93l-135 -134q-27 -23 -57.5 -22.5t-51 23t-20.5 53.5zM365 715q0 -131 94 -225.5t225 -94.5t225.5 94.5t94.5 225.5t-94.5 225t-225.5 94t-225 -94t-94 -225z" />
<glyph unicode="&#xa5;" horiz-adv-x="1347" d="M133.5 1370.5q-0.5 34.5 28 61t66.5 26.5t57.5 -16.5t33.5 -40.5l355 -621l354 621q33 57 81 57t76.5 -26.5t28.5 -61.5t-16 -61l-344 -561h201q33 0 54 -23t21 -55q0 -33 -20 -54.5t-55 -21.5h-289v-186h289q33 0 54 -23t21 -55q0 -33 -20 -54.5t-55 -21.5h-289v-174 q0 -39 -27.5 -65.5t-64.5 -26.5t-64.5 26.5t-27.5 65.5v174h-289q-35 0 -55.5 21.5t-20.5 54.5t21.5 55.5t54.5 22.5h289v186h-289q-35 0 -55.5 21.5t-20.5 54.5t21.5 55.5t54.5 22.5h201l-344 561q-16 27 -16.5 61.5z" />
<glyph unicode="&#xa7;" horiz-adv-x="1083" d="M96 -57.5q0 41.5 22.5 65t45.5 23.5q25 0 55 -13q115 -57 285 -57q231 0 231 143q0 49 -46 83t-114.5 63t-148.5 58.5t-147 72.5q-162 98 -162 260q0 152 125 223q36 23 75 35q-131 94 -131 248q0 131 101 213q104 90 285.5 90t316.5 -55q55 -25 55 -80 q0 -33 -25.5 -57.5t-50 -24.5t-57.5 12q-84 33 -217 33q-231 0 -232 -143q0 -51 46.5 -84t115 -61.5t148.5 -58.5t147 -73q162 -100 162 -260q0 -152 -127 -225q-37 -23 -76 -35q133 -92 133 -246q0 -131 -100 -215q-104 -88 -295 -88q-240 0 -387 92q-33 20 -33 61.5z M289 653q0 -88 119 -147q53 -27 117.5 -50.5t129.5 -49.5q154 39 154 180q0 86 -121 147q-51 27 -118 49.5t-130 51.5q-152 -39 -151 -181z" />
<glyph unicode="&#xa8;" horiz-adv-x="819" d="M129 1282q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM475 1282q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1677" d="M104 715q0 152 57.5 285t158 233t234.5 157.5t286 57.5t286 -57.5t233 -157.5t157.5 -233.5t58.5 -284.5q0 -152 -58.5 -285t-157.5 -233.5t-233 -157.5t-286 -57t-286 57t-234.5 157.5t-158 233.5t-57.5 285zM211 715q0 -258 184.5 -442.5t442.5 -184.5q262 0 446 184.5 t184 442.5t-184 442t-444 184q-131 0 -245 -49t-200 -135q-184 -184 -184 -442zM457 715q0 188 109.5 301.5t285.5 113.5q123 0 210 -57t136 -158q6 -12 6 -33.5t-18.5 -40t-41 -18.5t-37.5 8.5t-41 52.5t-79 83t-127 39t-122 -24t-79 -64q-66 -80 -65 -203q0 -123 65 -203 q68 -88 193 -88q143 0 223 137q27 45 59.5 45t51 -18.5t18.5 -40t-6 -33.5q-49 -100 -136 -157.5t-192.5 -57.5t-178.5 29.5t-126 85.5q-109 113 -108 301z" />
<glyph unicode="&#xaa;" horiz-adv-x="993" d="M135 526q0 23 17.5 40.5t40.5 17.5h579q23 0 40 -17.5t17 -40.5t-17 -40t-40 -17h-579q-23 0 -40.5 17.5t-17.5 39.5zM166 914.5q0 101.5 81 160t249 58.5h155v4q0 94 -44 134t-115 40q-113 0 -201 -33q-12 -4 -30.5 -4t-37 19.5t-18.5 41.5q0 47 49 66q125 49 224.5 49 t155.5 -24.5t93 -65.5q72 -76 72 -195v-391q0 -31 -21.5 -52.5t-52.5 -21.5t-52.5 21.5t-21.5 52.5v19q-88 -98 -248 -99q-96 0 -165 60q-72 59 -72 160.5zM313 926q0 -41 30 -70t99 -29q123 0 209 97v98h-139q-199 0 -199 -96z" />
<glyph unicode="&#xab;" horiz-adv-x="1198" d="M111 522q0 51 36 88l336 371q18 23 55 23t60 -23t23 -56.5t-19 -54.5l-328 -348l328 -348q18 -23 18.5 -55.5t-22.5 -55t-59.5 -22.5t-55.5 22l-336 371q-37 37 -36 88zM537 522q0 51 36 88l336 371q18 23 55 23t60 -23t23 -56.5t-19 -54.5l-328 -348l328 -348 q18 -23 18.5 -55.5t-22.5 -55t-59.5 -22.5t-55.5 22l-336 371q-37 37 -36 88z" />
<glyph unicode="&#xac;" horiz-adv-x="1185" d="M150 711q0 31 21.5 52t51.5 21h707q35 0 60.5 -25.5t25.5 -60.5v-278q0 -33 -22.5 -54.5t-55.5 -21.5t-54.5 21.5t-21.5 54.5v217h-639q-31 0 -52 21.5t-21 52.5z" />
<glyph unicode="&#xad;" horiz-adv-x="835" d="M141 573q0 35 23.5 59.5t58.5 24.5h387q35 0 59.5 -24.5t24.5 -59.5t-24.5 -58t-59.5 -23h-387q-35 0 -58.5 23.5t-23.5 57.5z" />
<glyph unicode="&#xae;" horiz-adv-x="1677" d="M104 715q0 152 57.5 286t158 233t234.5 157.5t286 58.5t286 -58.5t233 -157.5t157.5 -233.5t58.5 -285.5q0 -152 -58.5 -286t-157.5 -234.5t-233.5 -157.5t-285.5 -57q-152 0 -286 57t-234.5 157.5t-158 234.5t-57.5 286zM211 717q0 -262 184.5 -446.5t444.5 -184.5 q129 0 243.5 49t200.5 135q184 184 184 447q0 258 -184 442t-446 184q-258 0 -442.5 -184t-184.5 -442zM535 365v663q0 39 26.5 66.5t65.5 27.5h245q168 0 245 -70.5t77 -161.5t-46 -140.5t-106 -71.5q49 -16 95.5 -75.5t46.5 -180.5v-55q0 -29 -20.5 -49.5t-49.5 -20.5 t-49.5 20.5t-20.5 49.5v36q0 88 -16 127q-37 82 -133 82h-223v-247q0 -29 -20.5 -48.5t-49.5 -19.5t-48 19.5t-19 48.5zM672 733h194q86 0 139.5 35t53.5 99.5t-42 100.5t-155 36h-190v-271z" />
<glyph unicode="&#xaf;" horiz-adv-x="638" d="M16 1266q0 27 17.5 44t44.5 17h479q27 0 44 -17.5t17 -43.5q0 -27 -17 -44.5t-44 -17.5h-479q-27 0 -44.5 17.5t-17.5 44.5z" />
<glyph unicode="&#xb0;" horiz-adv-x="774" d="M106 1169q0 113 82 199q80 82 199 82q117 0 199 -82t82 -199q0 -119 -82 -198q-86 -82 -199 -82q-115 0 -198 83t-83 197zM213 1169q0 -72 51 -123t123 -51t123 51.5t51 122.5q0 72 -51 123t-123 51t-123 -51t-51 -123z" />
<glyph unicode="&#xb1;" horiz-adv-x="1241" d="M127 78q0 35 21.5 57.5t56.5 22.5h829q35 0 57.5 -22.5t22.5 -57.5t-22.5 -56.5t-57.5 -21.5h-829q-35 0 -56.5 21.5t-21.5 56.5zM127 778q0 35 21.5 57.5t56.5 22.5h336v324q0 35 22.5 57.5t57.5 22.5t57 -22.5t22 -57.5v-324h334q35 0 57.5 -22.5t22.5 -57.5 t-22.5 -56.5t-57.5 -21.5h-334v-323q0 -35 -22.5 -57.5t-56.5 -22.5q-35 0 -57.5 22.5t-22.5 57.5v323h-336q-35 0 -56.5 21.5t-21.5 56.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="757" d="M78 1019q0 44 33.5 91t84 87t110 77t109.5 71.5t83.5 70.5t33.5 82t-35.5 82t-111.5 36q-121 0 -207 -45q-12 -6 -31.5 -6t-36 15t-16.5 46t37 53q104 61 241.5 61.5t213.5 -68t76 -171.5q0 -141 -195 -272q-51 -37 -100 -70q-121 -78 -148 -115h389q27 0 44.5 -17 t17.5 -44t-17.5 -43t-44.5 -16h-450q-35 0 -57.5 25.5t-22.5 69.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="757" d="M74 1012.5q0 29.5 18.5 47t32.5 17.5q18 0 41 -8q104 -31 176 -31q188 0 188 107q0 127 -229 127h-22q-27 0 -44.5 17.5t-17.5 43.5q0 27 17.5 44.5t44.5 17.5h10q215 0 215 118q0 78 -105 97q-29 6 -71 6q-82 0 -160 -39q-12 -6 -30.5 -6t-36 15.5t-17.5 44t43 52.5 q111 57 243 57.5t196.5 -63t64.5 -149.5t-42 -129t-85 -60q156 -47 155 -198q0 -156 -159 -211q-57 -18 -140 -19q-160 0 -251 52q-35 20 -35 49.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="491" d="M98 1210q0 31 15 45l104 185q20 39 62 39q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xb5;" horiz-adv-x="1236" d="M172 -354v1317q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-562q0 -117 52.5 -183t173.5 -66q180 0 336 196v615q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-879q0 -41 -26.5 -67.5t-65.5 -26.5t-66.5 26.5t-27.5 67.5v84q-86 -92 -142 -123q-109 -66 -221.5 -65.5 t-198.5 55.5v-389q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1499" d="M80 903q0 219 153.5 373t372.5 154h721q35 0 58.5 -24t23.5 -58q0 -35 -23.5 -57.5t-58.5 -22.5h-94v-1645q0 -35 -23.5 -58.5t-58.5 -23.5t-57.5 23.5t-22.5 58.5v1645h-229v-1645q0 -35 -23.5 -58.5t-58.5 -23.5t-57.5 23.5t-22.5 58.5v754h-74q-219 0 -372.5 153.5 t-153.5 372.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="532" d="M141 578v12q0 47 34 81t81 34h20q48 0 81.5 -34t33.5 -81v-12q0 -47 -33.5 -81t-81.5 -34h-20q-47 0 -81 33.5t-34 81.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="628" d="M92 -365q0 41 39 41q8 0 21 -6q63 -35 120 -35q117 0 117 95q0 31 -21.5 47t-48.5 16q-49 0 -73 -12q-33 -16 -44.5 -16.5t-22.5 9t-11 25t8 27.5l131 209h86l-94 -164q33 8 86 8t97 -39t44 -104q0 -188 -227 -189q-98 0 -184 54q-23 10 -23 34z" />
<glyph unicode="&#xb9;" horiz-adv-x="757" d="M160 1600.5q0 31.5 39 52.5l147 76q20 10 51 10t54.5 -25.5t23.5 -62.5v-607h103q26 0 43.5 -17t17.5 -44t-17.5 -43t-43.5 -16h-357q-27 0 -43 16t-16 43t16.5 44t42.5 17h123v543l-92 -45q-16 -8 -35.5 -8t-38 17.5t-18.5 49z" />
<glyph unicode="&#xba;" horiz-adv-x="1052" d="M143 526q0 23 17.5 40.5t40.5 17.5h651q23 0 40 -17.5t17 -40.5t-17.5 -40t-39.5 -17h-651q-23 0 -40.5 17.5t-17.5 39.5zM162 1073q0 170 100 273q100 104 262 104q84 0 151.5 -27.5t115.5 -76.5q100 -102 100 -273q0 -170 -100 -274t-264.5 -104.5t-264.5 104.5 q-100 104 -100 274zM309 1075q0 -125 72 -192q57 -58 145 -58q137 0 197 136q20 47 20 114q0 121 -71 191q-57 55 -146 55q-86 0 -145 -55q-72 -68 -72 -191z" />
<glyph unicode="&#xbb;" horiz-adv-x="1198" d="M152 119q0 33 18 55l328 348l-328 348q-18 20 -18 54t22.5 57t59 23t55.5 -23l336 -371q37 -37 37 -88t-37 -88l-336 -371q-18 -23 -55 -22.5t-59.5 23t-22.5 55.5zM578 119q0 33 18 55l328 348l-328 348q-18 20 -18 54t22.5 57t59 23t55.5 -23l336 -371q37 -37 36 -88 q0 -51 -36 -88l-336 -371q-18 -23 -55 -22.5t-59.5 23t-22.5 55.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="1925" d="M174 1311.5q0 31.5 39 52.5l147 76q20 10 51 10t54.5 -25.5t23.5 -62.5v-606h103q27 0 44 -17.5t17 -44.5t-17.5 -43t-43.5 -16h-356q-27 0 -43.5 16.5t-16.5 42.5q0 27 16.5 44.5t43.5 17.5h122v542l-92 -45q-16 -8 -35.5 -8t-38 17.5t-18.5 49zM467 13.5 q0 23.5 10 43.5l848 1397q18 29 52 29t54.5 -19.5t20.5 -43t-10 -44.5l-848 -1396q-18 -29 -52 -29t-54.5 19.5t-20.5 43zM1104 280.5q0 32.5 22 61.5l338 426q37 43 74 43h16q37 0 61.5 -25.5t24.5 -60.5v-408h74q27 0 44.5 -17t17.5 -44t-17.5 -43t-44.5 -16h-74v-142 q0 -29 -18 -47t-47 -18t-47.5 18.5t-18.5 46.5v142h-319q-35 0 -60.5 25.5t-25.5 58zM1249 317h260v332z" />
<glyph unicode="&#xbd;" horiz-adv-x="1925" d="M162 1311.5q0 31.5 39 52.5l147 76q20 10 51 10t54.5 -25.5t23.5 -62.5v-606h103q27 0 44 -17.5t17 -44.5t-17.5 -43t-43.5 -16h-357q-27 0 -43 16.5t-16 42.5q0 27 16.5 44.5t42.5 17.5h123v542l-92 -45q-16 -8 -35.5 -8t-38 17.5t-18.5 49zM455 13.5q0 23.5 10 43.5 l848 1397q18 29 52 29t54.5 -19.5t20.5 -43t-10 -44.5l-848 -1396q-18 -29 -52 -29t-54.5 19.5t-20.5 43zM1180 95q0 44 33.5 91t84 87t109.5 77t109.5 72t84 70.5t33.5 82t-35.5 82t-111.5 35.5q-121 0 -207 -45q-12 -6 -31.5 -6t-36 15.5t-16.5 46t37 53.5 q104 61 241.5 61t213 -68.5t75.5 -170.5q0 -141 -194 -275q-51 -35 -101 -65q-121 -80 -147 -117h389q27 0 44.5 -17.5t17.5 -44.5t-17.5 -43t-44.5 -16h-450q-35 0 -57.5 25.5t-22.5 69.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="1925" d="M129 724q0 30 18.5 47t32.5 17q18 0 41 -6q104 -33 176 -32q188 0 189 106q0 127 -230 127h-22q-27 0 -44.5 17.5t-17.5 43.5q0 27 17.5 44.5t44.5 17.5h10q215 0 215 119q0 78 -104 98q-29 4 -70 4q-84 0 -162 -39q-12 -6 -30.5 -6t-36 15.5t-17.5 44t45 51.5 q109 59 241 59t196.5 -63.5t64.5 -149.5t-42 -129t-85 -59q156 -47 156 -199q0 -156 -160 -209q-57 -20 -139 -20q-160 0 -252 51q-35 20 -35 50zM496 13.5q0 23.5 10 43.5l848 1397q18 29 52 29t54.5 -19.5t20.5 -43t-11 -44.5l-847 -1396q-18 -29 -52 -29t-54.5 19.5 t-20.5 43zM1133 280.5q0 32.5 22 61.5l338 426q37 43 74 43h16q37 0 61.5 -25.5t24.5 -60.5v-408h74q27 0 44 -17t17 -44t-17.5 -43t-43.5 -16h-74v-142q0 -29 -18.5 -47t-46.5 -18q-29 0 -47.5 18.5t-18.5 46.5v142h-319q-35 0 -60.5 25.5t-25.5 58zM1278 317h260v332z" />
<glyph unicode="&#xbf;" horiz-adv-x="999" d="M82 -19q0 184 193 326q113 84 162 164q29 47 28.5 97t27.5 72.5t64.5 22.5t62.5 -23.5t26 -56.5q0 -203 -191 -350q-123 -98 -155.5 -144.5t-32.5 -97.5q0 -223 292 -223q160 0 263 68q27 16 56.5 16t53 -23.5t23.5 -54.5q0 -51 -51 -82q-160 -98 -361 -98 q-211 0 -334 104q-127 106 -127 283zM437 930.5q0 50.5 35.5 86t86 35.5t86 -35.5t35.5 -86t-35.5 -86.5t-86 -36t-86 36t-35.5 86.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1427" d="M53 70.5q0 27.5 10 52.5l523 1233q37 84 119 84h20q82 0 119 -84l522 -1233q10 -25 10 -52.5t-24.5 -54t-77.5 -26.5t-78 57l-133 320h-696l-134 -320q-25 -57 -78 -57t-77.5 26.5t-24.5 54zM438 537h553l-276 665zM526 1755q0 76 82 76q41 0 62 -39l104 -185 q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51z" />
<glyph unicode="&#xc1;" horiz-adv-x="1427" d="M53 70.5q0 27.5 10 52.5l523 1233q37 84 119 84h20q82 0 119 -84l522 -1233q10 -25 10 -52.5t-24.5 -54t-77.5 -26.5t-78 57l-133 320h-696l-134 -320q-25 -57 -78 -57t-77.5 26.5t-24.5 54zM438 537h553l-276 665zM639 1562q0 31 15 45l104 185q20 39 62 39q82 0 81 -76 q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xc2;" horiz-adv-x="1427" d="M53 70.5q0 27.5 10 52.5l523 1233q37 84 119 84h20q82 0 119 -84l522 -1233q10 -25 10 -52.5t-24.5 -54t-77.5 -26.5t-78 57l-133 320h-696l-134 -320q-25 -57 -78 -57t-77.5 26.5t-24.5 54zM438 537h553l-276 665zM438 1559.5v23.5t17 29l139 143q41 41 82 41h78 q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18t-17.5 30z" />
<glyph unicode="&#xc3;" horiz-adv-x="1427" d="M53 70.5q0 27.5 10 52.5l523 1233q37 84 119 84h20q82 0 119 -84l522 -1233q10 -25 10 -52.5t-24.5 -54t-77.5 -26.5t-78 57l-133 320h-696l-134 -320q-25 -57 -78 -57t-77.5 26.5t-24.5 54zM387 1573.5q0 25.5 12 59.5t35 62q47 68 125 68q45 0 84 -15.5t76.5 -32.5 t75.5 -32.5t75 -15.5t53.5 14t24.5 35q18 49 53 49q43 0 47 -45q0 -72 -47 -131q-53 -63 -125 -63q-61 0 -131 33.5t-107.5 49t-72.5 15.5q-55 0 -84 -66q-10 -37 -47 -37q-18 0 -32.5 13.5t-14.5 39zM438 537h553l-276 665z" />
<glyph unicode="&#xc4;" horiz-adv-x="1427" d="M53 70.5q0 27.5 10 52.5l523 1233q37 84 119 84h20q82 0 119 -84l522 -1233q10 -25 10 -52.5t-24.5 -54t-77.5 -26.5t-78 57l-133 320h-696l-134 -320q-25 -57 -78 -57t-77.5 26.5t-24.5 54zM438 537h553l-276 665zM438 1634q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5 t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM784 1634q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1427" d="M53 70.5q0 27.5 10 52.5l523 1233q37 84 119 84h20q82 0 119 -84l522 -1233q10 -25 10 -52.5t-24.5 -54t-77.5 -26.5t-78 57l-133 320h-696l-134 -320q-25 -57 -78 -57t-77.5 26.5t-24.5 54zM438 537h553l-276 665zM522 1688q0 80 56.5 136t134.5 56q41 0 76.5 -15 t62.5 -40q55 -57 55 -137t-56.5 -136.5t-136 -56.5t-136 56.5t-56.5 136.5zM614 1688q0 -41 30 -70.5t71 -29.5t70.5 29.5t29.5 70.5t-29.5 70.5t-70.5 29.5t-71 -29.5t-30 -70.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2129" d="M4 70q0 41 21 69l817 1239q37 51 94 52h999q37 0 62.5 -26t25.5 -63t-25.5 -61.5t-62.5 -24.5h-727v-452h654q37 0 62.5 -25.5t25.5 -62.5t-26 -61.5t-62 -24.5h-654v-455h727q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-821q-39 0 -65.5 26.5t-26.5 65.5v275 h-631l-209 -324q-35 -53 -80 -53t-71.5 26.5t-26.5 53.5zM502 537h520v718h-51z" />
<glyph unicode="&#xc7;" horiz-adv-x="1429" d="M115 715q0 338 204 538q199 197 512 197q152 0 261.5 -37t168 -68.5t70.5 -51t12 -49.5t-21.5 -53.5t-51 -23.5t-45.5 9q-82 43 -168 69.5t-214 26.5t-231.5 -43t-173.5 -119q-137 -150 -137 -395q0 -246 137 -396q150 -162 412 -161q121 0 207 26.5t135 52t78.5 25.5 t51 -23.5t21.5 -54.5q0 -55 -45 -78q-94 -53 -201.5 -88.5t-254.5 -37.5l-62 -109q33 8 86.5 8t97.5 -39t44 -104q0 -188 -228 -189q-98 0 -184 54q-23 10 -23 34q0 41 39 41q8 0 21 -6q64 -35 121 -35q117 0 116 95q0 31 -21.5 47t-47.5 16q-49 0 -74 -12 q-33 -16 -44 -16.5t-22.5 9t-11.5 25t8 27.5l99 158q-287 25 -461 217q-180 201 -180 514z" />
<glyph unicode="&#xc8;" horiz-adv-x="1306" d="M199 92v1243q0 39 26.5 67t65.5 28h821q37 0 62.5 -26t25.5 -63t-25.5 -61.5t-62.5 -24.5h-727v-452h653q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-653v-455h727q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-821q-39 0 -65.5 26.5t-26.5 65.5z M516 1755q0 76 82 76q41 0 62 -39l104 -185q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51z" />
<glyph unicode="&#xc9;" horiz-adv-x="1306" d="M199 92v1243q0 39 26.5 67t65.5 28h821q37 0 62.5 -26t25.5 -63t-25.5 -61.5t-62.5 -24.5h-727v-452h653q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-653v-455h727q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-821q-39 0 -65.5 26.5t-26.5 65.5z M618 1562q0 31 15 45l104 185q20 39 62 39q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xca;" horiz-adv-x="1306" d="M199 92v1243q0 39 26.5 67t65.5 28h821q37 0 62.5 -26t25.5 -63t-25.5 -61.5t-62.5 -24.5h-727v-452h653q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-653v-455h727q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-821q-39 0 -65.5 26.5t-26.5 65.5z M438 1559.5v23.5t17 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18t-17.5 30z" />
<glyph unicode="&#xcb;" horiz-adv-x="1306" d="M199 92v1243q0 39 26.5 67t65.5 28h821q37 0 62.5 -26t25.5 -63t-25.5 -61.5t-62.5 -24.5h-727v-452h653q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-653v-455h727q37 0 62.5 -25.5t25.5 -62.5t-25.5 -61.5t-62.5 -24.5h-821q-39 0 -65.5 26.5t-26.5 65.5z M416 1634q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM762 1634q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="583" d="M116 1755q0 76 82 76q41 0 62 -39l104 -185q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51zM199 82v1264q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-1264q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="&#xcd;" horiz-adv-x="583" d="M199 82v1264q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-1264q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5zM207 1562q0 31 15 45l104 185q20 39 62 39q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xce;" horiz-adv-x="583" d="M16 1559.5v23.5t17 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18t-17.5 30zM199 82v1264q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-1264q0 -39 -27.5 -65.5 t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="&#xcf;" horiz-adv-x="583" d="M16 1634q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM199 82v1264q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-1264q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5zM362 1634q0 43 31 73.5t74 30.5t73.5 -30.5 t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1550" d="M35 713q0 29 19.5 49t47.5 20h111v553q0 39 26.5 67t65.5 28h412q342 0 545 -230q90 -100 134 -228t44 -257t-44 -257t-134 -229q-203 -229 -545 -229h-412q-39 0 -65.5 26.5t-26.5 65.5v553h-111q-29 0 -48 19.5t-19 48.5zM399 174h295q254 0 408 150t153.5 391 t-153.5 391q-154 150 -408 149h-295v-473h240q29 0 49.5 -20.5t20.5 -48.5q0 -29 -20.5 -48.5t-49.5 -19.5h-240v-471z" />
<glyph unicode="&#xd1;" horiz-adv-x="1644" d="M199 82v1223q0 55 38.5 95t94.5 40h26q63 0 113 -62l789 -1138v1106q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-1223q0 -55 -40 -94t-95 -39h-25q-68 0 -108 55l-793 1145v-1108q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5zM502 1573.5q0 25.5 12 59.5 t35 62q47 68 125 68q45 0 84 -15.5t76.5 -32.5t75.5 -32.5t75 -15.5t53.5 14t24.5 35q18 49 53 49q43 0 47 -45q0 -72 -47 -131q-53 -63 -125 -63q-61 0 -131 33.5t-107.5 49t-72.5 15.5q-55 0 -84 -66q-10 -37 -47 -37q-18 0 -32.5 13.5t-14.5 39z" />
<glyph unicode="&#xd2;" horiz-adv-x="1660" d="M113 715q4 324 198 528q194 207 520 207q324 0 521 -207q190 -203 198 -528q-8 -326 -198 -529q-197 -207 -521 -206q-326 0 -520 206q-195 205 -198 529zM299 715q0 -244 139 -400q143 -161 393 -161t394 161q139 156 139 400t-139 399q-143 162 -394 162 q-250 0 -393 -162q-139 -156 -139 -399zM659 1755q0 76 82 76q41 0 62 -39l104 -185q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51z" />
<glyph unicode="&#xd3;" horiz-adv-x="1660" d="M113 715q4 324 198 528q194 207 520 207q324 0 521 -207q190 -203 198 -528q-8 -326 -198 -529q-197 -207 -521 -206q-326 0 -520 206q-195 205 -198 529zM299 715q0 -244 139 -400q143 -161 393 -161t394 161q139 156 139 400t-139 399q-143 162 -394 162 q-250 0 -393 -162q-139 -156 -139 -399zM741 1562q0 31 15 45l104 185q20 39 62 39q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xd4;" horiz-adv-x="1660" d="M113 715q4 324 198 528q194 207 520 207q324 0 521 -207q190 -203 198 -528q-8 -326 -198 -529q-197 -207 -521 -206q-326 0 -520 206q-195 205 -198 529zM299 715q0 -244 139 -400q143 -161 393 -161t394 161q139 156 139 400t-139 399q-143 162 -394 162 q-250 0 -393 -162q-139 -156 -139 -399zM555 1559.5v23.5t17 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18t-17.5 30z" />
<glyph unicode="&#xd5;" horiz-adv-x="1660" d="M113 715q4 324 198 528q194 207 520 207q324 0 521 -207q190 -203 198 -528q-8 -326 -198 -529q-197 -207 -521 -206q-326 0 -520 206q-195 205 -198 529zM299 715q0 -244 139 -400q143 -161 393 -161t394 161q139 156 139 400t-139 399q-143 162 -394 162 q-250 0 -393 -162q-139 -156 -139 -399zM502 1573.5q0 25.5 12 59.5t35 62q47 68 125 68q45 0 84 -15.5t76.5 -32.5t75.5 -32.5t75 -15.5t53.5 14t24.5 35q18 49 53 49q43 0 47 -45q0 -72 -47 -131q-53 -63 -125 -63q-61 0 -131 33.5t-107.5 49t-72.5 15.5q-55 0 -84 -66 q-10 -37 -47 -37q-18 0 -32.5 13.5t-14.5 39z" />
<glyph unicode="&#xd6;" horiz-adv-x="1660" d="M113 715q4 324 198 528q194 207 520 207q324 0 521 -207q190 -203 198 -528q-8 -326 -198 -529q-197 -207 -521 -206q-326 0 -520 206q-195 205 -198 529zM299 715q0 -244 139 -400q143 -161 393 -161t394 161q139 156 139 400t-139 399q-143 162 -394 162 q-250 0 -393 -162q-139 -156 -139 -399zM555 1634q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM901 1634q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1153" d="M121 152q0 31 24 55l322 321l-322 322q-25 25 -24 55q0 31 21.5 54.5t54 23.5t57.5 -25l321 -321l322 321q25 25 57.5 25t54 -23.5t21.5 -54.5t-24 -55l-322 -322l322 -321q25 -25 24 -55q0 -31 -21.5 -54.5t-54 -23.5t-57.5 24l-322 322l-321 -322q-25 -25 -57.5 -24.5 t-54 24t-21.5 54.5z" />
<glyph unicode="&#xd8;" horiz-adv-x="1660" d="M113 715q4 324 198 528q195 207 520 207q215 0 386 -100l81 118q14 20 40 20.5t40.5 -14t14.5 -33t-13 -36.5l-80 -115q117 -98 181.5 -245.5t68.5 -329.5q-8 -326 -198 -529q-197 -207 -521 -206q-219 0 -382 96l-82 -117q-14 -20 -40 -20t-40.5 14t-14.5 32.5t13 37.5 l80 112q-244 201 -252 580zM299 715q0 -272 170 -430l643 919q-123 72 -265 72t-241.5 -43t-167.5 -119q-139 -156 -139 -399zM551 223q121 -70 264 -69.5t242.5 43.5t167.5 118q139 156 139 400q0 268 -168 428z" />
<glyph unicode="&#xd9;" horiz-adv-x="1552" d="M188 537v809q0 39 27 66.5t66 27.5t66.5 -28t27.5 -66v-816q0 -182 125 -286q115 -94 276 -94q162 0 277 94q125 104 125 286v816q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-809q0 -268 -193 -422q-166 -135 -395 -135q-233 0 -397 135q-190 154 -191 422zM616 1755 q0 76 82 76q41 0 62 -39l104 -185q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51z" />
<glyph unicode="&#xda;" horiz-adv-x="1552" d="M188 537v809q0 39 27 66.5t66 27.5t66.5 -28t27.5 -66v-816q0 -182 125 -286q115 -94 276 -94q162 0 277 94q125 104 125 286v816q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-809q0 -268 -193 -422q-166 -135 -395 -135q-233 0 -397 135q-190 154 -191 422zM673 1562 q0 31 15 45l104 185q20 39 62 39q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xdb;" horiz-adv-x="1552" d="M188 537v809q0 39 27 66.5t66 27.5t66.5 -28t27.5 -66v-816q0 -182 125 -286q115 -94 276 -94q162 0 277 94q125 104 125 286v816q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-809q0 -268 -193 -422q-166 -135 -395 -135q-233 0 -397 135q-190 154 -191 422z M499 1559.5v23.5t17 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18t-17.5 30z" />
<glyph unicode="&#xdc;" horiz-adv-x="1552" d="M188 537v809q0 39 27 66.5t66 27.5t66.5 -28t27.5 -66v-816q0 -182 125 -286q115 -94 276 -94q162 0 277 94q125 104 125 286v816q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-809q0 -268 -193 -422q-166 -135 -395 -135q-233 0 -397 135q-190 154 -191 422zM500 1634 q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM846 1634q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1320" d="M61.5 1351.5q-0.5 34.5 24.5 61.5t71 27t76 -47l429 -623l428 623q31 47 76.5 47t70.5 -27t25 -61.5t-17 -61.5l-491 -686v-522q0 -39 -27 -65.5t-65 -26.5q-39 0 -67 26.5t-28 65.5v522l-489 686q-16 27 -16.5 61.5zM577 1562q0 31 15 45l104 185q20 39 62 39 q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xde;" horiz-adv-x="1374" d="M199 82v1264q0 39 26.5 66.5t65.5 27.5t66.5 -28t27.5 -66v-146h373q285 0 428 -147q119 -119 119 -308q0 -188 -119 -307q-143 -147 -428 -147h-373v-209q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5zM385 467h354q211 0 305 90q74 70 74 188q0 190 -190 254 q-74 25 -189 25h-354v-557z" />
<glyph unicode="&#xdf;" horiz-adv-x="1249" d="M162 84v911q0 215 129 336q125 119 334 119q176 0 301 -96q147 -111 147 -305q0 -207 -143 -291q-41 -25 -82 -37q129 -33 201 -103q104 -100 104 -262q0 -178 -121 -284q-106 -92 -268 -92q-131 0 -227 34q-66 23 -66 78q0 35 22.5 57.5t47 22.5t63.5 -13.5t98.5 -13.5 t99.5 9.5t77 33.5q88 61 88 191q0 117 -101 186q-96 68 -252 68q-37 0 -57 20.5t-20 53t21.5 53t55.5 24.5q127 14 200 80t73 173.5t-71 173t-193 65.5q-186 0 -250 -154q-25 -57 -25 -149v-889q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1136" d="M78 287q0 152 115 237q125 94 356 94h248v17q0 137 -67.5 195.5t-207.5 58.5q-119 0 -272 -49q-16 -4 -36.5 -4t-43.5 21.5t-22.5 66t53.5 67.5q172 74 356 74q209 0 324 -127q102 -111 102 -276v-580q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5v53 q-55 -57 -148.5 -106t-236.5 -49q-141 0 -236 81q-98 86 -98 226zM264 307q0 -164 217 -164q96 0 179 47.5t137 106.5v172h-224q-145 0 -227 -37t-82 -125zM387 1403q0 76 82 76q41 0 62 -39l104 -185q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51 z" />
<glyph unicode="&#xe1;" horiz-adv-x="1136" d="M78 287q0 152 115 237q125 94 356 94h248v17q0 137 -67.5 195.5t-207.5 58.5q-119 0 -272 -49q-16 -4 -36.5 -4t-43.5 21.5t-22.5 66t53.5 67.5q172 74 356 74q209 0 324 -127q102 -111 102 -276v-580q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5v53 q-55 -57 -148.5 -106t-236.5 -49q-141 0 -236 81q-98 86 -98 226zM264 307q0 -164 217 -164q96 0 179 47.5t137 106.5v172h-224q-145 0 -227 -37t-82 -125zM458 1210q0 31 15 45l104 185q20 39 62 39q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5 t-15.5 33.5z" />
<glyph unicode="&#xe2;" horiz-adv-x="1136" d="M78 287q0 152 115 237q125 94 356 94h248v17q0 137 -67.5 195.5t-207.5 58.5q-119 0 -272 -49q-16 -4 -36.5 -4t-43.5 21.5t-22.5 66t53.5 67.5q172 74 356 74q209 0 324 -127q102 -111 102 -276v-580q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5v53 q-55 -57 -148.5 -106t-236.5 -49q-141 0 -236 81q-98 86 -98 226zM264 307q0 -164 217 -164q96 0 179 47.5t137 106.5v172h-224q-145 0 -227 -37t-82 -125zM282 1207.5v23.5t17 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5 t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18t-17.5 30z" />
<glyph unicode="&#xe3;" horiz-adv-x="1136" d="M78 287q0 152 115 237q125 94 356 94h248v17q0 137 -67.5 195.5t-207.5 58.5q-119 0 -272 -49q-16 -4 -36.5 -4t-43.5 21.5t-22.5 66t53.5 67.5q172 74 356 74q209 0 324 -127q102 -111 102 -276v-580q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5v53 q-55 -57 -148.5 -106t-236.5 -49q-141 0 -236 81q-98 86 -98 226zM224 1221.5q0 25.5 12 59.5t35 62q47 68 125 68q45 0 84 -15.5t76.5 -32.5t75.5 -32.5t75 -15.5t53.5 14t24.5 35q18 49 53 49q43 0 47 -45q0 -72 -47 -131q-53 -63 -125 -63q-61 0 -131 33.5t-107.5 49 t-72.5 15.5q-55 0 -84 -66q-10 -37 -47 -37q-18 0 -32.5 13.5t-14.5 39zM264 307q0 -164 217 -164q96 0 179 47.5t137 106.5v172h-224q-145 0 -227 -37t-82 -125z" />
<glyph unicode="&#xe4;" horiz-adv-x="1136" d="M78 287q0 152 115 237q125 94 356 94h248v17q0 137 -67.5 195.5t-207.5 58.5q-119 0 -272 -49q-16 -4 -36.5 -4t-43.5 21.5t-22.5 66t53.5 67.5q172 74 356 74q209 0 324 -127q102 -111 102 -276v-580q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5v53 q-55 -57 -148.5 -106t-236.5 -49q-141 0 -236 81q-98 86 -98 226zM264 307q0 -164 217 -164q96 0 179 47.5t137 106.5v172h-224q-145 0 -227 -37t-82 -125zM285 1282q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM631 1282 q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1136" d="M78 287q0 152 115 237q125 94 356 94h248v17q0 137 -67.5 195.5t-207.5 58.5q-119 0 -272 -49q-16 -4 -36.5 -4t-43.5 21.5t-22.5 66t53.5 67.5q172 74 356 74q209 0 324 -127q102 -111 102 -276v-580q0 -39 -27.5 -65.5t-66.5 -26.5t-65.5 26.5t-26.5 65.5v53 q-55 -57 -148.5 -106t-236.5 -49q-141 0 -236 81q-98 86 -98 226zM264 307q0 -164 217 -164q96 0 179 47.5t137 106.5v172h-224q-145 0 -227 -37t-82 -125zM358 1321q0 80 56.5 136t134.5 56q41 0 76.5 -15t62.5 -40q55 -57 55 -137t-56.5 -136.5t-136 -56.5t-136 56.5 t-56.5 136.5zM450 1321q0 -41 30 -70.5t71 -29.5t70.5 29.5t29.5 70.5t-29.5 70.5t-70.5 29.5t-71 -29.5t-30 -70.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1851" d="M76 287q0 231 262 303q88 23 209 22h248v23q0 137 -68 195.5t-207 58.5q-119 0 -272 -49q-16 -4 -37 -4t-43.5 21.5t-22.5 66.5t64 71q162 70 344 70q244 0 350 -172q131 172 371 172q225 0 360 -147q129 -141 129 -367q0 -35 -23.5 -61.5t-66.5 -26.5h-704 q37 -309 362 -309q150 0 277 63q16 8 42.5 8t51 -22.5t24.5 -50.5q0 -53 -44 -77t-78.5 -40.5t-75.5 -28.5q-92 -27 -213 -26q-281 0 -416 178q-119 -178 -422 -178q-170 0 -282 81q-119 84 -119 226zM262 307q0 -66 63.5 -115t188.5 -49q190 0 256 103q25 39 25 100v117 h-224q-145 0 -227 -34t-82 -122zM969 612h610q-8 147 -109 228q-84 68 -196 67q-113 0 -197 -67q-100 -80 -108 -228z" />
<glyph unicode="&#xe7;" horiz-adv-x="1099" d="M94 522q0 248 164 400q156 143 383 143q123 0 202 -32t126 -62.5t47 -67.5t-22.5 -63.5t-45.5 -26.5q-35 0 -71 21q-104 57 -220 57q-180 0 -278 -101.5t-98 -267.5t98 -267t278 -101q80 0 131.5 18t83.5 37q43 23 71 22.5t50.5 -27t22.5 -63.5t-47 -67 q-139 -90 -324 -94l-61 -109q33 8 86 8t97 -39t44 -104q0 -188 -227 -189q-98 0 -185 54q-23 10 -22 34q0 41 39 41q8 0 20 -6q64 -35 121 -35q117 0 117 95q0 31 -21.5 47t-48.5 16q-49 0 -74 -12q-33 -16 -44 -16.5t-22 9t-11 25t8 27.5l98 160q-201 27 -330 164 q-135 147 -135 372z" />
<glyph unicode="&#xe8;" horiz-adv-x="1167" d="M100 522q0 225 117 375q131 168 373 168q229 0 362 -156q125 -145 125 -360q0 -39 -25.5 -65.5t-68.5 -26.5h-694q16 -140 104 -221.5t246 -81.5t264 53q37 18 63.5 18t51 -22.5t24.5 -50.5q0 -53 -44 -77t-78.5 -40.5t-75.5 -28.5q-92 -27 -213 -26q-252 0 -391.5 141 t-139.5 401zM289 608h604q-8 145 -107 230q-81 69 -196 69q-182 0 -266 -162q-31 -57 -35 -137zM428 1403q0 76 82 76q41 0 62 -39l104 -185q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51z" />
<glyph unicode="&#xe9;" horiz-adv-x="1167" d="M100 522q0 225 117 375q131 168 373 168q229 0 362 -156q125 -145 125 -360q0 -39 -25.5 -65.5t-68.5 -26.5h-694q16 -140 104 -221.5t246 -81.5t264 53q37 18 63.5 18t51 -22.5t24.5 -50.5q0 -53 -44 -77t-78.5 -40.5t-75.5 -28.5q-92 -27 -213 -26q-252 0 -391.5 141 t-139.5 401zM289 608h604q-8 145 -107 230q-81 69 -196 69q-182 0 -266 -162q-31 -57 -35 -137zM487 1210q0 31 15 45l104 185q20 39 62 39q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xea;" horiz-adv-x="1167" d="M100 522q0 225 117 375q131 168 373 168q229 0 362 -156q125 -145 125 -360q0 -39 -25.5 -65.5t-68.5 -26.5h-694q16 -140 104 -221.5t246 -81.5t264 53q37 18 63.5 18t51 -22.5t24.5 -50.5q0 -53 -44 -77t-78.5 -40.5t-75.5 -28.5q-92 -27 -213 -26q-252 0 -391.5 141 t-139.5 401zM289 608h604q-8 145 -107 230q-81 69 -196 69q-182 0 -266 -162q-31 -57 -35 -137zM311 1207.5v23.5t17 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18 t-17.5 30z" />
<glyph unicode="&#xeb;" horiz-adv-x="1167" d="M100 522q0 225 117 375q131 168 373 168q229 0 362 -156q125 -145 125 -360q0 -39 -25.5 -65.5t-68.5 -26.5h-694q16 -140 104 -221.5t246 -81.5t264 53q37 18 63.5 18t51 -22.5t24.5 -50.5q0 -53 -44 -77t-78.5 -40.5t-75.5 -28.5q-92 -27 -213 -26q-252 0 -391.5 141 t-139.5 401zM289 608h604q-8 145 -107 230q-81 69 -196 69q-182 0 -266 -162q-31 -57 -35 -137zM311 1282q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM657 1282q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5 t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xec;" horiz-adv-x="526" d="M86 1403q0 76 82 76q41 0 61 -39l105 -185q14 -14 14 -38.5t-15.5 -40t-41 -15.5t-43.5 23l-142 168q-20 22 -20 51zM170 82v879q0 40 26.5 67t66.5 27t66.5 -27t26.5 -67v-879q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5z" />
<glyph unicode="&#xed;" horiz-adv-x="526" d="M170 82v879q0 40 26.5 67t66.5 27t66.5 -27t26.5 -67v-879q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5zM178 1210q0 31 15 45l104 185q20 39 61 39q82 0 82 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xee;" horiz-adv-x="526" d="M-14 1207.5v23.5t16 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16.5 -40t-17 -42.5t-42 -18.5t-39.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42 18t-17 30zM170 82v879q0 40 26.5 67t66.5 27t66.5 -27t26.5 -67v-879q0 -39 -26.5 -65.5t-66.5 -26.5 t-66.5 26.5t-26.5 65.5z" />
<glyph unicode="&#xef;" horiz-adv-x="526" d="M-14 1282q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM170 82v879q0 40 26.5 67t66.5 27t66.5 -27t26.5 -67v-879q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5zM332 1282q0 43 30.5 73.5t73.5 30.5t74 -30.5 t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1226" d="M100 508q0 217 125 364q135 162 355 162q120 0 231 -57q-84 123 -219 240l-172 -101q-25 -12 -41.5 -12t-30.5 14.5t-14 41t31 42.5l139 82q-94 66 -134 87.5t-51.5 41t-11.5 49t25.5 54t54.5 24.5t62 -20q111 -66 210 -146l140 84q16 10 34.5 10t33 -14t14.5 -41 t-25 -41l-115 -67q377 -344 377 -744q0 -406 -274 -536q-98 -45 -224 -45t-220.5 37.5t-161.5 107.5q-137 145 -138 383zM281 516q0 -188 106 -287q90 -86 221 -86t221 86q109 100 109 287q0 182 -109 283q-88 84 -221 84t-221 -84q-106 -98 -106 -283z" />
<glyph unicode="&#xf1;" horiz-adv-x="1247" d="M162 82v879q0 41 26.5 67.5t65.5 26.5t66.5 -27t27.5 -67v-84q82 86 141 120q117 68 222.5 68t173 -32t114.5 -87q96 -113 97 -282v-582q0 -39 -28 -65.5t-67 -26.5t-65.5 26.5t-26.5 65.5v561q0 117 -52 183.5t-173 66.5q-180 0 -336 -197v-614q0 -39 -27.5 -65.5 t-66.5 -26.5t-65.5 26.5t-26.5 65.5zM306 1221.5q0 25.5 12 59.5t35 62q47 68 125 68q45 0 84 -15.5t76.5 -32.5t75.5 -32.5t75 -15.5t53.5 14t24.5 35q18 49 53 49q43 0 47 -45q0 -72 -47 -131q-53 -63 -125 -63q-61 0 -131 33.5t-107.5 49t-72.5 15.5q-55 0 -84 -66 q-10 -37 -47 -37q-18 0 -32.5 13.5t-14.5 39z" />
<glyph unicode="&#xf2;" horiz-adv-x="1245" d="M98 522q0 244 144 393q141 150 381 150q238 0 381 -150q143 -152 143 -393q0 -242 -143 -393q-143 -150 -381 -149q-240 0 -381 149q-143 150 -144 393zM284.5 523q0.5 -101 31 -172.5t78.5 -116t108.5 -64.5t119 -20t119 20t109.5 63q111 100 111 291q0 184 -111 287 q-92 84 -227 84q-221 0 -308 -199q-31 -72 -30.5 -173zM446 1403q0 76 82 76q41 0 62 -39l104 -185q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51z" />
<glyph unicode="&#xf3;" horiz-adv-x="1245" d="M98 522q0 244 144 393q141 150 381 150q238 0 381 -150q143 -152 143 -393q0 -242 -143 -393q-143 -150 -381 -149q-240 0 -381 149q-143 150 -144 393zM284.5 523q0.5 -101 31 -172.5t78.5 -116t108.5 -64.5t119 -20t119 20t109.5 63q111 100 111 291q0 184 -111 287 q-92 84 -227 84q-221 0 -308 -199q-31 -72 -30.5 -173zM536 1210q0 31 15 45l104 185q20 39 62 39q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xf4;" horiz-adv-x="1245" d="M98 522q0 244 144 393q141 150 381 150q238 0 381 -150q143 -152 143 -393q0 -242 -143 -393q-143 -150 -381 -149q-240 0 -381 149q-143 150 -144 393zM284.5 523q0.5 -101 31 -172.5t78.5 -116t108.5 -64.5t119 -20t119 20t109.5 63q111 100 111 291q0 184 -111 287 q-92 84 -227 84q-221 0 -308 -199q-31 -72 -30.5 -173zM346 1207.5v23.5t17 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18t-17.5 30z" />
<glyph unicode="&#xf5;" horiz-adv-x="1245" d="M98 522q0 244 144 393q141 150 381 150q238 0 381 -150q143 -152 143 -393q0 -242 -143 -393q-143 -150 -381 -149q-240 0 -381 149q-143 150 -144 393zM284.5 523q0.5 -101 31 -172.5t78.5 -116t108.5 -64.5t119 -20t119 20t109.5 63q111 100 111 291q0 184 -111 287 q-92 84 -227 84q-221 0 -308 -199q-31 -72 -30.5 -173zM295 1221.5q0 25.5 12 59.5t35 62q47 68 125 68q45 0 84 -15.5t76.5 -32.5t75.5 -32.5t75 -15.5t53.5 14t24.5 35q18 49 53 49q43 0 47 -45q0 -72 -47 -131q-53 -63 -125 -63q-61 0 -131 33.5t-107.5 49t-72.5 15.5 q-55 0 -84 -66q-10 -37 -47 -37q-18 0 -32.5 13.5t-14.5 39z" />
<glyph unicode="&#xf6;" horiz-adv-x="1245" d="M98 522q0 244 144 393q141 150 381 150q238 0 381 -150q143 -152 143 -393q0 -242 -143 -393q-143 -150 -381 -149q-240 0 -381 149q-143 150 -144 393zM284.5 523q0.5 -101 31 -172.5t78.5 -116t108.5 -64.5t119 -20t119 20t109.5 63q111 100 111 291q0 184 -111 287 q-92 84 -227 84q-221 0 -308 -199q-31 -72 -30.5 -173zM346 1282q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM692 1282q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1458" d="M150 678q0 35 23.5 59.5t57.5 24.5h994q35 0 59.5 -24.5t24.5 -59.5t-24.5 -58.5t-59.5 -23.5h-994q-35 0 -58 23.5t-23 58.5zM625 299q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM625 1057q0 43 30.5 73.5t73.5 30.5 t74 -30.5t31 -73.5t-31 -74t-74 -31t-73.5 31t-30.5 74z" />
<glyph unicode="&#xf8;" horiz-adv-x="1245" d="M98 522q0 244 144 393q141 150 381 150q143 0 270 -66l59 84q14 20 40 20.5t40.5 -14t14.5 -33t-13 -36.5l-57 -82q170 -150 170 -416q0 -242 -143 -393q-143 -150 -381 -149q-154 0 -273 65l-61 -88q-14 -20 -40 -20t-40 14t-14 32.5t12 36.5l59 84q-168 152 -168 418z M284.5 530.5q0.5 -178.5 88.5 -274.5l418 596q-80 43 -154 43t-134.5 -19.5t-109.5 -64.5q-109 -102 -108.5 -280.5zM455 193q78 -43 151.5 -43t134 20t109.5 63q111 100 111 291q0 162 -89 264z" />
<glyph unicode="&#xf9;" horiz-adv-x="1249" d="M154 381v582q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-562q0 -117 52 -183t173 -66q180 0 336 196v615q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-879q0 -41 -26.5 -67.5t-65.5 -26.5t-66.5 26.5t-27.5 67.5v84q-82 -86 -141 -121q-117 -68 -222.5 -67.5 t-173 32t-114.5 86.5q-96 113 -96 283zM444 1403q0 76 82 76q41 0 62 -39l104 -185q14 -14 14 -38.5t-15 -40t-40.5 -15.5t-44.5 23l-141 168q-21 22 -21 51z" />
<glyph unicode="&#xfa;" horiz-adv-x="1249" d="M154 381v582q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-562q0 -117 52 -183t173 -66q180 0 336 196v615q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-879q0 -41 -26.5 -67.5t-65.5 -26.5t-66.5 26.5t-27.5 67.5v84q-82 -86 -141 -121q-117 -68 -222.5 -67.5 t-173 32t-114.5 86.5q-96 113 -96 283zM534 1210q0 31 15 45l104 185q20 39 62 39q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xfb;" horiz-adv-x="1249" d="M154 381v582q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-562q0 -117 52 -183t173 -66q180 0 336 196v615q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-879q0 -41 -26.5 -67.5t-65.5 -26.5t-66.5 26.5t-27.5 67.5v84q-82 -86 -141 -121q-117 -68 -222.5 -67.5 t-173 32t-114.5 86.5q-96 113 -96 283zM344 1207.5v23.5t17 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18t-17.5 30z" />
<glyph unicode="&#xfc;" horiz-adv-x="1249" d="M154 381v582q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-562q0 -117 52 -183t173 -66q180 0 336 196v615q0 39 27.5 65.5t66.5 26.5t65.5 -27t26.5 -65v-879q0 -41 -26.5 -67.5t-65.5 -26.5t-66.5 26.5t-27.5 67.5v84q-82 -86 -141 -121q-117 -68 -222.5 -67.5 t-173 32t-114.5 86.5q-96 113 -96 283zM344 1282q0 43 30.5 73.5t73.5 30.5t74 -30.5t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM690 1282q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1091" d="M49 970.5q0 28.5 26.5 56.5t59.5 28q68 0 92 -58l316 -782l321 778q27 61 86 62q39 0 65.5 -28t26.5 -57.5t-6 -43.5l-559 -1344q-27 -63 -86 -63q-37 0 -62.5 26.5t-25.5 53t6 43.5l133 342l-385 938q-8 20 -8 48.5zM458 1210q0 31 15 45l104 185q20 39 62 39 q82 0 81 -76q0 -29 -20 -51l-141 -168q-18 -23 -44 -23t-41.5 15.5t-15.5 33.5z" />
<glyph unicode="&#xfe;" horiz-adv-x="1292" d="M162 -387v1798q0 41 26.5 67.5t66.5 26.5t66.5 -26.5t26.5 -67.5v-502q152 156 350.5 156t339.5 -141q156 -156 156 -402q0 -250 -156 -403q-143 -139 -340.5 -139t-349.5 155v-522q0 -41 -26.5 -67.5t-66.5 -26.5t-66.5 26.5t-26.5 67.5zM348 307q139 -154 324 -153 q233 0 311 208q25 70 25 160q0 176 -95 277q-90 92 -241 92q-184 0 -324 -154v-430z" />
<glyph unicode="&#xff;" horiz-adv-x="1091" d="M49 970.5q0 28.5 26.5 56.5t59.5 28q68 0 92 -58l316 -782l321 778q27 61 86 62q39 0 65.5 -28t26.5 -57.5t-6 -43.5l-559 -1344q-27 -63 -86 -63q-37 0 -62.5 26.5t-25.5 53t6 43.5l133 342l-385 938q-8 20 -8 48.5zM266 1282q0 43 30.5 73.5t73.5 30.5t74 -30.5 t31 -73.5t-31 -73.5t-74 -30.5t-73.5 30.5t-30.5 73.5zM612 1282q0 43 31 73.5t74 30.5t73.5 -30.5t30.5 -73.5t-30.5 -73.5t-73.5 -30.5t-74 30.5t-31 73.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="837" d="M139 1207.5v23.5t17 29l139 143q41 41 82 41h78q41 0 82 -41l139 -143q16 -16 16 -40t-17.5 -42.5t-42 -18.5t-38.5 15l-178 151l-178 -151q-14 -14 -39 -14.5t-42.5 18t-17.5 30z" />
<glyph unicode="&#x2dc;" horiz-adv-x="884" d="M113 1221.5q0 25.5 12 59.5t35 62q47 68 125 68q45 0 84 -15.5t76.5 -32.5t75.5 -32.5t75 -15.5t53.5 14t24.5 35q18 49 53 49q43 0 47 -45q0 -72 -47 -131q-53 -63 -125 -63q-61 0 -131 33.5t-107.5 49t-72.5 15.5q-55 0 -84 -66q-10 -37 -47 -37q-18 0 -32.5 13.5 t-14.5 39z" />
<glyph unicode="&#x2000;" horiz-adv-x="940" />
<glyph unicode="&#x2001;" horiz-adv-x="1880" />
<glyph unicode="&#x2002;" horiz-adv-x="940" />
<glyph unicode="&#x2003;" horiz-adv-x="1880" />
<glyph unicode="&#x2004;" horiz-adv-x="626" />
<glyph unicode="&#x2005;" horiz-adv-x="470" />
<glyph unicode="&#x2006;" horiz-adv-x="313" />
<glyph unicode="&#x2007;" horiz-adv-x="313" />
<glyph unicode="&#x2008;" horiz-adv-x="235" />
<glyph unicode="&#x2009;" horiz-adv-x="376" />
<glyph unicode="&#x200a;" horiz-adv-x="104" />
<glyph unicode="&#x2010;" horiz-adv-x="835" d="M141 573q0 35 23.5 59.5t58.5 24.5h387q35 0 59.5 -24.5t24.5 -59.5t-24.5 -58t-59.5 -23h-387q-35 0 -58.5 23.5t-23.5 57.5z" />
<glyph unicode="&#x2011;" horiz-adv-x="835" d="M141 573q0 35 23.5 59.5t58.5 24.5h387q35 0 59.5 -24.5t24.5 -59.5t-24.5 -58t-59.5 -23h-387q-35 0 -58.5 23.5t-23.5 57.5z" />
<glyph unicode="&#x2012;" horiz-adv-x="835" d="M141 573q0 35 23.5 59.5t58.5 24.5h387q35 0 59.5 -24.5t24.5 -59.5t-24.5 -58t-59.5 -23h-387q-35 0 -58.5 23.5t-23.5 57.5z" />
<glyph unicode="&#x2013;" horiz-adv-x="1400" d="M141 573q0 35 23.5 59.5t58.5 24.5h953q34 0 59 -24.5t25 -59.5t-25 -58t-59 -23h-953q-35 0 -58.5 23.5t-23.5 57.5z" />
<glyph unicode="&#x2014;" horiz-adv-x="1824" d="M141 573q0 35 23.5 59.5t58.5 24.5h1376q35 0 59.5 -24.5t24.5 -59.5t-24.5 -58t-59.5 -23h-1376q-35 0 -58.5 23.5t-23.5 57.5z" />
<glyph unicode="&#x2018;" horiz-adv-x="522" d="M135 1092v120q0 109 53 193q41 61 115 106q10 10 36 10.5t42 -18t16 -47.5t-30 -47q-45 -27 -75 -66.5t-30 -123.5h8q47 0 81 -34t34 -81v-12q0 -47 -34 -81t-81 -34h-20q-47 0 -81 33.5t-34 81.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="487" d="M129 1042.5q0 28.5 31 47.5q45 27 74.5 66.5t29.5 123.5h-8q-47 0 -81 34t-34 81v12q0 47 34 81t81 34h20q48 0 81.5 -34t33.5 -81v-121q0 -109 -55 -192q-39 -61 -113 -107q-10 -10 -35.5 -10t-42 18.5t-16.5 47z" />
<glyph unicode="&#x201a;" horiz-adv-x="532" d="M129 -243.5q0 28.5 31 46.5q45 27 74.5 67t29.5 124h-8q-47 0 -81 33.5t-34 81.5v12q0 47 34 81t81 34h20q48 0 81.5 -34t33.5 -81v-121q0 -109 -55 -193q-39 -61 -113 -106q-10 -10 -35.5 -10t-42 18.5t-16.5 47z" />
<glyph unicode="&#x201c;" horiz-adv-x="894" d="M135 1092v120q0 109 53 193q41 61 115 106q10 10 36 10.5t42 -18t16 -47.5t-30 -47q-45 -27 -75 -66.5t-30 -123.5h8q47 0 81 -34t34 -81v-12q0 -47 -34 -81t-81 -34h-20q-47 0 -81 33.5t-34 81.5zM506 1092v120q0 109 55 193q39 61 113 106q10 10 35.5 10.5t42 -18 t16.5 -47.5t-31 -47q-45 -27 -74.5 -66.5t-29.5 -123.5h8q47 0 81 -34t34 -81v-12q0 -47 -34 -81t-81 -34h-20q-47 0 -81 33.5t-34 81.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="860" d="M129 1042.5q0 28.5 31 47.5q45 27 74.5 66.5t29.5 123.5h-8q-47 0 -81 34t-34 81v12q0 47 34 81t81 34h20q47 0 81 -34t34 -81v-121q0 -109 -55 -192q-39 -61 -113 -107q-10 -10 -35.5 -10t-42 18.5t-16.5 47zM500 1042.5q0 28.5 30 47.5q45 27 75 66.5t30 123.5h-8 q-47 0 -81 34t-34 81v12q0 47 34 81t81 34h20q47 0 81 -34t34 -81v-121q0 -109 -53 -192q-41 -61 -115 -107q-10 -10 -36 -10t-42 18.5t-16 47z" />
<glyph unicode="&#x201e;" horiz-adv-x="905" d="M129 -243.5q0 28.5 31 46.5q45 27 74.5 67t29.5 124h-8q-47 0 -81 33.5t-34 81.5v12q0 47 34 81t81 34h20q47 0 81 -34t34 -81v-121q0 -109 -55 -193q-39 -61 -113 -106q-10 -10 -35.5 -10t-42 18.5t-16.5 47zM500 -243.5q0 28.5 30 46.5q45 27 75 67t30 124h-8 q-47 0 -81 33.5t-34 81.5v12q0 47 34 81t81 34h20q47 0 81 -34t34 -81v-121q0 -109 -53 -193q-41 -61 -115 -106q-10 -10 -36 -10t-42 18.5t-16 47z" />
<glyph unicode="&#x2022;" horiz-adv-x="772" d="M115 805q0 113 79.5 192.5t190.5 79.5q57 0 106.5 -21.5t86.5 -58.5q80 -80 79 -192q0 -113 -79.5 -193t-190.5 -80q-113 0 -192.5 80t-79.5 193z" />
<glyph unicode="&#x2026;" horiz-adv-x="1982" d="M141 109v12q0 47 34 81t81 34h20q47 0 81 -34t34 -81v-12q0 -47 -33.5 -81t-81.5 -34h-20q-47 0 -81 33.5t-34 81.5zM868 109v12q0 47 34 81t81 34h21q47 0 80.5 -34t33.5 -81v-12q0 -47 -33.5 -81t-80.5 -34h-21q-47 0 -81 33.5t-34 81.5zM1589 109v12q0 47 34 81t81 34 h20q47 0 81 -34t34 -81v-12q0 -47 -33.5 -81t-81.5 -34h-20q-47 0 -81 33.5t-34 81.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="376" />
<glyph unicode="&#x2039;" horiz-adv-x="772" d="M111 522q0 51 36 88l336 371q18 23 55 23t60 -23t23 -56.5t-19 -54.5l-328 -348l328 -348q18 -23 18.5 -55.5t-22.5 -55t-59.5 -22.5t-55.5 22l-336 371q-37 37 -36 88z" />
<glyph unicode="&#x203a;" horiz-adv-x="772" d="M152 119q0 33 18 55l328 348l-328 348q-18 20 -18 54t22.5 57t59 23t55.5 -23l336 -371q37 -37 37 -88t-37 -88l-336 -371q-18 -23 -55 -22.5t-59.5 23t-22.5 55.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="470" />
<glyph unicode="&#x20ac;" horiz-adv-x="1396" d="M86 543q0 31 21.5 53t52.5 22h112q-8 46 -8 97q0 47 8 96h-112q-31 0 -52.5 21.5t-21.5 52.5t21.5 52t52.5 21h145q68 217 240 353q176 139 413 139q164 0 299 -66q47 -25 47.5 -66.5t-24.5 -65t-47 -23.5q-31 0 -66 12q-80 33 -182 33t-172 -21.5t-129 -62.5 q-123 -86 -184 -232h587q31 0 52.5 -21.5t21.5 -51t-21.5 -52t-52.5 -22.5h-630q-6 -47 -6 -97t6 -96h528q31 0 53.5 -22.5t22.5 -54t-20.5 -52t-55.5 -20.5h-485q61 -145 183 -229t275 -84q123 0 224 34q20 10 46.5 10.5t51.5 -23t25 -66.5t-48 -66q-135 -66 -278 -65.5 t-247.5 37t-186.5 102.5q-174 137 -240 350h-145q-31 0 -52.5 21.5t-21.5 52.5z" />
<glyph unicode="&#x2122;" horiz-adv-x="1951" d="M117 1370q0 25 17.5 42.5t41.5 17.5h541q25 0 42 -17.5t17 -42.5t-17.5 -42t-41.5 -17h-207v-598q0 -29 -18.5 -46.5t-47.5 -17.5t-46 17.5t-17 46.5v598h-205q-25 0 -42 17.5t-17 41.5zM889 715v633q0 35 24.5 60.5t61.5 25.5h35q55 0 80 -54l237 -587l238 587 q25 53 80 54h34q37 0 61.5 -26t24.5 -60v-633q0 -29 -18 -47.5t-47 -18.5t-47.5 18.5t-18.5 47.5v536l-227 -551q-20 -51 -80 -51q-59 0 -80 51l-227 551v-536q0 -29 -18.5 -47.5t-47.5 -18.5t-47 18.5t-18 47.5z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1054" d="M0 0v1055h1055v-1055h-1055z" />
<glyph unicode="&#xfb01;" horiz-adv-x="1312" d="M39 965q0 35 22.5 57t57.5 22h121v119q0 178 100 275q82 78 207 78q96 0 178 -41q47 -27 47 -64t-21.5 -61.5t-54.5 -24.5l-102 12q-78 0 -123 -35.5t-45 -144.5v-113h629q39 -4 63.5 -29.5t24.5 -64.5v-868q0 -39 -26.5 -65.5t-66.5 -26.5t-67 26.5t-27 65.5v803h-530 v-803q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5v803h-121q-35 0 -57.5 22.5t-22.5 57.5zM940 1362v8q0 43 30.5 74t73.5 31h13q43 0 73.5 -31t30.5 -74v-8q0 -43 -30.5 -74t-73.5 -31h-13q-43 0 -73.5 31t-30.5 74z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1318" d="M39 965q0 35 22.5 57t57.5 22h121v119q0 178 100 275q82 78 207 78q96 0 178 -41q47 -27 47 -64t-21.5 -61.5t-54.5 -24.5l-102 12q-78 0 -123 -35.5t-45 -144.5v-113h256q35 0 57.5 -22.5t22.5 -56.5q0 -35 -22.5 -57.5t-57.5 -22.5h-256v-803q0 -39 -26.5 -65.5 t-66.5 -26.5t-66.5 26.5t-26.5 65.5v803h-121q-35 0 -57.5 22.5t-22.5 57.5zM954 82v1329q0 41 26.5 67.5t66.5 26.5t67 -26.5t27 -67.5v-1329q0 -39 -27 -65.5t-67 -26.5t-66.5 26.5t-26.5 65.5z" />
<glyph unicode="&#xfb03;" horiz-adv-x="2119" d="M39 965q0 35 22.5 57t57.5 22h121v119q0 178 100 275q82 78 207 78q96 0 178 -41q47 -27 47 -64t-21.5 -61.5t-54.5 -24.5l-102 12q-78 0 -123 -35.5t-45 -144.5v-113h621v119q0 178 100 275q82 78 207 78q96 0 178 -41q47 -27 47 -64t-21.5 -61.5t-54.5 -24.5l-102 12 q-78 0 -123 -35.5t-45 -144.5v-113h622q41 0 68 -26.5t27 -67.5v-868q0 -39 -27 -65.5t-66.5 -26.5t-66.5 26.5t-27 65.5v803h-530v-803q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5v803h-621v-803q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5v803h-121 q-35 0 -57.5 22.5t-22.5 57.5zM1747 1362v8q0 43 30.5 74t73.5 31h13q43 0 73.5 -31t30.5 -74v-8q0 -43 -30.5 -74t-73.5 -31h-13q-43 0 -73.5 31t-30.5 74z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2125" d="M39 965q0 35 22.5 57t57.5 22h121v119q0 178 100 275q82 78 207 78q96 0 178 -41q47 -27 47 -64t-21.5 -61.5t-54.5 -24.5l-102 12q-78 0 -123 -35.5t-45 -144.5v-113h621v119q0 178 100 275q82 78 207 78q96 0 178 -41q47 -27 47 -64t-21.5 -61.5t-54.5 -24.5l-102 12 q-78 0 -123 -35.5t-45 -144.5v-113h256q35 0 57.5 -22.5t22.5 -56.5q0 -35 -22.5 -57.5t-57.5 -22.5h-256v-803q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5v803h-621v-803q0 -39 -26.5 -65.5t-66.5 -26.5t-66.5 26.5t-26.5 65.5v803h-121q-35 0 -57.5 22.5 t-22.5 57.5zM1761 82v1329q0 41 26.5 67.5t66.5 26.5t67 -26.5t27 -67.5v-1329q0 -39 -27 -65.5t-67 -26.5t-66.5 26.5t-26.5 65.5z" />
<hkern u1="&#x22;" u2="&#x2039;" k="70" />
<hkern u1="&#x22;" u2="&#x2026;" k="266" />
<hkern u1="&#x22;" u2="&#x201e;" k="281" />
<hkern u1="&#x22;" u2="&#x201a;" k="266" />
<hkern u1="&#x22;" u2="&#xf8;" k="57" />
<hkern u1="&#x22;" u2="&#xf6;" k="57" />
<hkern u1="&#x22;" u2="&#xf5;" k="57" />
<hkern u1="&#x22;" u2="&#xf4;" k="57" />
<hkern u1="&#x22;" u2="&#xf3;" k="57" />
<hkern u1="&#x22;" u2="&#xf2;" k="57" />
<hkern u1="&#x22;" u2="&#xf0;" k="57" />
<hkern u1="&#x22;" u2="&#xeb;" k="57" />
<hkern u1="&#x22;" u2="&#xea;" k="57" />
<hkern u1="&#x22;" u2="&#xe9;" k="57" />
<hkern u1="&#x22;" u2="&#xe8;" k="57" />
<hkern u1="&#x22;" u2="&#xe7;" k="57" />
<hkern u1="&#x22;" u2="&#xe6;" k="29" />
<hkern u1="&#x22;" u2="&#xe5;" k="29" />
<hkern u1="&#x22;" u2="&#xe4;" k="29" />
<hkern u1="&#x22;" u2="&#xe3;" k="29" />
<hkern u1="&#x22;" u2="&#xe2;" k="29" />
<hkern u1="&#x22;" u2="&#xe1;" k="29" />
<hkern u1="&#x22;" u2="&#xe0;" k="29" />
<hkern u1="&#x22;" u2="&#xc6;" k="131" />
<hkern u1="&#x22;" u2="&#xc5;" k="104" />
<hkern u1="&#x22;" u2="&#xc4;" k="104" />
<hkern u1="&#x22;" u2="&#xc3;" k="104" />
<hkern u1="&#x22;" u2="&#xc2;" k="104" />
<hkern u1="&#x22;" u2="&#xc1;" k="104" />
<hkern u1="&#x22;" u2="&#xc0;" k="104" />
<hkern u1="&#x22;" u2="&#xab;" k="70" />
<hkern u1="&#x22;" u2="s" k="33" />
<hkern u1="&#x22;" u2="q" k="57" />
<hkern u1="&#x22;" u2="o" k="57" />
<hkern u1="&#x22;" u2="g" k="57" />
<hkern u1="&#x22;" u2="e" k="57" />
<hkern u1="&#x22;" u2="d" k="57" />
<hkern u1="&#x22;" u2="c" k="57" />
<hkern u1="&#x22;" u2="a" k="29" />
<hkern u1="&#x22;" u2="A" k="104" />
<hkern u1="&#x22;" u2="&#x40;" k="41" />
<hkern u1="&#x22;" u2="&#x2f;" k="88" />
<hkern u1="&#x22;" u2="&#x2e;" k="266" />
<hkern u1="&#x22;" u2="&#x2c;" k="266" />
<hkern u1="&#x26;" u2="&#xfb04;" k="27" />
<hkern u1="&#x26;" u2="&#xfb03;" k="27" />
<hkern u1="&#x26;" u2="&#xfb02;" k="27" />
<hkern u1="&#x26;" u2="&#xfb01;" k="27" />
<hkern u1="&#x26;" u2="&#x201d;" k="94" />
<hkern u1="&#x26;" u2="&#x2019;" k="94" />
<hkern u1="&#x26;" u2="&#xff;" k="43" />
<hkern u1="&#x26;" u2="&#xfd;" k="43" />
<hkern u1="&#x26;" u2="&#xdf;" k="27" />
<hkern u1="&#x26;" u2="&#xdd;" k="115" />
<hkern u1="&#x26;" u2="&#xdc;" k="10" />
<hkern u1="&#x26;" u2="&#xdb;" k="10" />
<hkern u1="&#x26;" u2="&#xda;" k="10" />
<hkern u1="&#x26;" u2="&#xd9;" k="10" />
<hkern u1="&#x26;" u2="&#xc6;" k="-39" />
<hkern u1="&#x26;" u2="y" k="43" />
<hkern u1="&#x26;" u2="v" k="49" />
<hkern u1="&#x26;" u2="t" k="31" />
<hkern u1="&#x26;" u2="f" k="27" />
<hkern u1="&#x26;" u2="Y" k="115" />
<hkern u1="&#x26;" u2="V" k="94" />
<hkern u1="&#x26;" u2="U" k="10" />
<hkern u1="&#x26;" u2="T" k="98" />
<hkern u1="&#x26;" u2="&#x27;" k="94" />
<hkern u1="&#x26;" u2="&#x22;" k="94" />
<hkern u1="&#x27;" u2="&#x2039;" k="70" />
<hkern u1="&#x27;" u2="&#x2026;" k="244" />
<hkern u1="&#x27;" u2="&#x201e;" k="244" />
<hkern u1="&#x27;" u2="&#x201a;" k="244" />
<hkern u1="&#x27;" u2="&#xf8;" k="57" />
<hkern u1="&#x27;" u2="&#xf6;" k="57" />
<hkern u1="&#x27;" u2="&#xf5;" k="57" />
<hkern u1="&#x27;" u2="&#xf4;" k="57" />
<hkern u1="&#x27;" u2="&#xf3;" k="57" />
<hkern u1="&#x27;" u2="&#xf2;" k="57" />
<hkern u1="&#x27;" u2="&#xf0;" k="57" />
<hkern u1="&#x27;" u2="&#xeb;" k="57" />
<hkern u1="&#x27;" u2="&#xea;" k="57" />
<hkern u1="&#x27;" u2="&#xe9;" k="57" />
<hkern u1="&#x27;" u2="&#xe8;" k="57" />
<hkern u1="&#x27;" u2="&#xe7;" k="57" />
<hkern u1="&#x27;" u2="&#xe6;" k="29" />
<hkern u1="&#x27;" u2="&#xe5;" k="29" />
<hkern u1="&#x27;" u2="&#xe4;" k="29" />
<hkern u1="&#x27;" u2="&#xe3;" k="29" />
<hkern u1="&#x27;" u2="&#xe2;" k="29" />
<hkern u1="&#x27;" u2="&#xe1;" k="29" />
<hkern u1="&#x27;" u2="&#xe0;" k="29" />
<hkern u1="&#x27;" u2="&#xc6;" k="131" />
<hkern u1="&#x27;" u2="&#xc5;" k="104" />
<hkern u1="&#x27;" u2="&#xc4;" k="104" />
<hkern u1="&#x27;" u2="&#xc3;" k="104" />
<hkern u1="&#x27;" u2="&#xc2;" k="104" />
<hkern u1="&#x27;" u2="&#xc1;" k="104" />
<hkern u1="&#x27;" u2="&#xc0;" k="104" />
<hkern u1="&#x27;" u2="&#xab;" k="70" />
<hkern u1="&#x27;" u2="s" k="33" />
<hkern u1="&#x27;" u2="q" k="57" />
<hkern u1="&#x27;" u2="o" k="57" />
<hkern u1="&#x27;" u2="g" k="57" />
<hkern u1="&#x27;" u2="e" k="57" />
<hkern u1="&#x27;" u2="d" k="57" />
<hkern u1="&#x27;" u2="c" k="57" />
<hkern u1="&#x27;" u2="a" k="29" />
<hkern u1="&#x27;" u2="A" k="104" />
<hkern u1="&#x27;" u2="&#x40;" k="41" />
<hkern u1="&#x27;" u2="&#x2f;" k="88" />
<hkern u1="&#x27;" u2="&#x2e;" k="244" />
<hkern u1="&#x27;" u2="&#x2c;" k="244" />
<hkern u1="&#x28;" u2="&#xfb04;" k="27" />
<hkern u1="&#x28;" u2="&#xfb03;" k="27" />
<hkern u1="&#x28;" u2="&#xfb02;" k="27" />
<hkern u1="&#x28;" u2="&#xfb01;" k="27" />
<hkern u1="&#x28;" u2="&#xff;" k="31" />
<hkern u1="&#x28;" u2="&#xfd;" k="31" />
<hkern u1="&#x28;" u2="&#xfc;" k="47" />
<hkern u1="&#x28;" u2="&#xfb;" k="47" />
<hkern u1="&#x28;" u2="&#xfa;" k="47" />
<hkern u1="&#x28;" u2="&#xf9;" k="47" />
<hkern u1="&#x28;" u2="&#xf8;" k="55" />
<hkern u1="&#x28;" u2="&#xf6;" k="55" />
<hkern u1="&#x28;" u2="&#xf5;" k="55" />
<hkern u1="&#x28;" u2="&#xf4;" k="55" />
<hkern u1="&#x28;" u2="&#xf3;" k="55" />
<hkern u1="&#x28;" u2="&#xf2;" k="55" />
<hkern u1="&#x28;" u2="&#xf1;" k="37" />
<hkern u1="&#x28;" u2="&#xf0;" k="55" />
<hkern u1="&#x28;" u2="&#xeb;" k="55" />
<hkern u1="&#x28;" u2="&#xea;" k="55" />
<hkern u1="&#x28;" u2="&#xe9;" k="55" />
<hkern u1="&#x28;" u2="&#xe8;" k="55" />
<hkern u1="&#x28;" u2="&#xe7;" k="55" />
<hkern u1="&#x28;" u2="&#xe6;" k="39" />
<hkern u1="&#x28;" u2="&#xe5;" k="39" />
<hkern u1="&#x28;" u2="&#xe4;" k="39" />
<hkern u1="&#x28;" u2="&#xe3;" k="39" />
<hkern u1="&#x28;" u2="&#xe2;" k="39" />
<hkern u1="&#x28;" u2="&#xe1;" k="39" />
<hkern u1="&#x28;" u2="&#xe0;" k="39" />
<hkern u1="&#x28;" u2="&#xdf;" k="27" />
<hkern u1="&#x28;" u2="&#xdc;" k="25" />
<hkern u1="&#x28;" u2="&#xdb;" k="25" />
<hkern u1="&#x28;" u2="&#xda;" k="25" />
<hkern u1="&#x28;" u2="&#xd9;" k="25" />
<hkern u1="&#x28;" u2="&#xd8;" k="43" />
<hkern u1="&#x28;" u2="&#xd6;" k="43" />
<hkern u1="&#x28;" u2="&#xd5;" k="43" />
<hkern u1="&#x28;" u2="&#xd4;" k="43" />
<hkern u1="&#x28;" u2="&#xd3;" k="43" />
<hkern u1="&#x28;" u2="&#xd2;" k="43" />
<hkern u1="&#x28;" u2="&#xce;" k="-35" />
<hkern u1="&#x28;" u2="&#xc7;" k="43" />
<hkern u1="&#x28;" u2="y" k="31" />
<hkern u1="&#x28;" u2="v" k="43" />
<hkern u1="&#x28;" u2="u" k="47" />
<hkern u1="&#x28;" u2="t" k="45" />
<hkern u1="&#x28;" u2="s" k="39" />
<hkern u1="&#x28;" u2="r" k="37" />
<hkern u1="&#x28;" u2="q" k="55" />
<hkern u1="&#x28;" u2="p" k="37" />
<hkern u1="&#x28;" u2="o" k="55" />
<hkern u1="&#x28;" u2="n" k="37" />
<hkern u1="&#x28;" u2="m" k="37" />
<hkern u1="&#x28;" u2="g" k="55" />
<hkern u1="&#x28;" u2="f" k="27" />
<hkern u1="&#x28;" u2="e" k="55" />
<hkern u1="&#x28;" u2="d" k="55" />
<hkern u1="&#x28;" u2="c" k="55" />
<hkern u1="&#x28;" u2="a" k="39" />
<hkern u1="&#x28;" u2="U" k="25" />
<hkern u1="&#x28;" u2="Q" k="43" />
<hkern u1="&#x28;" u2="O" k="43" />
<hkern u1="&#x28;" u2="G" k="43" />
<hkern u1="&#x28;" u2="C" k="43" />
<hkern u1="&#x2a;" u2="&#xf8;" k="29" />
<hkern u1="&#x2a;" u2="&#xf6;" k="29" />
<hkern u1="&#x2a;" u2="&#xf5;" k="29" />
<hkern u1="&#x2a;" u2="&#xf4;" k="29" />
<hkern u1="&#x2a;" u2="&#xf3;" k="29" />
<hkern u1="&#x2a;" u2="&#xf2;" k="29" />
<hkern u1="&#x2a;" u2="&#xf0;" k="29" />
<hkern u1="&#x2a;" u2="&#xef;" k="-23" />
<hkern u1="&#x2a;" u2="&#xee;" k="-41" />
<hkern u1="&#x2a;" u2="&#xeb;" k="29" />
<hkern u1="&#x2a;" u2="&#xea;" k="29" />
<hkern u1="&#x2a;" u2="&#xe9;" k="29" />
<hkern u1="&#x2a;" u2="&#xe8;" k="29" />
<hkern u1="&#x2a;" u2="&#xe7;" k="29" />
<hkern u1="&#x2a;" u2="&#xe6;" k="20" />
<hkern u1="&#x2a;" u2="&#xe5;" k="20" />
<hkern u1="&#x2a;" u2="&#xe4;" k="20" />
<hkern u1="&#x2a;" u2="&#xe3;" k="20" />
<hkern u1="&#x2a;" u2="&#xe2;" k="20" />
<hkern u1="&#x2a;" u2="&#xe1;" k="20" />
<hkern u1="&#x2a;" u2="&#xe0;" k="20" />
<hkern u1="&#x2a;" u2="&#xc6;" k="104" />
<hkern u1="&#x2a;" u2="&#xc5;" k="84" />
<hkern u1="&#x2a;" u2="&#xc4;" k="84" />
<hkern u1="&#x2a;" u2="&#xc3;" k="84" />
<hkern u1="&#x2a;" u2="&#xc2;" k="84" />
<hkern u1="&#x2a;" u2="&#xc1;" k="84" />
<hkern u1="&#x2a;" u2="&#xc0;" k="84" />
<hkern u1="&#x2a;" u2="q" k="29" />
<hkern u1="&#x2a;" u2="o" k="29" />
<hkern u1="&#x2a;" u2="g" k="29" />
<hkern u1="&#x2a;" u2="e" k="29" />
<hkern u1="&#x2a;" u2="d" k="29" />
<hkern u1="&#x2a;" u2="c" k="29" />
<hkern u1="&#x2a;" u2="a" k="20" />
<hkern u1="&#x2a;" u2="A" k="84" />
<hkern u1="&#x2c;" u2="&#xfb04;" k="20" />
<hkern u1="&#x2c;" u2="&#xfb03;" k="20" />
<hkern u1="&#x2c;" u2="&#xfb02;" k="20" />
<hkern u1="&#x2c;" u2="&#xfb01;" k="20" />
<hkern u1="&#x2c;" u2="&#x201d;" k="256" />
<hkern u1="&#x2c;" u2="&#x201c;" k="254" />
<hkern u1="&#x2c;" u2="&#x2019;" k="244" />
<hkern u1="&#x2c;" u2="&#x2018;" k="254" />
<hkern u1="&#x2c;" u2="&#xff;" k="57" />
<hkern u1="&#x2c;" u2="&#xfd;" k="57" />
<hkern u1="&#x2c;" u2="&#xdf;" k="20" />
<hkern u1="&#x2c;" u2="&#xdd;" k="154" />
<hkern u1="&#x2c;" u2="&#xdc;" k="37" />
<hkern u1="&#x2c;" u2="&#xdb;" k="37" />
<hkern u1="&#x2c;" u2="&#xda;" k="37" />
<hkern u1="&#x2c;" u2="&#xd9;" k="37" />
<hkern u1="&#x2c;" u2="&#xd8;" k="39" />
<hkern u1="&#x2c;" u2="&#xd6;" k="39" />
<hkern u1="&#x2c;" u2="&#xd5;" k="39" />
<hkern u1="&#x2c;" u2="&#xd4;" k="39" />
<hkern u1="&#x2c;" u2="&#xd3;" k="39" />
<hkern u1="&#x2c;" u2="&#xd2;" k="39" />
<hkern u1="&#x2c;" u2="&#xc7;" k="39" />
<hkern u1="&#x2c;" u2="y" k="57" />
<hkern u1="&#x2c;" u2="v" k="63" />
<hkern u1="&#x2c;" u2="t" k="25" />
<hkern u1="&#x2c;" u2="f" k="20" />
<hkern u1="&#x2c;" u2="Y" k="154" />
<hkern u1="&#x2c;" u2="V" k="131" />
<hkern u1="&#x2c;" u2="U" k="37" />
<hkern u1="&#x2c;" u2="T" k="131" />
<hkern u1="&#x2c;" u2="Q" k="39" />
<hkern u1="&#x2c;" u2="O" k="39" />
<hkern u1="&#x2c;" u2="G" k="39" />
<hkern u1="&#x2c;" u2="C" k="39" />
<hkern u1="&#x2c;" u2="&#x27;" k="244" />
<hkern u1="&#x2c;" u2="&#x22;" k="256" />
<hkern u1="&#x2d;" u2="&#xfb04;" k="27" />
<hkern u1="&#x2d;" u2="&#xfb03;" k="27" />
<hkern u1="&#x2d;" u2="&#xfb02;" k="27" />
<hkern u1="&#x2d;" u2="&#xfb01;" k="27" />
<hkern u1="&#x2d;" u2="&#xdf;" k="27" />
<hkern u1="&#x2d;" u2="&#xdd;" k="135" />
<hkern u1="&#x2d;" u2="&#xc5;" k="29" />
<hkern u1="&#x2d;" u2="&#xc4;" k="29" />
<hkern u1="&#x2d;" u2="&#xc3;" k="29" />
<hkern u1="&#x2d;" u2="&#xc2;" k="29" />
<hkern u1="&#x2d;" u2="&#xc1;" k="29" />
<hkern u1="&#x2d;" u2="&#xc0;" k="29" />
<hkern u1="&#x2d;" u2="x" k="45" />
<hkern u1="&#x2d;" u2="v" k="16" />
<hkern u1="&#x2d;" u2="t" k="25" />
<hkern u1="&#x2d;" u2="f" k="27" />
<hkern u1="&#x2d;" u2="Y" k="135" />
<hkern u1="&#x2d;" u2="X" k="63" />
<hkern u1="&#x2d;" u2="V" k="68" />
<hkern u1="&#x2d;" u2="T" k="131" />
<hkern u1="&#x2d;" u2="S" k="39" />
<hkern u1="&#x2d;" u2="A" k="29" />
<hkern u1="&#x2e;" u2="&#xfb04;" k="20" />
<hkern u1="&#x2e;" u2="&#xfb03;" k="20" />
<hkern u1="&#x2e;" u2="&#xfb02;" k="20" />
<hkern u1="&#x2e;" u2="&#xfb01;" k="20" />
<hkern u1="&#x2e;" u2="&#x201d;" k="256" />
<hkern u1="&#x2e;" u2="&#x201c;" k="254" />
<hkern u1="&#x2e;" u2="&#x2019;" k="244" />
<hkern u1="&#x2e;" u2="&#x2018;" k="254" />
<hkern u1="&#x2e;" u2="&#xff;" k="57" />
<hkern u1="&#x2e;" u2="&#xfd;" k="57" />
<hkern u1="&#x2e;" u2="&#xdf;" k="20" />
<hkern u1="&#x2e;" u2="&#xdd;" k="154" />
<hkern u1="&#x2e;" u2="&#xdc;" k="37" />
<hkern u1="&#x2e;" u2="&#xdb;" k="37" />
<hkern u1="&#x2e;" u2="&#xda;" k="37" />
<hkern u1="&#x2e;" u2="&#xd9;" k="37" />
<hkern u1="&#x2e;" u2="&#xd8;" k="39" />
<hkern u1="&#x2e;" u2="&#xd6;" k="39" />
<hkern u1="&#x2e;" u2="&#xd5;" k="39" />
<hkern u1="&#x2e;" u2="&#xd4;" k="39" />
<hkern u1="&#x2e;" u2="&#xd3;" k="39" />
<hkern u1="&#x2e;" u2="&#xd2;" k="39" />
<hkern u1="&#x2e;" u2="&#xc7;" k="39" />
<hkern u1="&#x2e;" u2="y" k="57" />
<hkern u1="&#x2e;" u2="v" k="63" />
<hkern u1="&#x2e;" u2="t" k="25" />
<hkern u1="&#x2e;" u2="f" k="20" />
<hkern u1="&#x2e;" u2="Y" k="154" />
<hkern u1="&#x2e;" u2="V" k="131" />
<hkern u1="&#x2e;" u2="U" k="37" />
<hkern u1="&#x2e;" u2="T" k="131" />
<hkern u1="&#x2e;" u2="Q" k="39" />
<hkern u1="&#x2e;" u2="O" k="39" />
<hkern u1="&#x2e;" u2="G" k="39" />
<hkern u1="&#x2e;" u2="C" k="39" />
<hkern u1="&#x2e;" u2="&#x27;" k="244" />
<hkern u1="&#x2e;" u2="&#x22;" k="256" />
<hkern u1="&#x2f;" u2="&#xfc;" k="29" />
<hkern u1="&#x2f;" u2="&#xfb;" k="29" />
<hkern u1="&#x2f;" u2="&#xfa;" k="29" />
<hkern u1="&#x2f;" u2="&#xf9;" k="29" />
<hkern u1="&#x2f;" u2="&#xf8;" k="45" />
<hkern u1="&#x2f;" u2="&#xf6;" k="45" />
<hkern u1="&#x2f;" u2="&#xf5;" k="45" />
<hkern u1="&#x2f;" u2="&#xf4;" k="45" />
<hkern u1="&#x2f;" u2="&#xf3;" k="45" />
<hkern u1="&#x2f;" u2="&#xf2;" k="45" />
<hkern u1="&#x2f;" u2="&#xf1;" k="31" />
<hkern u1="&#x2f;" u2="&#xf0;" k="45" />
<hkern u1="&#x2f;" u2="&#xeb;" k="45" />
<hkern u1="&#x2f;" u2="&#xea;" k="45" />
<hkern u1="&#x2f;" u2="&#xe9;" k="45" />
<hkern u1="&#x2f;" u2="&#xe8;" k="45" />
<hkern u1="&#x2f;" u2="&#xe7;" k="45" />
<hkern u1="&#x2f;" u2="&#xe6;" k="41" />
<hkern u1="&#x2f;" u2="&#xe5;" k="41" />
<hkern u1="&#x2f;" u2="&#xe4;" k="41" />
<hkern u1="&#x2f;" u2="&#xe3;" k="41" />
<hkern u1="&#x2f;" u2="&#xe2;" k="41" />
<hkern u1="&#x2f;" u2="&#xe1;" k="41" />
<hkern u1="&#x2f;" u2="&#xe0;" k="41" />
<hkern u1="&#x2f;" u2="&#xc6;" k="35" />
<hkern u1="&#x2f;" u2="&#xc5;" k="66" />
<hkern u1="&#x2f;" u2="&#xc4;" k="66" />
<hkern u1="&#x2f;" u2="&#xc3;" k="66" />
<hkern u1="&#x2f;" u2="&#xc2;" k="66" />
<hkern u1="&#x2f;" u2="&#xc1;" k="66" />
<hkern u1="&#x2f;" u2="&#xc0;" k="66" />
<hkern u1="&#x2f;" u2="u" k="29" />
<hkern u1="&#x2f;" u2="s" k="35" />
<hkern u1="&#x2f;" u2="r" k="31" />
<hkern u1="&#x2f;" u2="q" k="45" />
<hkern u1="&#x2f;" u2="p" k="31" />
<hkern u1="&#x2f;" u2="o" k="45" />
<hkern u1="&#x2f;" u2="n" k="31" />
<hkern u1="&#x2f;" u2="m" k="31" />
<hkern u1="&#x2f;" u2="g" k="45" />
<hkern u1="&#x2f;" u2="e" k="45" />
<hkern u1="&#x2f;" u2="d" k="45" />
<hkern u1="&#x2f;" u2="c" k="45" />
<hkern u1="&#x2f;" u2="a" k="41" />
<hkern u1="&#x2f;" u2="A" k="66" />
<hkern u1="&#x2f;" u2="&#x2f;" k="303" />
<hkern u1="&#x3a;" u2="&#xdd;" k="53" />
<hkern u1="&#x3a;" u2="Y" k="53" />
<hkern u1="&#x3a;" u2="V" k="18" />
<hkern u1="&#x3a;" u2="T" k="86" />
<hkern u1="&#x3b;" u2="&#xdd;" k="53" />
<hkern u1="&#x3b;" u2="Y" k="53" />
<hkern u1="&#x3b;" u2="V" k="18" />
<hkern u1="&#x3b;" u2="T" k="86" />
<hkern u1="&#x40;" u2="&#xdd;" k="49" />
<hkern u1="&#x40;" u2="Y" k="49" />
<hkern u1="&#x40;" u2="T" k="37" />
<hkern u1="A" u2="&#x2122;" k="90" />
<hkern u1="A" u2="&#xae;" k="20" />
<hkern u1="A" u2="&#x7d;" k="25" />
<hkern u1="A" u2="v" k="74" />
<hkern u1="A" u2="]" k="31" />
<hkern u1="A" u2="\" k="66" />
<hkern u1="A" u2="X" k="-49" />
<hkern u1="A" u2="V" k="90" />
<hkern u1="A" u2="&#x3f;" k="37" />
<hkern u1="A" u2="&#x2a;" k="84" />
<hkern u1="B" u2="&#xfb04;" k="23" />
<hkern u1="B" u2="&#xfb03;" k="23" />
<hkern u1="B" u2="&#xfb02;" k="23" />
<hkern u1="B" u2="&#xfb01;" k="23" />
<hkern u1="B" u2="&#xff;" k="20" />
<hkern u1="B" u2="&#xfe;" k="14" />
<hkern u1="B" u2="&#xfd;" k="20" />
<hkern u1="B" u2="&#xfc;" k="14" />
<hkern u1="B" u2="&#xfb;" k="14" />
<hkern u1="B" u2="&#xfa;" k="14" />
<hkern u1="B" u2="&#xf9;" k="14" />
<hkern u1="B" u2="&#xf8;" k="10" />
<hkern u1="B" u2="&#xf6;" k="10" />
<hkern u1="B" u2="&#xf5;" k="10" />
<hkern u1="B" u2="&#xf4;" k="10" />
<hkern u1="B" u2="&#xf3;" k="10" />
<hkern u1="B" u2="&#xf2;" k="10" />
<hkern u1="B" u2="&#xf1;" k="14" />
<hkern u1="B" u2="&#xf0;" k="10" />
<hkern u1="B" u2="&#xef;" k="16" />
<hkern u1="B" u2="&#xee;" k="16" />
<hkern u1="B" u2="&#xed;" k="16" />
<hkern u1="B" u2="&#xec;" k="16" />
<hkern u1="B" u2="&#xeb;" k="10" />
<hkern u1="B" u2="&#xea;" k="10" />
<hkern u1="B" u2="&#xe9;" k="10" />
<hkern u1="B" u2="&#xe8;" k="10" />
<hkern u1="B" u2="&#xe7;" k="10" />
<hkern u1="B" u2="&#xdf;" k="23" />
<hkern u1="B" u2="&#xdd;" k="59" />
<hkern u1="B" u2="&#xc6;" k="16" />
<hkern u1="B" u2="&#x7d;" k="29" />
<hkern u1="B" u2="y" k="20" />
<hkern u1="B" u2="x" k="37" />
<hkern u1="B" u2="v" k="27" />
<hkern u1="B" u2="u" k="14" />
<hkern u1="B" u2="t" k="29" />
<hkern u1="B" u2="s" k="18" />
<hkern u1="B" u2="r" k="14" />
<hkern u1="B" u2="q" k="10" />
<hkern u1="B" u2="p" k="14" />
<hkern u1="B" u2="o" k="10" />
<hkern u1="B" u2="n" k="14" />
<hkern u1="B" u2="m" k="14" />
<hkern u1="B" u2="k" k="14" />
<hkern u1="B" u2="j" k="16" />
<hkern u1="B" u2="i" k="16" />
<hkern u1="B" u2="h" k="14" />
<hkern u1="B" u2="g" k="10" />
<hkern u1="B" u2="f" k="23" />
<hkern u1="B" u2="e" k="10" />
<hkern u1="B" u2="d" k="10" />
<hkern u1="B" u2="c" k="10" />
<hkern u1="B" u2="b" k="14" />
<hkern u1="B" u2="]" k="29" />
<hkern u1="B" u2="Y" k="59" />
<hkern u1="B" u2="X" k="35" />
<hkern u1="B" u2="V" k="25" />
<hkern u1="B" u2="T" k="86" />
<hkern u1="B" u2="&#x29;" k="33" />
<hkern u1="C" u2="&#xfb04;" k="14" />
<hkern u1="C" u2="&#xfb03;" k="14" />
<hkern u1="C" u2="&#xfb02;" k="14" />
<hkern u1="C" u2="&#xfb01;" k="14" />
<hkern u1="C" u2="&#x2014;" k="45" />
<hkern u1="C" u2="&#x2013;" k="45" />
<hkern u1="C" u2="&#xff;" k="94" />
<hkern u1="C" u2="&#xfd;" k="94" />
<hkern u1="C" u2="&#xfc;" k="23" />
<hkern u1="C" u2="&#xfb;" k="23" />
<hkern u1="C" u2="&#xfa;" k="23" />
<hkern u1="C" u2="&#xf9;" k="23" />
<hkern u1="C" u2="&#xf8;" k="33" />
<hkern u1="C" u2="&#xf6;" k="33" />
<hkern u1="C" u2="&#xf5;" k="33" />
<hkern u1="C" u2="&#xf4;" k="33" />
<hkern u1="C" u2="&#xf3;" k="33" />
<hkern u1="C" u2="&#xf2;" k="33" />
<hkern u1="C" u2="&#xf1;" k="14" />
<hkern u1="C" u2="&#xf0;" k="33" />
<hkern u1="C" u2="&#xeb;" k="33" />
<hkern u1="C" u2="&#xea;" k="33" />
<hkern u1="C" u2="&#xe9;" k="33" />
<hkern u1="C" u2="&#xe8;" k="33" />
<hkern u1="C" u2="&#xe7;" k="33" />
<hkern u1="C" u2="&#xdf;" k="14" />
<hkern u1="C" u2="&#xd8;" k="18" />
<hkern u1="C" u2="&#xd6;" k="18" />
<hkern u1="C" u2="&#xd5;" k="18" />
<hkern u1="C" u2="&#xd4;" k="18" />
<hkern u1="C" u2="&#xd3;" k="18" />
<hkern u1="C" u2="&#xd2;" k="18" />
<hkern u1="C" u2="&#xc7;" k="18" />
<hkern u1="C" u2="y" k="94" />
<hkern u1="C" u2="v" k="102" />
<hkern u1="C" u2="u" k="23" />
<hkern u1="C" u2="t" k="41" />
<hkern u1="C" u2="s" k="20" />
<hkern u1="C" u2="r" k="14" />
<hkern u1="C" u2="q" k="33" />
<hkern u1="C" u2="p" k="14" />
<hkern u1="C" u2="o" k="33" />
<hkern u1="C" u2="n" k="14" />
<hkern u1="C" u2="m" k="14" />
<hkern u1="C" u2="g" k="33" />
<hkern u1="C" u2="f" k="14" />
<hkern u1="C" u2="e" k="33" />
<hkern u1="C" u2="d" k="33" />
<hkern u1="C" u2="c" k="33" />
<hkern u1="C" u2="Q" k="18" />
<hkern u1="C" u2="O" k="18" />
<hkern u1="C" u2="G" k="18" />
<hkern u1="C" u2="C" k="18" />
<hkern u1="C" u2="&#x2d;" k="45" />
<hkern u1="C" u2="&#x26;" k="14" />
<hkern u1="D" u2="&#x2122;" k="16" />
<hkern u1="D" u2="&#x2026;" k="39" />
<hkern u1="D" u2="&#x201e;" k="39" />
<hkern u1="D" u2="&#x201a;" k="39" />
<hkern u1="D" u2="&#xfe;" k="18" />
<hkern u1="D" u2="&#xfc;" k="16" />
<hkern u1="D" u2="&#xfb;" k="16" />
<hkern u1="D" u2="&#xfa;" k="16" />
<hkern u1="D" u2="&#xf9;" k="16" />
<hkern u1="D" u2="&#xf8;" k="12" />
<hkern u1="D" u2="&#xf6;" k="12" />
<hkern u1="D" u2="&#xf5;" k="12" />
<hkern u1="D" u2="&#xf4;" k="12" />
<hkern u1="D" u2="&#xf3;" k="12" />
<hkern u1="D" u2="&#xf2;" k="12" />
<hkern u1="D" u2="&#xf1;" k="18" />
<hkern u1="D" u2="&#xf0;" k="12" />
<hkern u1="D" u2="&#xef;" k="20" />
<hkern u1="D" u2="&#xee;" k="20" />
<hkern u1="D" u2="&#xed;" k="20" />
<hkern u1="D" u2="&#xec;" k="20" />
<hkern u1="D" u2="&#xeb;" k="12" />
<hkern u1="D" u2="&#xea;" k="12" />
<hkern u1="D" u2="&#xe9;" k="12" />
<hkern u1="D" u2="&#xe8;" k="12" />
<hkern u1="D" u2="&#xe7;" k="12" />
<hkern u1="D" u2="&#xe6;" k="20" />
<hkern u1="D" u2="&#xe5;" k="20" />
<hkern u1="D" u2="&#xe4;" k="20" />
<hkern u1="D" u2="&#xe3;" k="20" />
<hkern u1="D" u2="&#xe2;" k="20" />
<hkern u1="D" u2="&#xe1;" k="20" />
<hkern u1="D" u2="&#xe0;" k="20" />
<hkern u1="D" u2="&#xdd;" k="68" />
<hkern u1="D" u2="&#xc6;" k="96" />
<hkern u1="D" u2="&#xc5;" k="31" />
<hkern u1="D" u2="&#xc4;" k="31" />
<hkern u1="D" u2="&#xc3;" k="31" />
<hkern u1="D" u2="&#xc2;" k="31" />
<hkern u1="D" u2="&#xc1;" k="31" />
<hkern u1="D" u2="&#xc0;" k="31" />
<hkern u1="D" u2="&#x7d;" k="37" />
<hkern u1="D" u2="x" k="27" />
<hkern u1="D" u2="u" k="16" />
<hkern u1="D" u2="s" k="12" />
<hkern u1="D" u2="r" k="18" />
<hkern u1="D" u2="q" k="12" />
<hkern u1="D" u2="p" k="18" />
<hkern u1="D" u2="o" k="12" />
<hkern u1="D" u2="n" k="18" />
<hkern u1="D" u2="m" k="18" />
<hkern u1="D" u2="k" k="18" />
<hkern u1="D" u2="j" k="20" />
<hkern u1="D" u2="i" k="20" />
<hkern u1="D" u2="h" k="18" />
<hkern u1="D" u2="g" k="12" />
<hkern u1="D" u2="e" k="12" />
<hkern u1="D" u2="d" k="12" />
<hkern u1="D" u2="c" k="12" />
<hkern u1="D" u2="b" k="18" />
<hkern u1="D" u2="a" k="20" />
<hkern u1="D" u2="]" k="37" />
<hkern u1="D" u2="Y" k="68" />
<hkern u1="D" u2="X" k="57" />
<hkern u1="D" u2="V" k="29" />
<hkern u1="D" u2="T" k="57" />
<hkern u1="D" u2="A" k="31" />
<hkern u1="D" u2="&#x2f;" k="20" />
<hkern u1="D" u2="&#x2e;" k="39" />
<hkern u1="D" u2="&#x2c;" k="39" />
<hkern u1="D" u2="&#x29;" k="43" />
<hkern u1="E" u2="&#xfb04;" k="23" />
<hkern u1="E" u2="&#xfb03;" k="23" />
<hkern u1="E" u2="&#xfb02;" k="23" />
<hkern u1="E" u2="&#xfb01;" k="23" />
<hkern u1="E" u2="&#xff;" k="29" />
<hkern u1="E" u2="&#xfd;" k="29" />
<hkern u1="E" u2="&#xfc;" k="27" />
<hkern u1="E" u2="&#xfb;" k="27" />
<hkern u1="E" u2="&#xfa;" k="27" />
<hkern u1="E" u2="&#xf9;" k="27" />
<hkern u1="E" u2="&#xf8;" k="29" />
<hkern u1="E" u2="&#xf6;" k="29" />
<hkern u1="E" u2="&#xf5;" k="29" />
<hkern u1="E" u2="&#xf4;" k="29" />
<hkern u1="E" u2="&#xf3;" k="29" />
<hkern u1="E" u2="&#xf2;" k="29" />
<hkern u1="E" u2="&#xf1;" k="14" />
<hkern u1="E" u2="&#xf0;" k="29" />
<hkern u1="E" u2="&#xeb;" k="29" />
<hkern u1="E" u2="&#xea;" k="29" />
<hkern u1="E" u2="&#xe9;" k="29" />
<hkern u1="E" u2="&#xe8;" k="29" />
<hkern u1="E" u2="&#xe7;" k="29" />
<hkern u1="E" u2="&#xe6;" k="14" />
<hkern u1="E" u2="&#xe5;" k="14" />
<hkern u1="E" u2="&#xe4;" k="14" />
<hkern u1="E" u2="&#xe3;" k="14" />
<hkern u1="E" u2="&#xe2;" k="14" />
<hkern u1="E" u2="&#xe1;" k="14" />
<hkern u1="E" u2="&#xe0;" k="14" />
<hkern u1="E" u2="&#xdf;" k="23" />
<hkern u1="E" u2="&#xd8;" k="14" />
<hkern u1="E" u2="&#xd6;" k="14" />
<hkern u1="E" u2="&#xd5;" k="14" />
<hkern u1="E" u2="&#xd4;" k="14" />
<hkern u1="E" u2="&#xd3;" k="14" />
<hkern u1="E" u2="&#xd2;" k="14" />
<hkern u1="E" u2="&#xc7;" k="14" />
<hkern u1="E" u2="y" k="29" />
<hkern u1="E" u2="v" k="33" />
<hkern u1="E" u2="u" k="27" />
<hkern u1="E" u2="t" k="35" />
<hkern u1="E" u2="s" k="12" />
<hkern u1="E" u2="r" k="14" />
<hkern u1="E" u2="q" k="29" />
<hkern u1="E" u2="p" k="14" />
<hkern u1="E" u2="o" k="29" />
<hkern u1="E" u2="n" k="14" />
<hkern u1="E" u2="m" k="14" />
<hkern u1="E" u2="g" k="29" />
<hkern u1="E" u2="f" k="23" />
<hkern u1="E" u2="e" k="29" />
<hkern u1="E" u2="d" k="29" />
<hkern u1="E" u2="c" k="29" />
<hkern u1="E" u2="a" k="14" />
<hkern u1="E" u2="Q" k="14" />
<hkern u1="E" u2="O" k="14" />
<hkern u1="E" u2="G" k="14" />
<hkern u1="E" u2="C" k="14" />
<hkern u1="E" u2="&#x26;" k="14" />
<hkern u1="F" u2="&#xfb04;" k="37" />
<hkern u1="F" u2="&#xfb03;" k="37" />
<hkern u1="F" u2="&#xfb02;" k="37" />
<hkern u1="F" u2="&#xfb01;" k="37" />
<hkern u1="F" u2="&#x203a;" k="27" />
<hkern u1="F" u2="&#x2026;" k="145" />
<hkern u1="F" u2="&#x201e;" k="145" />
<hkern u1="F" u2="&#x201a;" k="145" />
<hkern u1="F" u2="&#xff;" k="27" />
<hkern u1="F" u2="&#xfe;" k="14" />
<hkern u1="F" u2="&#xfd;" k="27" />
<hkern u1="F" u2="&#xfc;" k="49" />
<hkern u1="F" u2="&#xfb;" k="49" />
<hkern u1="F" u2="&#xfa;" k="49" />
<hkern u1="F" u2="&#xf9;" k="49" />
<hkern u1="F" u2="&#xf8;" k="41" />
<hkern u1="F" u2="&#xf6;" k="41" />
<hkern u1="F" u2="&#xf5;" k="41" />
<hkern u1="F" u2="&#xf4;" k="41" />
<hkern u1="F" u2="&#xf3;" k="41" />
<hkern u1="F" u2="&#xf2;" k="41" />
<hkern u1="F" u2="&#xf1;" k="55" />
<hkern u1="F" u2="&#xf0;" k="41" />
<hkern u1="F" u2="&#xef;" k="23" />
<hkern u1="F" u2="&#xee;" k="23" />
<hkern u1="F" u2="&#xed;" k="23" />
<hkern u1="F" u2="&#xec;" k="23" />
<hkern u1="F" u2="&#xeb;" k="41" />
<hkern u1="F" u2="&#xea;" k="41" />
<hkern u1="F" u2="&#xe9;" k="41" />
<hkern u1="F" u2="&#xe8;" k="41" />
<hkern u1="F" u2="&#xe7;" k="41" />
<hkern u1="F" u2="&#xe6;" k="156" />
<hkern u1="F" u2="&#xe5;" k="156" />
<hkern u1="F" u2="&#xe4;" k="156" />
<hkern u1="F" u2="&#xe3;" k="156" />
<hkern u1="F" u2="&#xe2;" k="156" />
<hkern u1="F" u2="&#xe1;" k="156" />
<hkern u1="F" u2="&#xe0;" k="156" />
<hkern u1="F" u2="&#xdf;" k="37" />
<hkern u1="F" u2="&#xc6;" k="162" />
<hkern u1="F" u2="&#xc5;" k="106" />
<hkern u1="F" u2="&#xc4;" k="106" />
<hkern u1="F" u2="&#xc3;" k="106" />
<hkern u1="F" u2="&#xc2;" k="106" />
<hkern u1="F" u2="&#xc1;" k="106" />
<hkern u1="F" u2="&#xc0;" k="106" />
<hkern u1="F" u2="&#xbb;" k="27" />
<hkern u1="F" u2="y" k="27" />
<hkern u1="F" u2="x" k="88" />
<hkern u1="F" u2="v" k="31" />
<hkern u1="F" u2="u" k="49" />
<hkern u1="F" u2="t" k="55" />
<hkern u1="F" u2="s" k="41" />
<hkern u1="F" u2="r" k="55" />
<hkern u1="F" u2="q" k="41" />
<hkern u1="F" u2="p" k="55" />
<hkern u1="F" u2="o" k="41" />
<hkern u1="F" u2="n" k="55" />
<hkern u1="F" u2="m" k="55" />
<hkern u1="F" u2="k" k="14" />
<hkern u1="F" u2="j" k="23" />
<hkern u1="F" u2="i" k="23" />
<hkern u1="F" u2="h" k="14" />
<hkern u1="F" u2="g" k="41" />
<hkern u1="F" u2="f" k="37" />
<hkern u1="F" u2="e" k="41" />
<hkern u1="F" u2="d" k="41" />
<hkern u1="F" u2="c" k="41" />
<hkern u1="F" u2="b" k="14" />
<hkern u1="F" u2="a" k="156" />
<hkern u1="F" u2="A" k="106" />
<hkern u1="F" u2="&#x2f;" k="51" />
<hkern u1="F" u2="&#x2e;" k="145" />
<hkern u1="F" u2="&#x2c;" k="145" />
<hkern u1="F" u2="&#x26;" k="47" />
<hkern u1="H" u2="&#xfb04;" k="16" />
<hkern u1="H" u2="&#xfb03;" k="16" />
<hkern u1="H" u2="&#xfb02;" k="16" />
<hkern u1="H" u2="&#xfb01;" k="16" />
<hkern u1="H" u2="&#xff;" k="14" />
<hkern u1="H" u2="&#xfe;" k="20" />
<hkern u1="H" u2="&#xfd;" k="14" />
<hkern u1="H" u2="&#xfc;" k="27" />
<hkern u1="H" u2="&#xfb;" k="27" />
<hkern u1="H" u2="&#xfa;" k="27" />
<hkern u1="H" u2="&#xf9;" k="27" />
<hkern u1="H" u2="&#xf8;" k="29" />
<hkern u1="H" u2="&#xf6;" k="29" />
<hkern u1="H" u2="&#xf5;" k="29" />
<hkern u1="H" u2="&#xf4;" k="29" />
<hkern u1="H" u2="&#xf3;" k="29" />
<hkern u1="H" u2="&#xf2;" k="29" />
<hkern u1="H" u2="&#xf1;" k="20" />
<hkern u1="H" u2="&#xf0;" k="29" />
<hkern u1="H" u2="&#xef;" k="20" />
<hkern u1="H" u2="&#xee;" k="20" />
<hkern u1="H" u2="&#xed;" k="20" />
<hkern u1="H" u2="&#xec;" k="20" />
<hkern u1="H" u2="&#xeb;" k="29" />
<hkern u1="H" u2="&#xea;" k="29" />
<hkern u1="H" u2="&#xe9;" k="29" />
<hkern u1="H" u2="&#xe8;" k="29" />
<hkern u1="H" u2="&#xe7;" k="29" />
<hkern u1="H" u2="&#xe6;" k="23" />
<hkern u1="H" u2="&#xe5;" k="23" />
<hkern u1="H" u2="&#xe4;" k="23" />
<hkern u1="H" u2="&#xe3;" k="23" />
<hkern u1="H" u2="&#xe2;" k="23" />
<hkern u1="H" u2="&#xe1;" k="23" />
<hkern u1="H" u2="&#xe0;" k="23" />
<hkern u1="H" u2="&#xdf;" k="16" />
<hkern u1="H" u2="&#xdd;" k="10" />
<hkern u1="H" u2="y" k="14" />
<hkern u1="H" u2="v" k="18" />
<hkern u1="H" u2="u" k="27" />
<hkern u1="H" u2="t" k="23" />
<hkern u1="H" u2="s" k="23" />
<hkern u1="H" u2="r" k="20" />
<hkern u1="H" u2="q" k="29" />
<hkern u1="H" u2="p" k="20" />
<hkern u1="H" u2="o" k="29" />
<hkern u1="H" u2="n" k="20" />
<hkern u1="H" u2="m" k="20" />
<hkern u1="H" u2="k" k="20" />
<hkern u1="H" u2="j" k="20" />
<hkern u1="H" u2="i" k="20" />
<hkern u1="H" u2="h" k="20" />
<hkern u1="H" u2="g" k="29" />
<hkern u1="H" u2="f" k="16" />
<hkern u1="H" u2="e" k="29" />
<hkern u1="H" u2="d" k="29" />
<hkern u1="H" u2="c" k="29" />
<hkern u1="H" u2="b" k="20" />
<hkern u1="H" u2="a" k="23" />
<hkern u1="H" u2="Y" k="10" />
<hkern u1="I" u2="&#xfb04;" k="16" />
<hkern u1="I" u2="&#xfb03;" k="16" />
<hkern u1="I" u2="&#xfb02;" k="16" />
<hkern u1="I" u2="&#xfb01;" k="16" />
<hkern u1="I" u2="&#xff;" k="14" />
<hkern u1="I" u2="&#xfe;" k="20" />
<hkern u1="I" u2="&#xfd;" k="14" />
<hkern u1="I" u2="&#xfc;" k="27" />
<hkern u1="I" u2="&#xfb;" k="27" />
<hkern u1="I" u2="&#xfa;" k="27" />
<hkern u1="I" u2="&#xf9;" k="27" />
<hkern u1="I" u2="&#xf8;" k="29" />
<hkern u1="I" u2="&#xf6;" k="29" />
<hkern u1="I" u2="&#xf5;" k="29" />
<hkern u1="I" u2="&#xf4;" k="29" />
<hkern u1="I" u2="&#xf3;" k="29" />
<hkern u1="I" u2="&#xf2;" k="29" />
<hkern u1="I" u2="&#xf1;" k="20" />
<hkern u1="I" u2="&#xf0;" k="29" />
<hkern u1="I" u2="&#xef;" k="20" />
<hkern u1="I" u2="&#xee;" k="20" />
<hkern u1="I" u2="&#xed;" k="20" />
<hkern u1="I" u2="&#xec;" k="20" />
<hkern u1="I" u2="&#xeb;" k="29" />
<hkern u1="I" u2="&#xea;" k="29" />
<hkern u1="I" u2="&#xe9;" k="29" />
<hkern u1="I" u2="&#xe8;" k="29" />
<hkern u1="I" u2="&#xe7;" k="29" />
<hkern u1="I" u2="&#xe6;" k="23" />
<hkern u1="I" u2="&#xe5;" k="23" />
<hkern u1="I" u2="&#xe4;" k="23" />
<hkern u1="I" u2="&#xe3;" k="23" />
<hkern u1="I" u2="&#xe2;" k="23" />
<hkern u1="I" u2="&#xe1;" k="23" />
<hkern u1="I" u2="&#xe0;" k="23" />
<hkern u1="I" u2="&#xdf;" k="16" />
<hkern u1="I" u2="&#xdd;" k="10" />
<hkern u1="I" u2="y" k="14" />
<hkern u1="I" u2="v" k="18" />
<hkern u1="I" u2="u" k="27" />
<hkern u1="I" u2="t" k="23" />
<hkern u1="I" u2="s" k="23" />
<hkern u1="I" u2="r" k="20" />
<hkern u1="I" u2="q" k="29" />
<hkern u1="I" u2="p" k="20" />
<hkern u1="I" u2="o" k="29" />
<hkern u1="I" u2="n" k="20" />
<hkern u1="I" u2="m" k="20" />
<hkern u1="I" u2="k" k="20" />
<hkern u1="I" u2="j" k="20" />
<hkern u1="I" u2="i" k="20" />
<hkern u1="I" u2="h" k="20" />
<hkern u1="I" u2="g" k="29" />
<hkern u1="I" u2="f" k="16" />
<hkern u1="I" u2="e" k="29" />
<hkern u1="I" u2="d" k="29" />
<hkern u1="I" u2="c" k="29" />
<hkern u1="I" u2="b" k="20" />
<hkern u1="I" u2="a" k="23" />
<hkern u1="I" u2="Y" k="10" />
<hkern u1="M" u2="&#xfb04;" k="16" />
<hkern u1="M" u2="&#xfb03;" k="16" />
<hkern u1="M" u2="&#xfb02;" k="16" />
<hkern u1="M" u2="&#xfb01;" k="16" />
<hkern u1="M" u2="&#xff;" k="14" />
<hkern u1="M" u2="&#xfe;" k="20" />
<hkern u1="M" u2="&#xfd;" k="14" />
<hkern u1="M" u2="&#xfc;" k="27" />
<hkern u1="M" u2="&#xfb;" k="27" />
<hkern u1="M" u2="&#xfa;" k="27" />
<hkern u1="M" u2="&#xf9;" k="27" />
<hkern u1="M" u2="&#xf8;" k="29" />
<hkern u1="M" u2="&#xf6;" k="29" />
<hkern u1="M" u2="&#xf5;" k="29" />
<hkern u1="M" u2="&#xf4;" k="29" />
<hkern u1="M" u2="&#xf3;" k="29" />
<hkern u1="M" u2="&#xf2;" k="29" />
<hkern u1="M" u2="&#xf1;" k="20" />
<hkern u1="M" u2="&#xf0;" k="29" />
<hkern u1="M" u2="&#xef;" k="20" />
<hkern u1="M" u2="&#xee;" k="20" />
<hkern u1="M" u2="&#xed;" k="20" />
<hkern u1="M" u2="&#xec;" k="20" />
<hkern u1="M" u2="&#xeb;" k="29" />
<hkern u1="M" u2="&#xea;" k="29" />
<hkern u1="M" u2="&#xe9;" k="29" />
<hkern u1="M" u2="&#xe8;" k="29" />
<hkern u1="M" u2="&#xe7;" k="29" />
<hkern u1="M" u2="&#xe6;" k="23" />
<hkern u1="M" u2="&#xe5;" k="23" />
<hkern u1="M" u2="&#xe4;" k="23" />
<hkern u1="M" u2="&#xe3;" k="23" />
<hkern u1="M" u2="&#xe2;" k="23" />
<hkern u1="M" u2="&#xe1;" k="23" />
<hkern u1="M" u2="&#xe0;" k="23" />
<hkern u1="M" u2="&#xdf;" k="16" />
<hkern u1="M" u2="&#xdd;" k="10" />
<hkern u1="M" u2="y" k="14" />
<hkern u1="M" u2="v" k="18" />
<hkern u1="M" u2="u" k="27" />
<hkern u1="M" u2="t" k="23" />
<hkern u1="M" u2="s" k="23" />
<hkern u1="M" u2="r" k="20" />
<hkern u1="M" u2="q" k="29" />
<hkern u1="M" u2="p" k="20" />
<hkern u1="M" u2="o" k="29" />
<hkern u1="M" u2="n" k="20" />
<hkern u1="M" u2="m" k="20" />
<hkern u1="M" u2="k" k="20" />
<hkern u1="M" u2="j" k="20" />
<hkern u1="M" u2="i" k="20" />
<hkern u1="M" u2="h" k="20" />
<hkern u1="M" u2="g" k="29" />
<hkern u1="M" u2="f" k="16" />
<hkern u1="M" u2="e" k="29" />
<hkern u1="M" u2="d" k="29" />
<hkern u1="M" u2="c" k="29" />
<hkern u1="M" u2="b" k="20" />
<hkern u1="M" u2="a" k="23" />
<hkern u1="M" u2="Y" k="10" />
<hkern u1="N" u2="&#xfb04;" k="16" />
<hkern u1="N" u2="&#xfb03;" k="16" />
<hkern u1="N" u2="&#xfb02;" k="16" />
<hkern u1="N" u2="&#xfb01;" k="16" />
<hkern u1="N" u2="&#xff;" k="14" />
<hkern u1="N" u2="&#xfe;" k="20" />
<hkern u1="N" u2="&#xfd;" k="14" />
<hkern u1="N" u2="&#xfc;" k="27" />
<hkern u1="N" u2="&#xfb;" k="27" />
<hkern u1="N" u2="&#xfa;" k="27" />
<hkern u1="N" u2="&#xf9;" k="27" />
<hkern u1="N" u2="&#xf8;" k="29" />
<hkern u1="N" u2="&#xf6;" k="29" />
<hkern u1="N" u2="&#xf5;" k="29" />
<hkern u1="N" u2="&#xf4;" k="29" />
<hkern u1="N" u2="&#xf3;" k="29" />
<hkern u1="N" u2="&#xf2;" k="29" />
<hkern u1="N" u2="&#xf1;" k="20" />
<hkern u1="N" u2="&#xf0;" k="29" />
<hkern u1="N" u2="&#xef;" k="20" />
<hkern u1="N" u2="&#xee;" k="20" />
<hkern u1="N" u2="&#xed;" k="20" />
<hkern u1="N" u2="&#xec;" k="20" />
<hkern u1="N" u2="&#xeb;" k="29" />
<hkern u1="N" u2="&#xea;" k="29" />
<hkern u1="N" u2="&#xe9;" k="29" />
<hkern u1="N" u2="&#xe8;" k="29" />
<hkern u1="N" u2="&#xe7;" k="29" />
<hkern u1="N" u2="&#xe6;" k="23" />
<hkern u1="N" u2="&#xe5;" k="23" />
<hkern u1="N" u2="&#xe4;" k="23" />
<hkern u1="N" u2="&#xe3;" k="23" />
<hkern u1="N" u2="&#xe2;" k="23" />
<hkern u1="N" u2="&#xe1;" k="23" />
<hkern u1="N" u2="&#xe0;" k="23" />
<hkern u1="N" u2="&#xdf;" k="16" />
<hkern u1="N" u2="&#xdd;" k="10" />
<hkern u1="N" u2="y" k="14" />
<hkern u1="N" u2="v" k="18" />
<hkern u1="N" u2="u" k="27" />
<hkern u1="N" u2="t" k="23" />
<hkern u1="N" u2="s" k="23" />
<hkern u1="N" u2="r" k="20" />
<hkern u1="N" u2="q" k="29" />
<hkern u1="N" u2="p" k="20" />
<hkern u1="N" u2="o" k="29" />
<hkern u1="N" u2="n" k="20" />
<hkern u1="N" u2="m" k="20" />
<hkern u1="N" u2="k" k="20" />
<hkern u1="N" u2="j" k="20" />
<hkern u1="N" u2="i" k="20" />
<hkern u1="N" u2="h" k="20" />
<hkern u1="N" u2="g" k="29" />
<hkern u1="N" u2="f" k="16" />
<hkern u1="N" u2="e" k="29" />
<hkern u1="N" u2="d" k="29" />
<hkern u1="N" u2="c" k="29" />
<hkern u1="N" u2="b" k="20" />
<hkern u1="N" u2="a" k="23" />
<hkern u1="N" u2="Y" k="10" />
<hkern u1="O" u2="&#x2122;" k="16" />
<hkern u1="O" u2="&#x2026;" k="39" />
<hkern u1="O" u2="&#x201e;" k="39" />
<hkern u1="O" u2="&#x201a;" k="39" />
<hkern u1="O" u2="&#xfe;" k="18" />
<hkern u1="O" u2="&#xfc;" k="16" />
<hkern u1="O" u2="&#xfb;" k="16" />
<hkern u1="O" u2="&#xfa;" k="16" />
<hkern u1="O" u2="&#xf9;" k="16" />
<hkern u1="O" u2="&#xf8;" k="12" />
<hkern u1="O" u2="&#xf6;" k="12" />
<hkern u1="O" u2="&#xf5;" k="12" />
<hkern u1="O" u2="&#xf4;" k="12" />
<hkern u1="O" u2="&#xf3;" k="12" />
<hkern u1="O" u2="&#xf2;" k="12" />
<hkern u1="O" u2="&#xf1;" k="18" />
<hkern u1="O" u2="&#xf0;" k="12" />
<hkern u1="O" u2="&#xef;" k="20" />
<hkern u1="O" u2="&#xee;" k="20" />
<hkern u1="O" u2="&#xed;" k="20" />
<hkern u1="O" u2="&#xec;" k="20" />
<hkern u1="O" u2="&#xeb;" k="12" />
<hkern u1="O" u2="&#xea;" k="12" />
<hkern u1="O" u2="&#xe9;" k="12" />
<hkern u1="O" u2="&#xe8;" k="12" />
<hkern u1="O" u2="&#xe7;" k="12" />
<hkern u1="O" u2="&#xe6;" k="20" />
<hkern u1="O" u2="&#xe5;" k="20" />
<hkern u1="O" u2="&#xe4;" k="20" />
<hkern u1="O" u2="&#xe3;" k="20" />
<hkern u1="O" u2="&#xe2;" k="20" />
<hkern u1="O" u2="&#xe1;" k="20" />
<hkern u1="O" u2="&#xe0;" k="20" />
<hkern u1="O" u2="&#xdd;" k="63" />
<hkern u1="O" u2="&#xc6;" k="90" />
<hkern u1="O" u2="&#xc5;" k="29" />
<hkern u1="O" u2="&#xc4;" k="29" />
<hkern u1="O" u2="&#xc3;" k="29" />
<hkern u1="O" u2="&#xc2;" k="29" />
<hkern u1="O" u2="&#xc1;" k="29" />
<hkern u1="O" u2="&#xc0;" k="29" />
<hkern u1="O" u2="&#x7d;" k="35" />
<hkern u1="O" u2="x" k="27" />
<hkern u1="O" u2="u" k="16" />
<hkern u1="O" u2="s" k="12" />
<hkern u1="O" u2="r" k="18" />
<hkern u1="O" u2="q" k="12" />
<hkern u1="O" u2="p" k="18" />
<hkern u1="O" u2="o" k="12" />
<hkern u1="O" u2="n" k="18" />
<hkern u1="O" u2="m" k="18" />
<hkern u1="O" u2="k" k="18" />
<hkern u1="O" u2="j" k="20" />
<hkern u1="O" u2="i" k="20" />
<hkern u1="O" u2="h" k="18" />
<hkern u1="O" u2="g" k="12" />
<hkern u1="O" u2="e" k="12" />
<hkern u1="O" u2="d" k="12" />
<hkern u1="O" u2="c" k="12" />
<hkern u1="O" u2="b" k="18" />
<hkern u1="O" u2="a" k="20" />
<hkern u1="O" u2="]" k="37" />
<hkern u1="O" u2="Y" k="63" />
<hkern u1="O" u2="X" k="53" />
<hkern u1="O" u2="V" k="27" />
<hkern u1="O" u2="T" k="49" />
<hkern u1="O" u2="A" k="29" />
<hkern u1="O" u2="&#x2e;" k="39" />
<hkern u1="O" u2="&#x2c;" k="39" />
<hkern u1="O" u2="&#x29;" k="43" />
<hkern u1="P" u2="&#x2026;" k="160" />
<hkern u1="P" u2="&#x201e;" k="160" />
<hkern u1="P" u2="&#x201a;" k="160" />
<hkern u1="P" u2="&#xfe;" k="12" />
<hkern u1="P" u2="&#xfc;" k="14" />
<hkern u1="P" u2="&#xfb;" k="14" />
<hkern u1="P" u2="&#xfa;" k="14" />
<hkern u1="P" u2="&#xf9;" k="14" />
<hkern u1="P" u2="&#xf8;" k="29" />
<hkern u1="P" u2="&#xf6;" k="29" />
<hkern u1="P" u2="&#xf5;" k="29" />
<hkern u1="P" u2="&#xf4;" k="29" />
<hkern u1="P" u2="&#xf3;" k="29" />
<hkern u1="P" u2="&#xf2;" k="29" />
<hkern u1="P" u2="&#xf1;" k="16" />
<hkern u1="P" u2="&#xf0;" k="29" />
<hkern u1="P" u2="&#xef;" k="16" />
<hkern u1="P" u2="&#xee;" k="4" />
<hkern u1="P" u2="&#xed;" k="16" />
<hkern u1="P" u2="&#xec;" k="16" />
<hkern u1="P" u2="&#xeb;" k="29" />
<hkern u1="P" u2="&#xea;" k="29" />
<hkern u1="P" u2="&#xe9;" k="29" />
<hkern u1="P" u2="&#xe8;" k="29" />
<hkern u1="P" u2="&#xe7;" k="29" />
<hkern u1="P" u2="&#xe6;" k="53" />
<hkern u1="P" u2="&#xe5;" k="53" />
<hkern u1="P" u2="&#xe4;" k="53" />
<hkern u1="P" u2="&#xe3;" k="53" />
<hkern u1="P" u2="&#xe2;" k="53" />
<hkern u1="P" u2="&#xe1;" k="53" />
<hkern u1="P" u2="&#xe0;" k="53" />
<hkern u1="P" u2="&#xdd;" k="10" />
<hkern u1="P" u2="&#xc6;" k="162" />
<hkern u1="P" u2="&#xc5;" k="90" />
<hkern u1="P" u2="&#xc4;" k="90" />
<hkern u1="P" u2="&#xc3;" k="90" />
<hkern u1="P" u2="&#xc2;" k="90" />
<hkern u1="P" u2="&#xc1;" k="90" />
<hkern u1="P" u2="&#xc0;" k="90" />
<hkern u1="P" u2="&#x7d;" k="20" />
<hkern u1="P" u2="u" k="14" />
<hkern u1="P" u2="s" k="14" />
<hkern u1="P" u2="r" k="16" />
<hkern u1="P" u2="q" k="29" />
<hkern u1="P" u2="p" k="16" />
<hkern u1="P" u2="o" k="29" />
<hkern u1="P" u2="n" k="16" />
<hkern u1="P" u2="m" k="16" />
<hkern u1="P" u2="k" k="12" />
<hkern u1="P" u2="j" k="16" />
<hkern u1="P" u2="i" k="16" />
<hkern u1="P" u2="h" k="12" />
<hkern u1="P" u2="g" k="29" />
<hkern u1="P" u2="e" k="29" />
<hkern u1="P" u2="d" k="29" />
<hkern u1="P" u2="c" k="29" />
<hkern u1="P" u2="b" k="12" />
<hkern u1="P" u2="a" k="53" />
<hkern u1="P" u2="Y" k="10" />
<hkern u1="P" u2="X" k="41" />
<hkern u1="P" u2="A" k="90" />
<hkern u1="P" u2="&#x2f;" k="47" />
<hkern u1="P" u2="&#x2e;" k="160" />
<hkern u1="P" u2="&#x2c;" k="160" />
<hkern u1="P" u2="&#x29;" k="27" />
<hkern u1="P" u2="&#x26;" k="14" />
<hkern u1="Q" u2="&#x2122;" k="16" />
<hkern u1="Q" u2="&#x2026;" k="39" />
<hkern u1="Q" u2="&#x201e;" k="39" />
<hkern u1="Q" u2="&#x201a;" k="39" />
<hkern u1="Q" u2="&#xfe;" k="18" />
<hkern u1="Q" u2="&#xfc;" k="16" />
<hkern u1="Q" u2="&#xfb;" k="16" />
<hkern u1="Q" u2="&#xfa;" k="16" />
<hkern u1="Q" u2="&#xf9;" k="16" />
<hkern u1="Q" u2="&#xf8;" k="12" />
<hkern u1="Q" u2="&#xf6;" k="12" />
<hkern u1="Q" u2="&#xf5;" k="12" />
<hkern u1="Q" u2="&#xf4;" k="12" />
<hkern u1="Q" u2="&#xf3;" k="12" />
<hkern u1="Q" u2="&#xf2;" k="12" />
<hkern u1="Q" u2="&#xf1;" k="18" />
<hkern u1="Q" u2="&#xf0;" k="12" />
<hkern u1="Q" u2="&#xef;" k="20" />
<hkern u1="Q" u2="&#xee;" k="20" />
<hkern u1="Q" u2="&#xed;" k="20" />
<hkern u1="Q" u2="&#xec;" k="20" />
<hkern u1="Q" u2="&#xeb;" k="12" />
<hkern u1="Q" u2="&#xea;" k="12" />
<hkern u1="Q" u2="&#xe9;" k="12" />
<hkern u1="Q" u2="&#xe8;" k="12" />
<hkern u1="Q" u2="&#xe7;" k="12" />
<hkern u1="Q" u2="&#xe6;" k="20" />
<hkern u1="Q" u2="&#xe5;" k="20" />
<hkern u1="Q" u2="&#xe4;" k="20" />
<hkern u1="Q" u2="&#xe3;" k="20" />
<hkern u1="Q" u2="&#xe2;" k="20" />
<hkern u1="Q" u2="&#xe1;" k="20" />
<hkern u1="Q" u2="&#xe0;" k="20" />
<hkern u1="Q" u2="&#xdd;" k="63" />
<hkern u1="Q" u2="&#xc6;" k="90" />
<hkern u1="Q" u2="&#xc5;" k="29" />
<hkern u1="Q" u2="&#xc4;" k="29" />
<hkern u1="Q" u2="&#xc3;" k="29" />
<hkern u1="Q" u2="&#xc2;" k="29" />
<hkern u1="Q" u2="&#xc1;" k="29" />
<hkern u1="Q" u2="&#xc0;" k="29" />
<hkern u1="Q" u2="&#x7d;" k="35" />
<hkern u1="Q" u2="x" k="27" />
<hkern u1="Q" u2="u" k="16" />
<hkern u1="Q" u2="s" k="12" />
<hkern u1="Q" u2="r" k="18" />
<hkern u1="Q" u2="q" k="12" />
<hkern u1="Q" u2="p" k="18" />
<hkern u1="Q" u2="o" k="12" />
<hkern u1="Q" u2="n" k="18" />
<hkern u1="Q" u2="m" k="18" />
<hkern u1="Q" u2="k" k="18" />
<hkern u1="Q" u2="j" k="20" />
<hkern u1="Q" u2="i" k="20" />
<hkern u1="Q" u2="h" k="18" />
<hkern u1="Q" u2="g" k="12" />
<hkern u1="Q" u2="e" k="12" />
<hkern u1="Q" u2="d" k="12" />
<hkern u1="Q" u2="c" k="12" />
<hkern u1="Q" u2="b" k="18" />
<hkern u1="Q" u2="a" k="20" />
<hkern u1="Q" u2="]" k="37" />
<hkern u1="Q" u2="Y" k="63" />
<hkern u1="Q" u2="X" k="53" />
<hkern u1="Q" u2="V" k="27" />
<hkern u1="Q" u2="T" k="49" />
<hkern u1="Q" u2="A" k="29" />
<hkern u1="Q" u2="&#x2e;" k="39" />
<hkern u1="Q" u2="&#x2c;" k="39" />
<hkern u1="Q" u2="&#x29;" k="43" />
<hkern u1="S" u2="&#xfb04;" k="53" />
<hkern u1="S" u2="&#xfb03;" k="53" />
<hkern u1="S" u2="&#xfb02;" k="53" />
<hkern u1="S" u2="&#xfb01;" k="53" />
<hkern u1="S" u2="&#x2122;" k="16" />
<hkern u1="S" u2="&#xff;" k="59" />
<hkern u1="S" u2="&#xfe;" k="16" />
<hkern u1="S" u2="&#xfd;" k="59" />
<hkern u1="S" u2="&#xfc;" k="18" />
<hkern u1="S" u2="&#xfb;" k="18" />
<hkern u1="S" u2="&#xfa;" k="18" />
<hkern u1="S" u2="&#xf9;" k="18" />
<hkern u1="S" u2="&#xf1;" k="16" />
<hkern u1="S" u2="&#xef;" k="18" />
<hkern u1="S" u2="&#xee;" k="18" />
<hkern u1="S" u2="&#xed;" k="18" />
<hkern u1="S" u2="&#xec;" k="18" />
<hkern u1="S" u2="&#xdf;" k="53" />
<hkern u1="S" u2="&#xdd;" k="16" />
<hkern u1="S" u2="y" k="59" />
<hkern u1="S" u2="x" k="43" />
<hkern u1="S" u2="v" k="66" />
<hkern u1="S" u2="u" k="18" />
<hkern u1="S" u2="t" k="45" />
<hkern u1="S" u2="s" k="14" />
<hkern u1="S" u2="r" k="16" />
<hkern u1="S" u2="p" k="16" />
<hkern u1="S" u2="n" k="16" />
<hkern u1="S" u2="m" k="16" />
<hkern u1="S" u2="k" k="16" />
<hkern u1="S" u2="j" k="18" />
<hkern u1="S" u2="i" k="18" />
<hkern u1="S" u2="h" k="16" />
<hkern u1="S" u2="f" k="53" />
<hkern u1="S" u2="b" k="16" />
<hkern u1="S" u2="Y" k="16" />
<hkern u1="S" u2="X" k="14" />
<hkern u1="S" u2="V" k="12" />
<hkern u1="S" u2="T" k="76" />
<hkern u1="S" u2="&#x2a;" k="25" />
<hkern u1="T" u2="&#xfb04;" k="61" />
<hkern u1="T" u2="&#xfb03;" k="61" />
<hkern u1="T" u2="&#xfb02;" k="61" />
<hkern u1="T" u2="&#xfb01;" k="61" />
<hkern u1="T" u2="&#x203a;" k="104" />
<hkern u1="T" u2="&#x2039;" k="121" />
<hkern u1="T" u2="&#x2026;" k="131" />
<hkern u1="T" u2="&#x201e;" k="131" />
<hkern u1="T" u2="&#x201a;" k="131" />
<hkern u1="T" u2="&#x2014;" k="131" />
<hkern u1="T" u2="&#x2013;" k="131" />
<hkern u1="T" u2="&#xff;" k="141" />
<hkern u1="T" u2="&#xfe;" k="12" />
<hkern u1="T" u2="&#xfd;" k="141" />
<hkern u1="T" u2="&#xfc;" k="135" />
<hkern u1="T" u2="&#xfb;" k="135" />
<hkern u1="T" u2="&#xfa;" k="135" />
<hkern u1="T" u2="&#xf9;" k="135" />
<hkern u1="T" u2="&#xf8;" k="174" />
<hkern u1="T" u2="&#xf6;" k="174" />
<hkern u1="T" u2="&#xf5;" k="174" />
<hkern u1="T" u2="&#xf4;" k="174" />
<hkern u1="T" u2="&#xf3;" k="174" />
<hkern u1="T" u2="&#xf2;" k="174" />
<hkern u1="T" u2="&#xf1;" k="137" />
<hkern u1="T" u2="&#xf0;" k="174" />
<hkern u1="T" u2="&#xef;" k="-31" />
<hkern u1="T" u2="&#xee;" k="-2" />
<hkern u1="T" u2="&#xed;" k="20" />
<hkern u1="T" u2="&#xec;" k="20" />
<hkern u1="T" u2="&#xeb;" k="174" />
<hkern u1="T" u2="&#xea;" k="174" />
<hkern u1="T" u2="&#xe9;" k="174" />
<hkern u1="T" u2="&#xe8;" k="174" />
<hkern u1="T" u2="&#xe7;" k="174" />
<hkern u1="T" u2="&#xe6;" k="162" />
<hkern u1="T" u2="&#xe5;" k="162" />
<hkern u1="T" u2="&#xe4;" k="162" />
<hkern u1="T" u2="&#xe3;" k="162" />
<hkern u1="T" u2="&#xe2;" k="162" />
<hkern u1="T" u2="&#xe1;" k="162" />
<hkern u1="T" u2="&#xe0;" k="162" />
<hkern u1="T" u2="&#xdf;" k="61" />
<hkern u1="T" u2="&#xdd;" k="-88" />
<hkern u1="T" u2="&#xd8;" k="49" />
<hkern u1="T" u2="&#xd6;" k="49" />
<hkern u1="T" u2="&#xd5;" k="49" />
<hkern u1="T" u2="&#xd4;" k="49" />
<hkern u1="T" u2="&#xd3;" k="49" />
<hkern u1="T" u2="&#xd2;" k="49" />
<hkern u1="T" u2="&#xc7;" k="49" />
<hkern u1="T" u2="&#xc6;" k="109" />
<hkern u1="T" u2="&#xc5;" k="127" />
<hkern u1="T" u2="&#xc4;" k="127" />
<hkern u1="T" u2="&#xc3;" k="127" />
<hkern u1="T" u2="&#xc2;" k="127" />
<hkern u1="T" u2="&#xc1;" k="127" />
<hkern u1="T" u2="&#xc0;" k="127" />
<hkern u1="T" u2="&#xbb;" k="104" />
<hkern u1="T" u2="&#xae;" k="33" />
<hkern u1="T" u2="&#xab;" k="121" />
<hkern u1="T" u2="y" k="141" />
<hkern u1="T" u2="x" k="139" />
<hkern u1="T" u2="v" k="145" />
<hkern u1="T" u2="u" k="135" />
<hkern u1="T" u2="t" k="129" />
<hkern u1="T" u2="s" k="156" />
<hkern u1="T" u2="r" k="137" />
<hkern u1="T" u2="q" k="174" />
<hkern u1="T" u2="p" k="137" />
<hkern u1="T" u2="o" k="174" />
<hkern u1="T" u2="n" k="137" />
<hkern u1="T" u2="m" k="137" />
<hkern u1="T" u2="k" k="12" />
<hkern u1="T" u2="j" k="20" />
<hkern u1="T" u2="i" k="20" />
<hkern u1="T" u2="h" k="12" />
<hkern u1="T" u2="g" k="174" />
<hkern u1="T" u2="f" k="61" />
<hkern u1="T" u2="e" k="174" />
<hkern u1="T" u2="d" k="174" />
<hkern u1="T" u2="c" k="174" />
<hkern u1="T" u2="b" k="12" />
<hkern u1="T" u2="a" k="162" />
<hkern u1="T" u2="Y" k="-88" />
<hkern u1="T" u2="X" k="-72" />
<hkern u1="T" u2="V" k="-76" />
<hkern u1="T" u2="Q" k="49" />
<hkern u1="T" u2="O" k="49" />
<hkern u1="T" u2="G" k="49" />
<hkern u1="T" u2="C" k="49" />
<hkern u1="T" u2="A" k="127" />
<hkern u1="T" u2="&#x40;" k="61" />
<hkern u1="T" u2="&#x3b;" k="86" />
<hkern u1="T" u2="&#x3a;" k="86" />
<hkern u1="T" u2="&#x2f;" k="74" />
<hkern u1="T" u2="&#x2e;" k="131" />
<hkern u1="T" u2="&#x2d;" k="131" />
<hkern u1="T" u2="&#x2c;" k="131" />
<hkern u1="T" u2="&#x26;" k="84" />
<hkern u1="U" u2="&#xfb04;" k="16" />
<hkern u1="U" u2="&#xfb03;" k="16" />
<hkern u1="U" u2="&#xfb02;" k="16" />
<hkern u1="U" u2="&#xfb01;" k="16" />
<hkern u1="U" u2="&#x2026;" k="37" />
<hkern u1="U" u2="&#x201e;" k="37" />
<hkern u1="U" u2="&#x201a;" k="37" />
<hkern u1="U" u2="&#xff;" k="12" />
<hkern u1="U" u2="&#xfe;" k="27" />
<hkern u1="U" u2="&#xfd;" k="12" />
<hkern u1="U" u2="&#xfc;" k="37" />
<hkern u1="U" u2="&#xfb;" k="37" />
<hkern u1="U" u2="&#xfa;" k="37" />
<hkern u1="U" u2="&#xf9;" k="37" />
<hkern u1="U" u2="&#xf8;" k="31" />
<hkern u1="U" u2="&#xf6;" k="31" />
<hkern u1="U" u2="&#xf5;" k="31" />
<hkern u1="U" u2="&#xf4;" k="31" />
<hkern u1="U" u2="&#xf3;" k="31" />
<hkern u1="U" u2="&#xf2;" k="31" />
<hkern u1="U" u2="&#xf1;" k="37" />
<hkern u1="U" u2="&#xf0;" k="31" />
<hkern u1="U" u2="&#xef;" k="31" />
<hkern u1="U" u2="&#xee;" k="31" />
<hkern u1="U" u2="&#xed;" k="31" />
<hkern u1="U" u2="&#xec;" k="31" />
<hkern u1="U" u2="&#xeb;" k="31" />
<hkern u1="U" u2="&#xea;" k="31" />
<hkern u1="U" u2="&#xe9;" k="31" />
<hkern u1="U" u2="&#xe8;" k="31" />
<hkern u1="U" u2="&#xe7;" k="31" />
<hkern u1="U" u2="&#xe6;" k="35" />
<hkern u1="U" u2="&#xe5;" k="35" />
<hkern u1="U" u2="&#xe4;" k="35" />
<hkern u1="U" u2="&#xe3;" k="35" />
<hkern u1="U" u2="&#xe2;" k="35" />
<hkern u1="U" u2="&#xe1;" k="35" />
<hkern u1="U" u2="&#xe0;" k="35" />
<hkern u1="U" u2="&#xdf;" k="16" />
<hkern u1="U" u2="&#xc6;" k="20" />
<hkern u1="U" u2="&#xc5;" k="41" />
<hkern u1="U" u2="&#xc4;" k="41" />
<hkern u1="U" u2="&#xc3;" k="41" />
<hkern u1="U" u2="&#xc2;" k="41" />
<hkern u1="U" u2="&#xc1;" k="41" />
<hkern u1="U" u2="&#xc0;" k="41" />
<hkern u1="U" u2="y" k="12" />
<hkern u1="U" u2="x" k="20" />
<hkern u1="U" u2="v" k="16" />
<hkern u1="U" u2="u" k="37" />
<hkern u1="U" u2="t" k="20" />
<hkern u1="U" u2="s" k="33" />
<hkern u1="U" u2="r" k="37" />
<hkern u1="U" u2="q" k="31" />
<hkern u1="U" u2="p" k="37" />
<hkern u1="U" u2="o" k="31" />
<hkern u1="U" u2="n" k="37" />
<hkern u1="U" u2="m" k="37" />
<hkern u1="U" u2="k" k="27" />
<hkern u1="U" u2="j" k="31" />
<hkern u1="U" u2="i" k="31" />
<hkern u1="U" u2="h" k="27" />
<hkern u1="U" u2="g" k="31" />
<hkern u1="U" u2="f" k="16" />
<hkern u1="U" u2="e" k="31" />
<hkern u1="U" u2="d" k="31" />
<hkern u1="U" u2="c" k="31" />
<hkern u1="U" u2="b" k="27" />
<hkern u1="U" u2="a" k="35" />
<hkern u1="U" u2="A" k="41" />
<hkern u1="U" u2="&#x2f;" k="29" />
<hkern u1="U" u2="&#x2e;" k="37" />
<hkern u1="U" u2="&#x2c;" k="37" />
<hkern u1="U" u2="&#x29;" k="23" />
<hkern u1="V" u2="&#xfb04;" k="23" />
<hkern u1="V" u2="&#xfb03;" k="23" />
<hkern u1="V" u2="&#xfb02;" k="23" />
<hkern u1="V" u2="&#xfb01;" k="23" />
<hkern u1="V" u2="&#x2122;" k="-16" />
<hkern u1="V" u2="&#x203a;" k="25" />
<hkern u1="V" u2="&#x2039;" k="63" />
<hkern u1="V" u2="&#x2026;" k="131" />
<hkern u1="V" u2="&#x201e;" k="131" />
<hkern u1="V" u2="&#x201a;" k="131" />
<hkern u1="V" u2="&#x2014;" k="68" />
<hkern u1="V" u2="&#x2013;" k="68" />
<hkern u1="V" u2="&#xff;" k="20" />
<hkern u1="V" u2="&#xfe;" k="16" />
<hkern u1="V" u2="&#xfd;" k="20" />
<hkern u1="V" u2="&#xfc;" k="84" />
<hkern u1="V" u2="&#xfb;" k="84" />
<hkern u1="V" u2="&#xfa;" k="84" />
<hkern u1="V" u2="&#xf9;" k="84" />
<hkern u1="V" u2="&#xf8;" k="109" />
<hkern u1="V" u2="&#xf6;" k="109" />
<hkern u1="V" u2="&#xf5;" k="109" />
<hkern u1="V" u2="&#xf4;" k="109" />
<hkern u1="V" u2="&#xf3;" k="109" />
<hkern u1="V" u2="&#xf2;" k="109" />
<hkern u1="V" u2="&#xf1;" k="90" />
<hkern u1="V" u2="&#xf0;" k="109" />
<hkern u1="V" u2="&#xef;" k="-37" />
<hkern u1="V" u2="&#xee;" k="-2" />
<hkern u1="V" u2="&#xed;" k="18" />
<hkern u1="V" u2="&#xec;" k="-6" />
<hkern u1="V" u2="&#xeb;" k="109" />
<hkern u1="V" u2="&#xea;" k="109" />
<hkern u1="V" u2="&#xe9;" k="109" />
<hkern u1="V" u2="&#xe8;" k="109" />
<hkern u1="V" u2="&#xe7;" k="109" />
<hkern u1="V" u2="&#xe6;" k="111" />
<hkern u1="V" u2="&#xe5;" k="111" />
<hkern u1="V" u2="&#xe4;" k="111" />
<hkern u1="V" u2="&#xe3;" k="111" />
<hkern u1="V" u2="&#xe2;" k="111" />
<hkern u1="V" u2="&#xe1;" k="111" />
<hkern u1="V" u2="&#xe0;" k="111" />
<hkern u1="V" u2="&#xdf;" k="23" />
<hkern u1="V" u2="&#xdd;" k="-98" />
<hkern u1="V" u2="&#xd8;" k="27" />
<hkern u1="V" u2="&#xd6;" k="27" />
<hkern u1="V" u2="&#xd5;" k="27" />
<hkern u1="V" u2="&#xd4;" k="27" />
<hkern u1="V" u2="&#xd3;" k="27" />
<hkern u1="V" u2="&#xd2;" k="27" />
<hkern u1="V" u2="&#xc7;" k="27" />
<hkern u1="V" u2="&#xc6;" k="98" />
<hkern u1="V" u2="&#xc5;" k="90" />
<hkern u1="V" u2="&#xc4;" k="90" />
<hkern u1="V" u2="&#xc3;" k="90" />
<hkern u1="V" u2="&#xc2;" k="90" />
<hkern u1="V" u2="&#xc1;" k="90" />
<hkern u1="V" u2="&#xc0;" k="90" />
<hkern u1="V" u2="&#xbb;" k="25" />
<hkern u1="V" u2="&#xae;" k="20" />
<hkern u1="V" u2="&#xab;" k="63" />
<hkern u1="V" u2="y" k="20" />
<hkern u1="V" u2="x" k="31" />
<hkern u1="V" u2="v" k="25" />
<hkern u1="V" u2="u" k="84" />
<hkern u1="V" u2="t" k="49" />
<hkern u1="V" u2="s" k="102" />
<hkern u1="V" u2="r" k="90" />
<hkern u1="V" u2="q" k="109" />
<hkern u1="V" u2="p" k="90" />
<hkern u1="V" u2="o" k="109" />
<hkern u1="V" u2="n" k="90" />
<hkern u1="V" u2="m" k="90" />
<hkern u1="V" u2="k" k="16" />
<hkern u1="V" u2="j" k="18" />
<hkern u1="V" u2="i" k="18" />
<hkern u1="V" u2="h" k="16" />
<hkern u1="V" u2="g" k="109" />
<hkern u1="V" u2="f" k="23" />
<hkern u1="V" u2="e" k="109" />
<hkern u1="V" u2="d" k="109" />
<hkern u1="V" u2="c" k="109" />
<hkern u1="V" u2="b" k="16" />
<hkern u1="V" u2="a" k="111" />
<hkern u1="V" u2="Y" k="-98" />
<hkern u1="V" u2="X" k="-68" />
<hkern u1="V" u2="V" k="-72" />
<hkern u1="V" u2="T" k="-63" />
<hkern u1="V" u2="S" k="10" />
<hkern u1="V" u2="Q" k="27" />
<hkern u1="V" u2="O" k="27" />
<hkern u1="V" u2="G" k="27" />
<hkern u1="V" u2="C" k="27" />
<hkern u1="V" u2="A" k="90" />
<hkern u1="V" u2="&#x40;" k="35" />
<hkern u1="V" u2="&#x3b;" k="18" />
<hkern u1="V" u2="&#x3a;" k="18" />
<hkern u1="V" u2="&#x2f;" k="72" />
<hkern u1="V" u2="&#x2e;" k="131" />
<hkern u1="V" u2="&#x2d;" k="68" />
<hkern u1="V" u2="&#x2c;" k="131" />
<hkern u1="V" u2="&#x26;" k="29" />
<hkern u1="X" u2="&#xfb04;" k="31" />
<hkern u1="X" u2="&#xfb03;" k="31" />
<hkern u1="X" u2="&#xfb02;" k="31" />
<hkern u1="X" u2="&#xfb01;" k="31" />
<hkern u1="X" u2="&#x2039;" k="31" />
<hkern u1="X" u2="&#x2014;" k="63" />
<hkern u1="X" u2="&#x2013;" k="63" />
<hkern u1="X" u2="&#xff;" k="74" />
<hkern u1="X" u2="&#xfd;" k="74" />
<hkern u1="X" u2="&#xfc;" k="63" />
<hkern u1="X" u2="&#xfb;" k="63" />
<hkern u1="X" u2="&#xfa;" k="63" />
<hkern u1="X" u2="&#xf9;" k="63" />
<hkern u1="X" u2="&#xf8;" k="82" />
<hkern u1="X" u2="&#xf6;" k="82" />
<hkern u1="X" u2="&#xf5;" k="82" />
<hkern u1="X" u2="&#xf4;" k="82" />
<hkern u1="X" u2="&#xf3;" k="82" />
<hkern u1="X" u2="&#xf2;" k="82" />
<hkern u1="X" u2="&#xf1;" k="16" />
<hkern u1="X" u2="&#xf0;" k="82" />
<hkern u1="X" u2="&#xeb;" k="82" />
<hkern u1="X" u2="&#xea;" k="82" />
<hkern u1="X" u2="&#xe9;" k="82" />
<hkern u1="X" u2="&#xe8;" k="82" />
<hkern u1="X" u2="&#xe7;" k="82" />
<hkern u1="X" u2="&#xe6;" k="20" />
<hkern u1="X" u2="&#xe5;" k="20" />
<hkern u1="X" u2="&#xe4;" k="20" />
<hkern u1="X" u2="&#xe3;" k="20" />
<hkern u1="X" u2="&#xe2;" k="20" />
<hkern u1="X" u2="&#xe1;" k="20" />
<hkern u1="X" u2="&#xe0;" k="20" />
<hkern u1="X" u2="&#xdf;" k="31" />
<hkern u1="X" u2="&#xdd;" k="-76" />
<hkern u1="X" u2="&#xd8;" k="53" />
<hkern u1="X" u2="&#xd6;" k="53" />
<hkern u1="X" u2="&#xd5;" k="53" />
<hkern u1="X" u2="&#xd4;" k="53" />
<hkern u1="X" u2="&#xd3;" k="53" />
<hkern u1="X" u2="&#xd2;" k="53" />
<hkern u1="X" u2="&#xc7;" k="53" />
<hkern u1="X" u2="&#xc5;" k="-66" />
<hkern u1="X" u2="&#xc4;" k="-66" />
<hkern u1="X" u2="&#xc3;" k="-66" />
<hkern u1="X" u2="&#xc2;" k="-66" />
<hkern u1="X" u2="&#xc1;" k="-66" />
<hkern u1="X" u2="&#xc0;" k="-66" />
<hkern u1="X" u2="&#xab;" k="31" />
<hkern u1="X" u2="y" k="74" />
<hkern u1="X" u2="v" k="78" />
<hkern u1="X" u2="u" k="63" />
<hkern u1="X" u2="t" k="90" />
<hkern u1="X" u2="s" k="18" />
<hkern u1="X" u2="r" k="16" />
<hkern u1="X" u2="q" k="82" />
<hkern u1="X" u2="p" k="16" />
<hkern u1="X" u2="o" k="82" />
<hkern u1="X" u2="n" k="16" />
<hkern u1="X" u2="m" k="16" />
<hkern u1="X" u2="g" k="82" />
<hkern u1="X" u2="f" k="31" />
<hkern u1="X" u2="e" k="82" />
<hkern u1="X" u2="d" k="82" />
<hkern u1="X" u2="c" k="82" />
<hkern u1="X" u2="a" k="20" />
<hkern u1="X" u2="Y" k="-76" />
<hkern u1="X" u2="X" k="-57" />
<hkern u1="X" u2="V" k="-66" />
<hkern u1="X" u2="T" k="-66" />
<hkern u1="X" u2="S" k="12" />
<hkern u1="X" u2="Q" k="53" />
<hkern u1="X" u2="O" k="53" />
<hkern u1="X" u2="G" k="53" />
<hkern u1="X" u2="C" k="53" />
<hkern u1="X" u2="A" k="-66" />
<hkern u1="X" u2="&#x2d;" k="63" />
<hkern u1="X" u2="&#x26;" k="35" />
<hkern u1="Y" u2="&#xfb04;" k="49" />
<hkern u1="Y" u2="&#xfb03;" k="49" />
<hkern u1="Y" u2="&#xfb02;" k="49" />
<hkern u1="Y" u2="&#xfb01;" k="49" />
<hkern u1="Y" u2="&#x2122;" k="-53" />
<hkern u1="Y" u2="&#x203a;" k="66" />
<hkern u1="Y" u2="&#x2039;" k="121" />
<hkern u1="Y" u2="&#x2026;" k="154" />
<hkern u1="Y" u2="&#x201e;" k="154" />
<hkern u1="Y" u2="&#x201a;" k="154" />
<hkern u1="Y" u2="&#x2014;" k="135" />
<hkern u1="Y" u2="&#x2013;" k="135" />
<hkern u1="Y" u2="&#xff;" k="70" />
<hkern u1="Y" u2="&#xfe;" k="16" />
<hkern u1="Y" u2="&#xfd;" k="70" />
<hkern u1="Y" u2="&#xfc;" k="139" />
<hkern u1="Y" u2="&#xfb;" k="139" />
<hkern u1="Y" u2="&#xfa;" k="139" />
<hkern u1="Y" u2="&#xf9;" k="139" />
<hkern u1="Y" u2="&#xf8;" k="178" />
<hkern u1="Y" u2="&#xf6;" k="178" />
<hkern u1="Y" u2="&#xf5;" k="178" />
<hkern u1="Y" u2="&#xf4;" k="178" />
<hkern u1="Y" u2="&#xf3;" k="178" />
<hkern u1="Y" u2="&#xf2;" k="178" />
<hkern u1="Y" u2="&#xf1;" k="143" />
<hkern u1="Y" u2="&#xf0;" k="178" />
<hkern u1="Y" u2="&#xef;" k="-47" />
<hkern u1="Y" u2="&#xee;" k="18" />
<hkern u1="Y" u2="&#xed;" k="18" />
<hkern u1="Y" u2="&#xec;" k="-29" />
<hkern u1="Y" u2="&#xeb;" k="178" />
<hkern u1="Y" u2="&#xea;" k="178" />
<hkern u1="Y" u2="&#xe9;" k="178" />
<hkern u1="Y" u2="&#xe8;" k="178" />
<hkern u1="Y" u2="&#xe7;" k="178" />
<hkern u1="Y" u2="&#xe6;" k="166" />
<hkern u1="Y" u2="&#xe5;" k="166" />
<hkern u1="Y" u2="&#xe4;" k="166" />
<hkern u1="Y" u2="&#xe3;" k="166" />
<hkern u1="Y" u2="&#xe2;" k="166" />
<hkern u1="Y" u2="&#xe1;" k="166" />
<hkern u1="Y" u2="&#xe0;" k="166" />
<hkern u1="Y" u2="&#xdf;" k="49" />
<hkern u1="Y" u2="&#xde;" k="10" />
<hkern u1="Y" u2="&#xdd;" k="-102" />
<hkern u1="Y" u2="&#xd8;" k="61" />
<hkern u1="Y" u2="&#xd6;" k="61" />
<hkern u1="Y" u2="&#xd5;" k="61" />
<hkern u1="Y" u2="&#xd4;" k="61" />
<hkern u1="Y" u2="&#xd3;" k="61" />
<hkern u1="Y" u2="&#xd2;" k="61" />
<hkern u1="Y" u2="&#xd1;" k="10" />
<hkern u1="Y" u2="&#xd0;" k="10" />
<hkern u1="Y" u2="&#xcf;" k="10" />
<hkern u1="Y" u2="&#xce;" k="10" />
<hkern u1="Y" u2="&#xcd;" k="10" />
<hkern u1="Y" u2="&#xcc;" k="10" />
<hkern u1="Y" u2="&#xcb;" k="10" />
<hkern u1="Y" u2="&#xca;" k="10" />
<hkern u1="Y" u2="&#xc9;" k="10" />
<hkern u1="Y" u2="&#xc8;" k="10" />
<hkern u1="Y" u2="&#xc7;" k="61" />
<hkern u1="Y" u2="&#xc6;" k="147" />
<hkern u1="Y" u2="&#xc5;" k="131" />
<hkern u1="Y" u2="&#xc4;" k="131" />
<hkern u1="Y" u2="&#xc3;" k="131" />
<hkern u1="Y" u2="&#xc2;" k="131" />
<hkern u1="Y" u2="&#xc1;" k="131" />
<hkern u1="Y" u2="&#xc0;" k="131" />
<hkern u1="Y" u2="&#xbb;" k="66" />
<hkern u1="Y" u2="&#xae;" k="47" />
<hkern u1="Y" u2="&#xab;" k="121" />
<hkern u1="Y" u2="&#x7d;" k="-25" />
<hkern u1="Y" u2="y" k="70" />
<hkern u1="Y" u2="x" k="88" />
<hkern u1="Y" u2="v" k="72" />
<hkern u1="Y" u2="u" k="139" />
<hkern u1="Y" u2="t" k="113" />
<hkern u1="Y" u2="s" k="174" />
<hkern u1="Y" u2="r" k="143" />
<hkern u1="Y" u2="q" k="178" />
<hkern u1="Y" u2="p" k="143" />
<hkern u1="Y" u2="o" k="178" />
<hkern u1="Y" u2="n" k="143" />
<hkern u1="Y" u2="m" k="143" />
<hkern u1="Y" u2="k" k="16" />
<hkern u1="Y" u2="j" k="18" />
<hkern u1="Y" u2="i" k="18" />
<hkern u1="Y" u2="h" k="16" />
<hkern u1="Y" u2="g" k="178" />
<hkern u1="Y" u2="f" k="49" />
<hkern u1="Y" u2="e" k="178" />
<hkern u1="Y" u2="d" k="178" />
<hkern u1="Y" u2="c" k="178" />
<hkern u1="Y" u2="b" k="16" />
<hkern u1="Y" u2="a" k="166" />
<hkern u1="Y" u2="]" k="-20" />
<hkern u1="Y" u2="Y" k="-102" />
<hkern u1="Y" u2="X" k="-92" />
<hkern u1="Y" u2="V" k="-92" />
<hkern u1="Y" u2="T" k="-86" />
<hkern u1="Y" u2="S" k="18" />
<hkern u1="Y" u2="R" k="10" />
<hkern u1="Y" u2="Q" k="61" />
<hkern u1="Y" u2="P" k="10" />
<hkern u1="Y" u2="O" k="61" />
<hkern u1="Y" u2="N" k="10" />
<hkern u1="Y" u2="M" k="10" />
<hkern u1="Y" u2="L" k="10" />
<hkern u1="Y" u2="K" k="10" />
<hkern u1="Y" u2="I" k="10" />
<hkern u1="Y" u2="H" k="10" />
<hkern u1="Y" u2="G" k="61" />
<hkern u1="Y" u2="F" k="10" />
<hkern u1="Y" u2="E" k="10" />
<hkern u1="Y" u2="D" k="10" />
<hkern u1="Y" u2="C" k="61" />
<hkern u1="Y" u2="B" k="10" />
<hkern u1="Y" u2="A" k="131" />
<hkern u1="Y" u2="&#x40;" k="72" />
<hkern u1="Y" u2="&#x3b;" k="53" />
<hkern u1="Y" u2="&#x3a;" k="53" />
<hkern u1="Y" u2="&#x2f;" k="90" />
<hkern u1="Y" u2="&#x2e;" k="154" />
<hkern u1="Y" u2="&#x2d;" k="135" />
<hkern u1="Y" u2="&#x2c;" k="154" />
<hkern u1="Y" u2="&#x26;" k="53" />
<hkern u1="[" u2="&#xff;" k="31" />
<hkern u1="[" u2="&#xfd;" k="31" />
<hkern u1="[" u2="&#xfc;" k="47" />
<hkern u1="[" u2="&#xfb;" k="47" />
<hkern u1="[" u2="&#xfa;" k="47" />
<hkern u1="[" u2="&#xf9;" k="47" />
<hkern u1="[" u2="&#xf8;" k="51" />
<hkern u1="[" u2="&#xf6;" k="51" />
<hkern u1="[" u2="&#xf5;" k="51" />
<hkern u1="[" u2="&#xf4;" k="51" />
<hkern u1="[" u2="&#xf3;" k="51" />
<hkern u1="[" u2="&#xf2;" k="51" />
<hkern u1="[" u2="&#xf1;" k="41" />
<hkern u1="[" u2="&#xf0;" k="51" />
<hkern u1="[" u2="&#xeb;" k="51" />
<hkern u1="[" u2="&#xea;" k="51" />
<hkern u1="[" u2="&#xe9;" k="51" />
<hkern u1="[" u2="&#xe8;" k="51" />
<hkern u1="[" u2="&#xe7;" k="51" />
<hkern u1="[" u2="&#xe6;" k="41" />
<hkern u1="[" u2="&#xe5;" k="41" />
<hkern u1="[" u2="&#xe4;" k="41" />
<hkern u1="[" u2="&#xe3;" k="41" />
<hkern u1="[" u2="&#xe2;" k="41" />
<hkern u1="[" u2="&#xe1;" k="41" />
<hkern u1="[" u2="&#xe0;" k="41" />
<hkern u1="[" u2="&#xdd;" k="-20" />
<hkern u1="[" u2="&#xd8;" k="35" />
<hkern u1="[" u2="&#xd6;" k="35" />
<hkern u1="[" u2="&#xd5;" k="35" />
<hkern u1="[" u2="&#xd4;" k="35" />
<hkern u1="[" u2="&#xd3;" k="35" />
<hkern u1="[" u2="&#xd2;" k="35" />
<hkern u1="[" u2="&#xc7;" k="35" />
<hkern u1="[" u2="&#xc5;" k="35" />
<hkern u1="[" u2="&#xc4;" k="35" />
<hkern u1="[" u2="&#xc3;" k="35" />
<hkern u1="[" u2="&#xc2;" k="35" />
<hkern u1="[" u2="&#xc1;" k="35" />
<hkern u1="[" u2="&#xc0;" k="35" />
<hkern u1="[" u2="y" k="31" />
<hkern u1="[" u2="x" k="23" />
<hkern u1="[" u2="v" k="43" />
<hkern u1="[" u2="u" k="47" />
<hkern u1="[" u2="t" k="41" />
<hkern u1="[" u2="s" k="41" />
<hkern u1="[" u2="r" k="41" />
<hkern u1="[" u2="q" k="51" />
<hkern u1="[" u2="p" k="41" />
<hkern u1="[" u2="o" k="51" />
<hkern u1="[" u2="n" k="41" />
<hkern u1="[" u2="m" k="41" />
<hkern u1="[" u2="g" k="51" />
<hkern u1="[" u2="e" k="51" />
<hkern u1="[" u2="d" k="51" />
<hkern u1="[" u2="c" k="51" />
<hkern u1="[" u2="a" k="41" />
<hkern u1="[" u2="Y" k="-20" />
<hkern u1="[" u2="Q" k="35" />
<hkern u1="[" u2="O" k="35" />
<hkern u1="[" u2="G" k="35" />
<hkern u1="[" u2="C" k="35" />
<hkern u1="[" u2="A" k="35" />
<hkern u1="\" u2="&#x201d;" k="82" />
<hkern u1="\" u2="&#x2019;" k="82" />
<hkern u1="\" u2="&#xff;" k="27" />
<hkern u1="\" u2="&#xfd;" k="27" />
<hkern u1="\" u2="&#xdd;" k="90" />
<hkern u1="\" u2="&#xdc;" k="29" />
<hkern u1="\" u2="&#xdb;" k="29" />
<hkern u1="\" u2="&#xda;" k="29" />
<hkern u1="\" u2="&#xd9;" k="29" />
<hkern u1="\" u2="&#xd8;" k="20" />
<hkern u1="\" u2="&#xd6;" k="20" />
<hkern u1="\" u2="&#xd5;" k="20" />
<hkern u1="\" u2="&#xd4;" k="20" />
<hkern u1="\" u2="&#xd3;" k="20" />
<hkern u1="\" u2="&#xd2;" k="20" />
<hkern u1="\" u2="&#xc7;" k="20" />
<hkern u1="\" u2="&#xc6;" k="-16" />
<hkern u1="\" u2="y" k="27" />
<hkern u1="\" u2="v" k="39" />
<hkern u1="\" u2="t" k="20" />
<hkern u1="\" u2="Y" k="90" />
<hkern u1="\" u2="V" k="72" />
<hkern u1="\" u2="U" k="29" />
<hkern u1="\" u2="T" k="74" />
<hkern u1="\" u2="Q" k="20" />
<hkern u1="\" u2="O" k="20" />
<hkern u1="\" u2="G" k="20" />
<hkern u1="\" u2="C" k="20" />
<hkern u1="\" u2="&#x27;" k="82" />
<hkern u1="\" u2="&#x22;" k="82" />
<hkern u1="a" u2="&#xfb04;" k="12" />
<hkern u1="a" u2="&#xfb03;" k="12" />
<hkern u1="a" u2="&#xfb02;" k="12" />
<hkern u1="a" u2="&#xfb01;" k="12" />
<hkern u1="a" u2="&#x2122;" k="51" />
<hkern u1="a" u2="&#x201d;" k="29" />
<hkern u1="a" u2="&#x201c;" k="27" />
<hkern u1="a" u2="&#x2019;" k="29" />
<hkern u1="a" u2="&#x2018;" k="27" />
<hkern u1="a" u2="&#xff;" k="14" />
<hkern u1="a" u2="&#xfd;" k="14" />
<hkern u1="a" u2="&#xfc;" k="8" />
<hkern u1="a" u2="&#xfb;" k="8" />
<hkern u1="a" u2="&#xfa;" k="8" />
<hkern u1="a" u2="&#xf9;" k="8" />
<hkern u1="a" u2="&#xdf;" k="12" />
<hkern u1="a" u2="&#xde;" k="29" />
<hkern u1="a" u2="&#xdd;" k="176" />
<hkern u1="a" u2="&#xdc;" k="45" />
<hkern u1="a" u2="&#xdb;" k="45" />
<hkern u1="a" u2="&#xda;" k="45" />
<hkern u1="a" u2="&#xd9;" k="45" />
<hkern u1="a" u2="&#xd8;" k="20" />
<hkern u1="a" u2="&#xd6;" k="20" />
<hkern u1="a" u2="&#xd5;" k="20" />
<hkern u1="a" u2="&#xd4;" k="20" />
<hkern u1="a" u2="&#xd3;" k="20" />
<hkern u1="a" u2="&#xd2;" k="20" />
<hkern u1="a" u2="&#xd1;" k="29" />
<hkern u1="a" u2="&#xd0;" k="29" />
<hkern u1="a" u2="&#xcf;" k="29" />
<hkern u1="a" u2="&#xce;" k="29" />
<hkern u1="a" u2="&#xcd;" k="29" />
<hkern u1="a" u2="&#xcc;" k="29" />
<hkern u1="a" u2="&#xcb;" k="29" />
<hkern u1="a" u2="&#xca;" k="29" />
<hkern u1="a" u2="&#xc9;" k="29" />
<hkern u1="a" u2="&#xc8;" k="29" />
<hkern u1="a" u2="&#xc7;" k="20" />
<hkern u1="a" u2="&#xc5;" k="10" />
<hkern u1="a" u2="&#xc4;" k="10" />
<hkern u1="a" u2="&#xc3;" k="10" />
<hkern u1="a" u2="&#xc2;" k="10" />
<hkern u1="a" u2="&#xc1;" k="10" />
<hkern u1="a" u2="&#xc0;" k="10" />
<hkern u1="a" u2="&#x7d;" k="41" />
<hkern u1="a" u2="y" k="14" />
<hkern u1="a" u2="v" k="20" />
<hkern u1="a" u2="u" k="8" />
<hkern u1="a" u2="t" k="10" />
<hkern u1="a" u2="f" k="12" />
<hkern u1="a" u2="]" k="47" />
<hkern u1="a" u2="\" k="47" />
<hkern u1="a" u2="Y" k="176" />
<hkern u1="a" u2="X" k="12" />
<hkern u1="a" u2="V" k="109" />
<hkern u1="a" u2="U" k="45" />
<hkern u1="a" u2="T" k="186" />
<hkern u1="a" u2="S" k="25" />
<hkern u1="a" u2="R" k="29" />
<hkern u1="a" u2="Q" k="20" />
<hkern u1="a" u2="P" k="29" />
<hkern u1="a" u2="O" k="20" />
<hkern u1="a" u2="N" k="29" />
<hkern u1="a" u2="M" k="29" />
<hkern u1="a" u2="L" k="29" />
<hkern u1="a" u2="K" k="29" />
<hkern u1="a" u2="I" k="29" />
<hkern u1="a" u2="H" k="29" />
<hkern u1="a" u2="G" k="20" />
<hkern u1="a" u2="F" k="29" />
<hkern u1="a" u2="E" k="29" />
<hkern u1="a" u2="D" k="29" />
<hkern u1="a" u2="C" k="20" />
<hkern u1="a" u2="B" k="29" />
<hkern u1="a" u2="A" k="10" />
<hkern u1="a" u2="&#x3f;" k="39" />
<hkern u1="a" u2="&#x2a;" k="29" />
<hkern u1="a" u2="&#x29;" k="41" />
<hkern u1="a" u2="&#x27;" k="29" />
<hkern u1="a" u2="&#x26;" k="12" />
<hkern u1="a" u2="&#x22;" k="29" />
<hkern u1="b" u2="&#xfb04;" k="10" />
<hkern u1="b" u2="&#xfb03;" k="10" />
<hkern u1="b" u2="&#xfb02;" k="10" />
<hkern u1="b" u2="&#xfb01;" k="10" />
<hkern u1="b" u2="&#x2122;" k="47" />
<hkern u1="b" u2="&#x201d;" k="33" />
<hkern u1="b" u2="&#x201c;" k="31" />
<hkern u1="b" u2="&#x2019;" k="33" />
<hkern u1="b" u2="&#x2018;" k="31" />
<hkern u1="b" u2="&#xff;" k="10" />
<hkern u1="b" u2="&#xfd;" k="10" />
<hkern u1="b" u2="&#xdf;" k="10" />
<hkern u1="b" u2="&#xde;" k="29" />
<hkern u1="b" u2="&#xdd;" k="178" />
<hkern u1="b" u2="&#xdc;" k="31" />
<hkern u1="b" u2="&#xdb;" k="31" />
<hkern u1="b" u2="&#xda;" k="31" />
<hkern u1="b" u2="&#xd9;" k="31" />
<hkern u1="b" u2="&#xd8;" k="12" />
<hkern u1="b" u2="&#xd6;" k="12" />
<hkern u1="b" u2="&#xd5;" k="12" />
<hkern u1="b" u2="&#xd4;" k="12" />
<hkern u1="b" u2="&#xd3;" k="12" />
<hkern u1="b" u2="&#xd2;" k="12" />
<hkern u1="b" u2="&#xd1;" k="29" />
<hkern u1="b" u2="&#xd0;" k="29" />
<hkern u1="b" u2="&#xcf;" k="29" />
<hkern u1="b" u2="&#xce;" k="29" />
<hkern u1="b" u2="&#xcd;" k="29" />
<hkern u1="b" u2="&#xcc;" k="29" />
<hkern u1="b" u2="&#xcb;" k="29" />
<hkern u1="b" u2="&#xca;" k="29" />
<hkern u1="b" u2="&#xc9;" k="29" />
<hkern u1="b" u2="&#xc8;" k="29" />
<hkern u1="b" u2="&#xc7;" k="12" />
<hkern u1="b" u2="&#xc5;" k="25" />
<hkern u1="b" u2="&#xc4;" k="25" />
<hkern u1="b" u2="&#xc3;" k="25" />
<hkern u1="b" u2="&#xc2;" k="25" />
<hkern u1="b" u2="&#xc1;" k="25" />
<hkern u1="b" u2="&#xc0;" k="25" />
<hkern u1="b" u2="&#x7d;" k="47" />
<hkern u1="b" u2="y" k="10" />
<hkern u1="b" u2="x" k="31" />
<hkern u1="b" u2="v" k="14" />
<hkern u1="b" u2="t" k="8" />
<hkern u1="b" u2="f" k="10" />
<hkern u1="b" u2="]" k="51" />
<hkern u1="b" u2="\" k="45" />
<hkern u1="b" u2="Y" k="178" />
<hkern u1="b" u2="X" k="84" />
<hkern u1="b" u2="V" k="109" />
<hkern u1="b" u2="U" k="31" />
<hkern u1="b" u2="T" k="176" />
<hkern u1="b" u2="S" k="29" />
<hkern u1="b" u2="R" k="29" />
<hkern u1="b" u2="Q" k="12" />
<hkern u1="b" u2="P" k="29" />
<hkern u1="b" u2="O" k="12" />
<hkern u1="b" u2="N" k="29" />
<hkern u1="b" u2="M" k="29" />
<hkern u1="b" u2="L" k="29" />
<hkern u1="b" u2="K" k="29" />
<hkern u1="b" u2="I" k="29" />
<hkern u1="b" u2="H" k="29" />
<hkern u1="b" u2="G" k="12" />
<hkern u1="b" u2="F" k="29" />
<hkern u1="b" u2="E" k="29" />
<hkern u1="b" u2="D" k="29" />
<hkern u1="b" u2="C" k="12" />
<hkern u1="b" u2="B" k="29" />
<hkern u1="b" u2="A" k="25" />
<hkern u1="b" u2="&#x3f;" k="41" />
<hkern u1="b" u2="&#x2a;" k="29" />
<hkern u1="b" u2="&#x29;" k="55" />
<hkern u1="b" u2="&#x27;" k="33" />
<hkern u1="b" u2="&#x22;" k="33" />
<hkern u1="c" u2="&#x2122;" k="25" />
<hkern u1="c" u2="&#x2014;" k="25" />
<hkern u1="c" u2="&#x2013;" k="25" />
<hkern u1="c" u2="&#xf8;" k="8" />
<hkern u1="c" u2="&#xf6;" k="8" />
<hkern u1="c" u2="&#xf5;" k="8" />
<hkern u1="c" u2="&#xf4;" k="8" />
<hkern u1="c" u2="&#xf3;" k="8" />
<hkern u1="c" u2="&#xf2;" k="8" />
<hkern u1="c" u2="&#xf0;" k="8" />
<hkern u1="c" u2="&#xeb;" k="8" />
<hkern u1="c" u2="&#xea;" k="8" />
<hkern u1="c" u2="&#xe9;" k="8" />
<hkern u1="c" u2="&#xe8;" k="8" />
<hkern u1="c" u2="&#xe7;" k="8" />
<hkern u1="c" u2="&#xdd;" k="143" />
<hkern u1="c" u2="&#xdc;" k="18" />
<hkern u1="c" u2="&#xdb;" k="18" />
<hkern u1="c" u2="&#xda;" k="18" />
<hkern u1="c" u2="&#xd9;" k="18" />
<hkern u1="c" u2="&#xd8;" k="23" />
<hkern u1="c" u2="&#xd6;" k="23" />
<hkern u1="c" u2="&#xd5;" k="23" />
<hkern u1="c" u2="&#xd4;" k="23" />
<hkern u1="c" u2="&#xd3;" k="23" />
<hkern u1="c" u2="&#xd2;" k="23" />
<hkern u1="c" u2="&#xc7;" k="23" />
<hkern u1="c" u2="q" k="8" />
<hkern u1="c" u2="o" k="8" />
<hkern u1="c" u2="g" k="8" />
<hkern u1="c" u2="e" k="8" />
<hkern u1="c" u2="d" k="8" />
<hkern u1="c" u2="c" k="8" />
<hkern u1="c" u2="]" k="25" />
<hkern u1="c" u2="Y" k="143" />
<hkern u1="c" u2="V" k="63" />
<hkern u1="c" u2="U" k="18" />
<hkern u1="c" u2="T" k="197" />
<hkern u1="c" u2="S" k="23" />
<hkern u1="c" u2="Q" k="23" />
<hkern u1="c" u2="O" k="23" />
<hkern u1="c" u2="G" k="23" />
<hkern u1="c" u2="C" k="23" />
<hkern u1="c" u2="&#x2d;" k="25" />
<hkern u1="c" u2="&#x29;" k="20" />
<hkern u1="c" u2="&#x26;" k="35" />
<hkern u1="e" u2="&#x2122;" k="43" />
<hkern u1="e" u2="&#x201d;" k="25" />
<hkern u1="e" u2="&#x201c;" k="23" />
<hkern u1="e" u2="&#x2019;" k="25" />
<hkern u1="e" u2="&#x2018;" k="23" />
<hkern u1="e" u2="&#xde;" k="23" />
<hkern u1="e" u2="&#xdd;" k="170" />
<hkern u1="e" u2="&#xdc;" k="27" />
<hkern u1="e" u2="&#xdb;" k="27" />
<hkern u1="e" u2="&#xda;" k="27" />
<hkern u1="e" u2="&#xd9;" k="27" />
<hkern u1="e" u2="&#xd8;" k="10" />
<hkern u1="e" u2="&#xd6;" k="10" />
<hkern u1="e" u2="&#xd5;" k="10" />
<hkern u1="e" u2="&#xd4;" k="10" />
<hkern u1="e" u2="&#xd3;" k="10" />
<hkern u1="e" u2="&#xd2;" k="10" />
<hkern u1="e" u2="&#xd1;" k="23" />
<hkern u1="e" u2="&#xd0;" k="23" />
<hkern u1="e" u2="&#xcf;" k="23" />
<hkern u1="e" u2="&#xce;" k="23" />
<hkern u1="e" u2="&#xcd;" k="23" />
<hkern u1="e" u2="&#xcc;" k="23" />
<hkern u1="e" u2="&#xcb;" k="23" />
<hkern u1="e" u2="&#xca;" k="23" />
<hkern u1="e" u2="&#xc9;" k="23" />
<hkern u1="e" u2="&#xc8;" k="23" />
<hkern u1="e" u2="&#xc7;" k="10" />
<hkern u1="e" u2="&#xc5;" k="10" />
<hkern u1="e" u2="&#xc4;" k="10" />
<hkern u1="e" u2="&#xc3;" k="10" />
<hkern u1="e" u2="&#xc2;" k="10" />
<hkern u1="e" u2="&#xc1;" k="10" />
<hkern u1="e" u2="&#xc0;" k="10" />
<hkern u1="e" u2="&#x7d;" k="39" />
<hkern u1="e" u2="x" k="12" />
<hkern u1="e" u2="v" k="10" />
<hkern u1="e" u2="]" k="43" />
<hkern u1="e" u2="\" k="39" />
<hkern u1="e" u2="Y" k="170" />
<hkern u1="e" u2="X" k="33" />
<hkern u1="e" u2="V" k="111" />
<hkern u1="e" u2="U" k="27" />
<hkern u1="e" u2="T" k="166" />
<hkern u1="e" u2="S" k="23" />
<hkern u1="e" u2="R" k="23" />
<hkern u1="e" u2="Q" k="10" />
<hkern u1="e" u2="P" k="23" />
<hkern u1="e" u2="O" k="10" />
<hkern u1="e" u2="N" k="23" />
<hkern u1="e" u2="M" k="23" />
<hkern u1="e" u2="L" k="23" />
<hkern u1="e" u2="K" k="23" />
<hkern u1="e" u2="I" k="23" />
<hkern u1="e" u2="H" k="23" />
<hkern u1="e" u2="G" k="10" />
<hkern u1="e" u2="F" k="23" />
<hkern u1="e" u2="E" k="23" />
<hkern u1="e" u2="D" k="23" />
<hkern u1="e" u2="C" k="10" />
<hkern u1="e" u2="B" k="23" />
<hkern u1="e" u2="A" k="10" />
<hkern u1="e" u2="&#x3f;" k="35" />
<hkern u1="e" u2="&#x2a;" k="23" />
<hkern u1="e" u2="&#x29;" k="43" />
<hkern u1="e" u2="&#x27;" k="25" />
<hkern u1="e" u2="&#x26;" k="12" />
<hkern u1="e" u2="&#x22;" k="25" />
<hkern u1="f" u2="&#x2039;" k="31" />
<hkern u1="f" u2="&#x2026;" k="59" />
<hkern u1="f" u2="&#x201e;" k="59" />
<hkern u1="f" u2="&#x201a;" k="59" />
<hkern u1="f" u2="&#x2014;" k="63" />
<hkern u1="f" u2="&#x2013;" k="63" />
<hkern u1="f" u2="&#xff;" k="-66" />
<hkern u1="f" u2="&#xfd;" k="-66" />
<hkern u1="f" u2="&#xf8;" k="18" />
<hkern u1="f" u2="&#xf6;" k="18" />
<hkern u1="f" u2="&#xf5;" k="18" />
<hkern u1="f" u2="&#xf4;" k="18" />
<hkern u1="f" u2="&#xf3;" k="18" />
<hkern u1="f" u2="&#xf2;" k="18" />
<hkern u1="f" u2="&#xf0;" k="18" />
<hkern u1="f" u2="&#xeb;" k="18" />
<hkern u1="f" u2="&#xea;" k="18" />
<hkern u1="f" u2="&#xe9;" k="18" />
<hkern u1="f" u2="&#xe8;" k="18" />
<hkern u1="f" u2="&#xe7;" k="18" />
<hkern u1="f" u2="&#xe6;" k="33" />
<hkern u1="f" u2="&#xe5;" k="33" />
<hkern u1="f" u2="&#xe4;" k="33" />
<hkern u1="f" u2="&#xe3;" k="33" />
<hkern u1="f" u2="&#xe2;" k="33" />
<hkern u1="f" u2="&#xe1;" k="33" />
<hkern u1="f" u2="&#xe0;" k="33" />
<hkern u1="f" u2="&#xdd;" k="-39" />
<hkern u1="f" u2="&#xc5;" k="92" />
<hkern u1="f" u2="&#xc4;" k="92" />
<hkern u1="f" u2="&#xc3;" k="92" />
<hkern u1="f" u2="&#xc2;" k="92" />
<hkern u1="f" u2="&#xc1;" k="92" />
<hkern u1="f" u2="&#xc0;" k="92" />
<hkern u1="f" u2="&#xab;" k="31" />
<hkern u1="f" u2="y" k="-66" />
<hkern u1="f" u2="x" k="-31" />
<hkern u1="f" u2="v" k="-66" />
<hkern u1="f" u2="t" k="-43" />
<hkern u1="f" u2="q" k="18" />
<hkern u1="f" u2="o" k="18" />
<hkern u1="f" u2="g" k="18" />
<hkern u1="f" u2="e" k="18" />
<hkern u1="f" u2="d" k="18" />
<hkern u1="f" u2="c" k="18" />
<hkern u1="f" u2="a" k="33" />
<hkern u1="f" u2="Y" k="-39" />
<hkern u1="f" u2="V" k="-14" />
<hkern u1="f" u2="A" k="92" />
<hkern u1="f" u2="&#x2f;" k="35" />
<hkern u1="f" u2="&#x2e;" k="59" />
<hkern u1="f" u2="&#x2d;" k="63" />
<hkern u1="f" u2="&#x2c;" k="59" />
<hkern u1="f" u2="&#x26;" k="39" />
<hkern u1="g" u2="&#x2122;" k="37" />
<hkern u1="g" u2="&#xde;" k="20" />
<hkern u1="g" u2="&#xdd;" k="143" />
<hkern u1="g" u2="&#xdc;" k="37" />
<hkern u1="g" u2="&#xdb;" k="37" />
<hkern u1="g" u2="&#xda;" k="37" />
<hkern u1="g" u2="&#xd9;" k="37" />
<hkern u1="g" u2="&#xd8;" k="18" />
<hkern u1="g" u2="&#xd6;" k="18" />
<hkern u1="g" u2="&#xd5;" k="18" />
<hkern u1="g" u2="&#xd4;" k="18" />
<hkern u1="g" u2="&#xd3;" k="18" />
<hkern u1="g" u2="&#xd2;" k="18" />
<hkern u1="g" u2="&#xd1;" k="20" />
<hkern u1="g" u2="&#xd0;" k="20" />
<hkern u1="g" u2="&#xcf;" k="20" />
<hkern u1="g" u2="&#xce;" k="20" />
<hkern u1="g" u2="&#xcd;" k="20" />
<hkern u1="g" u2="&#xcc;" k="20" />
<hkern u1="g" u2="&#xcb;" k="20" />
<hkern u1="g" u2="&#xca;" k="20" />
<hkern u1="g" u2="&#xc9;" k="20" />
<hkern u1="g" u2="&#xc8;" k="20" />
<hkern u1="g" u2="&#xc7;" k="18" />
<hkern u1="g" u2="&#xc5;" k="12" />
<hkern u1="g" u2="&#xc4;" k="12" />
<hkern u1="g" u2="&#xc3;" k="12" />
<hkern u1="g" u2="&#xc2;" k="12" />
<hkern u1="g" u2="&#xc1;" k="12" />
<hkern u1="g" u2="&#xc0;" k="12" />
<hkern u1="g" u2="&#x7d;" k="37" />
<hkern u1="g" u2="]" k="41" />
<hkern u1="g" u2="\" k="31" />
<hkern u1="g" u2="Y" k="143" />
<hkern u1="g" u2="X" k="16" />
<hkern u1="g" u2="V" k="90" />
<hkern u1="g" u2="U" k="37" />
<hkern u1="g" u2="T" k="137" />
<hkern u1="g" u2="S" k="16" />
<hkern u1="g" u2="R" k="20" />
<hkern u1="g" u2="Q" k="18" />
<hkern u1="g" u2="P" k="20" />
<hkern u1="g" u2="O" k="18" />
<hkern u1="g" u2="N" k="20" />
<hkern u1="g" u2="M" k="20" />
<hkern u1="g" u2="L" k="20" />
<hkern u1="g" u2="K" k="20" />
<hkern u1="g" u2="I" k="20" />
<hkern u1="g" u2="H" k="20" />
<hkern u1="g" u2="G" k="18" />
<hkern u1="g" u2="F" k="20" />
<hkern u1="g" u2="E" k="20" />
<hkern u1="g" u2="D" k="20" />
<hkern u1="g" u2="C" k="18" />
<hkern u1="g" u2="B" k="20" />
<hkern u1="g" u2="A" k="12" />
<hkern u1="g" u2="&#x29;" k="37" />
<hkern u1="g" u2="&#x26;" k="14" />
<hkern u1="h" u2="&#xfb04;" k="10" />
<hkern u1="h" u2="&#xfb03;" k="10" />
<hkern u1="h" u2="&#xfb02;" k="10" />
<hkern u1="h" u2="&#xfb01;" k="10" />
<hkern u1="h" u2="&#x2122;" k="49" />
<hkern u1="h" u2="&#x201d;" k="29" />
<hkern u1="h" u2="&#x201c;" k="23" />
<hkern u1="h" u2="&#x2019;" k="29" />
<hkern u1="h" u2="&#x2018;" k="23" />
<hkern u1="h" u2="&#xff;" k="12" />
<hkern u1="h" u2="&#xfd;" k="12" />
<hkern u1="h" u2="&#xdf;" k="10" />
<hkern u1="h" u2="&#xde;" k="29" />
<hkern u1="h" u2="&#xdd;" k="166" />
<hkern u1="h" u2="&#xdc;" k="43" />
<hkern u1="h" u2="&#xdb;" k="43" />
<hkern u1="h" u2="&#xda;" k="43" />
<hkern u1="h" u2="&#xd9;" k="43" />
<hkern u1="h" u2="&#xd8;" k="20" />
<hkern u1="h" u2="&#xd6;" k="20" />
<hkern u1="h" u2="&#xd5;" k="20" />
<hkern u1="h" u2="&#xd4;" k="20" />
<hkern u1="h" u2="&#xd3;" k="20" />
<hkern u1="h" u2="&#xd2;" k="20" />
<hkern u1="h" u2="&#xd1;" k="29" />
<hkern u1="h" u2="&#xd0;" k="29" />
<hkern u1="h" u2="&#xcf;" k="29" />
<hkern u1="h" u2="&#xce;" k="29" />
<hkern u1="h" u2="&#xcd;" k="29" />
<hkern u1="h" u2="&#xcc;" k="29" />
<hkern u1="h" u2="&#xcb;" k="29" />
<hkern u1="h" u2="&#xca;" k="29" />
<hkern u1="h" u2="&#xc9;" k="29" />
<hkern u1="h" u2="&#xc8;" k="29" />
<hkern u1="h" u2="&#xc7;" k="20" />
<hkern u1="h" u2="&#xc5;" k="10" />
<hkern u1="h" u2="&#xc4;" k="10" />
<hkern u1="h" u2="&#xc3;" k="10" />
<hkern u1="h" u2="&#xc2;" k="10" />
<hkern u1="h" u2="&#xc1;" k="10" />
<hkern u1="h" u2="&#xc0;" k="10" />
<hkern u1="h" u2="&#x7d;" k="41" />
<hkern u1="h" u2="y" k="12" />
<hkern u1="h" u2="v" k="18" />
<hkern u1="h" u2="t" k="8" />
<hkern u1="h" u2="f" k="10" />
<hkern u1="h" u2="]" k="47" />
<hkern u1="h" u2="\" k="45" />
<hkern u1="h" u2="Y" k="166" />
<hkern u1="h" u2="X" k="12" />
<hkern u1="h" u2="V" k="106" />
<hkern u1="h" u2="U" k="43" />
<hkern u1="h" u2="T" k="184" />
<hkern u1="h" u2="S" k="23" />
<hkern u1="h" u2="R" k="29" />
<hkern u1="h" u2="Q" k="20" />
<hkern u1="h" u2="P" k="29" />
<hkern u1="h" u2="O" k="20" />
<hkern u1="h" u2="N" k="29" />
<hkern u1="h" u2="M" k="29" />
<hkern u1="h" u2="L" k="29" />
<hkern u1="h" u2="K" k="29" />
<hkern u1="h" u2="I" k="29" />
<hkern u1="h" u2="H" k="29" />
<hkern u1="h" u2="G" k="20" />
<hkern u1="h" u2="F" k="29" />
<hkern u1="h" u2="E" k="29" />
<hkern u1="h" u2="D" k="29" />
<hkern u1="h" u2="C" k="20" />
<hkern u1="h" u2="B" k="29" />
<hkern u1="h" u2="A" k="10" />
<hkern u1="h" u2="&#x3f;" k="37" />
<hkern u1="h" u2="&#x2a;" k="27" />
<hkern u1="h" u2="&#x29;" k="41" />
<hkern u1="h" u2="&#x27;" k="29" />
<hkern u1="h" u2="&#x26;" k="12" />
<hkern u1="h" u2="&#x22;" k="29" />
<hkern u1="i" u2="&#xde;" k="20" />
<hkern u1="i" u2="&#xdd;" k="16" />
<hkern u1="i" u2="&#xdc;" k="31" />
<hkern u1="i" u2="&#xdb;" k="31" />
<hkern u1="i" u2="&#xda;" k="31" />
<hkern u1="i" u2="&#xd9;" k="31" />
<hkern u1="i" u2="&#xd8;" k="20" />
<hkern u1="i" u2="&#xd6;" k="20" />
<hkern u1="i" u2="&#xd5;" k="20" />
<hkern u1="i" u2="&#xd4;" k="20" />
<hkern u1="i" u2="&#xd3;" k="20" />
<hkern u1="i" u2="&#xd2;" k="20" />
<hkern u1="i" u2="&#xd1;" k="20" />
<hkern u1="i" u2="&#xd0;" k="20" />
<hkern u1="i" u2="&#xcf;" k="20" />
<hkern u1="i" u2="&#xce;" k="20" />
<hkern u1="i" u2="&#xcd;" k="20" />
<hkern u1="i" u2="&#xcc;" k="20" />
<hkern u1="i" u2="&#xcb;" k="20" />
<hkern u1="i" u2="&#xca;" k="20" />
<hkern u1="i" u2="&#xc9;" k="20" />
<hkern u1="i" u2="&#xc8;" k="20" />
<hkern u1="i" u2="&#xc7;" k="20" />
<hkern u1="i" u2="&#xc5;" k="14" />
<hkern u1="i" u2="&#xc4;" k="14" />
<hkern u1="i" u2="&#xc3;" k="14" />
<hkern u1="i" u2="&#xc2;" k="14" />
<hkern u1="i" u2="&#xc1;" k="14" />
<hkern u1="i" u2="&#xc0;" k="14" />
<hkern u1="i" u2="Y" k="16" />
<hkern u1="i" u2="V" k="18" />
<hkern u1="i" u2="U" k="31" />
<hkern u1="i" u2="T" k="20" />
<hkern u1="i" u2="S" k="18" />
<hkern u1="i" u2="R" k="20" />
<hkern u1="i" u2="Q" k="20" />
<hkern u1="i" u2="P" k="20" />
<hkern u1="i" u2="O" k="20" />
<hkern u1="i" u2="N" k="20" />
<hkern u1="i" u2="M" k="20" />
<hkern u1="i" u2="L" k="20" />
<hkern u1="i" u2="K" k="20" />
<hkern u1="i" u2="I" k="20" />
<hkern u1="i" u2="H" k="20" />
<hkern u1="i" u2="G" k="20" />
<hkern u1="i" u2="F" k="20" />
<hkern u1="i" u2="E" k="20" />
<hkern u1="i" u2="D" k="20" />
<hkern u1="i" u2="C" k="20" />
<hkern u1="i" u2="B" k="20" />
<hkern u1="i" u2="A" k="14" />
<hkern u1="i" u2="&#x26;" k="16" />
<hkern u1="j" u2="&#xde;" k="20" />
<hkern u1="j" u2="&#xdd;" k="16" />
<hkern u1="j" u2="&#xdc;" k="31" />
<hkern u1="j" u2="&#xdb;" k="31" />
<hkern u1="j" u2="&#xda;" k="31" />
<hkern u1="j" u2="&#xd9;" k="31" />
<hkern u1="j" u2="&#xd8;" k="20" />
<hkern u1="j" u2="&#xd6;" k="20" />
<hkern u1="j" u2="&#xd5;" k="20" />
<hkern u1="j" u2="&#xd4;" k="20" />
<hkern u1="j" u2="&#xd3;" k="20" />
<hkern u1="j" u2="&#xd2;" k="20" />
<hkern u1="j" u2="&#xd1;" k="20" />
<hkern u1="j" u2="&#xd0;" k="20" />
<hkern u1="j" u2="&#xcf;" k="20" />
<hkern u1="j" u2="&#xce;" k="20" />
<hkern u1="j" u2="&#xcd;" k="20" />
<hkern u1="j" u2="&#xcc;" k="20" />
<hkern u1="j" u2="&#xcb;" k="20" />
<hkern u1="j" u2="&#xca;" k="20" />
<hkern u1="j" u2="&#xc9;" k="20" />
<hkern u1="j" u2="&#xc8;" k="20" />
<hkern u1="j" u2="&#xc7;" k="20" />
<hkern u1="j" u2="&#xc5;" k="14" />
<hkern u1="j" u2="&#xc4;" k="14" />
<hkern u1="j" u2="&#xc3;" k="14" />
<hkern u1="j" u2="&#xc2;" k="14" />
<hkern u1="j" u2="&#xc1;" k="14" />
<hkern u1="j" u2="&#xc0;" k="14" />
<hkern u1="j" u2="Y" k="16" />
<hkern u1="j" u2="V" k="18" />
<hkern u1="j" u2="U" k="31" />
<hkern u1="j" u2="T" k="20" />
<hkern u1="j" u2="S" k="18" />
<hkern u1="j" u2="R" k="20" />
<hkern u1="j" u2="Q" k="20" />
<hkern u1="j" u2="P" k="20" />
<hkern u1="j" u2="O" k="20" />
<hkern u1="j" u2="N" k="20" />
<hkern u1="j" u2="M" k="20" />
<hkern u1="j" u2="L" k="20" />
<hkern u1="j" u2="K" k="20" />
<hkern u1="j" u2="I" k="20" />
<hkern u1="j" u2="H" k="20" />
<hkern u1="j" u2="G" k="20" />
<hkern u1="j" u2="F" k="20" />
<hkern u1="j" u2="E" k="20" />
<hkern u1="j" u2="D" k="20" />
<hkern u1="j" u2="C" k="20" />
<hkern u1="j" u2="B" k="20" />
<hkern u1="j" u2="A" k="14" />
<hkern u1="j" u2="&#x26;" k="16" />
<hkern u1="l" u2="&#xde;" k="20" />
<hkern u1="l" u2="&#xdd;" k="23" />
<hkern u1="l" u2="&#xdc;" k="37" />
<hkern u1="l" u2="&#xdb;" k="37" />
<hkern u1="l" u2="&#xda;" k="37" />
<hkern u1="l" u2="&#xd9;" k="37" />
<hkern u1="l" u2="&#xd8;" k="23" />
<hkern u1="l" u2="&#xd6;" k="23" />
<hkern u1="l" u2="&#xd5;" k="23" />
<hkern u1="l" u2="&#xd4;" k="23" />
<hkern u1="l" u2="&#xd3;" k="23" />
<hkern u1="l" u2="&#xd2;" k="23" />
<hkern u1="l" u2="&#xd1;" k="20" />
<hkern u1="l" u2="&#xd0;" k="20" />
<hkern u1="l" u2="&#xcf;" k="20" />
<hkern u1="l" u2="&#xce;" k="20" />
<hkern u1="l" u2="&#xcd;" k="20" />
<hkern u1="l" u2="&#xcc;" k="20" />
<hkern u1="l" u2="&#xcb;" k="20" />
<hkern u1="l" u2="&#xca;" k="20" />
<hkern u1="l" u2="&#xc9;" k="20" />
<hkern u1="l" u2="&#xc8;" k="20" />
<hkern u1="l" u2="&#xc7;" k="23" />
<hkern u1="l" u2="&#xc5;" k="16" />
<hkern u1="l" u2="&#xc4;" k="16" />
<hkern u1="l" u2="&#xc3;" k="16" />
<hkern u1="l" u2="&#xc2;" k="16" />
<hkern u1="l" u2="&#xc1;" k="16" />
<hkern u1="l" u2="&#xc0;" k="16" />
<hkern u1="l" u2="&#xb7;" k="123" />
<hkern u1="l" u2="Y" k="23" />
<hkern u1="l" u2="V" k="23" />
<hkern u1="l" u2="U" k="37" />
<hkern u1="l" u2="T" k="18" />
<hkern u1="l" u2="S" k="18" />
<hkern u1="l" u2="R" k="20" />
<hkern u1="l" u2="Q" k="23" />
<hkern u1="l" u2="P" k="20" />
<hkern u1="l" u2="O" k="23" />
<hkern u1="l" u2="N" k="20" />
<hkern u1="l" u2="M" k="20" />
<hkern u1="l" u2="L" k="20" />
<hkern u1="l" u2="K" k="20" />
<hkern u1="l" u2="I" k="20" />
<hkern u1="l" u2="H" k="20" />
<hkern u1="l" u2="G" k="23" />
<hkern u1="l" u2="F" k="20" />
<hkern u1="l" u2="E" k="20" />
<hkern u1="l" u2="D" k="20" />
<hkern u1="l" u2="C" k="23" />
<hkern u1="l" u2="B" k="20" />
<hkern u1="l" u2="A" k="16" />
<hkern u1="l" u2="&#x26;" k="18" />
<hkern u1="m" u2="&#xfb04;" k="10" />
<hkern u1="m" u2="&#xfb03;" k="10" />
<hkern u1="m" u2="&#xfb02;" k="10" />
<hkern u1="m" u2="&#xfb01;" k="10" />
<hkern u1="m" u2="&#x2122;" k="49" />
<hkern u1="m" u2="&#x201d;" k="29" />
<hkern u1="m" u2="&#x201c;" k="23" />
<hkern u1="m" u2="&#x2019;" k="29" />
<hkern u1="m" u2="&#x2018;" k="23" />
<hkern u1="m" u2="&#xff;" k="12" />
<hkern u1="m" u2="&#xfd;" k="12" />
<hkern u1="m" u2="&#xdf;" k="10" />
<hkern u1="m" u2="&#xde;" k="29" />
<hkern u1="m" u2="&#xdd;" k="166" />
<hkern u1="m" u2="&#xdc;" k="43" />
<hkern u1="m" u2="&#xdb;" k="43" />
<hkern u1="m" u2="&#xda;" k="43" />
<hkern u1="m" u2="&#xd9;" k="43" />
<hkern u1="m" u2="&#xd8;" k="20" />
<hkern u1="m" u2="&#xd6;" k="20" />
<hkern u1="m" u2="&#xd5;" k="20" />
<hkern u1="m" u2="&#xd4;" k="20" />
<hkern u1="m" u2="&#xd3;" k="20" />
<hkern u1="m" u2="&#xd2;" k="20" />
<hkern u1="m" u2="&#xd1;" k="29" />
<hkern u1="m" u2="&#xd0;" k="29" />
<hkern u1="m" u2="&#xcf;" k="29" />
<hkern u1="m" u2="&#xce;" k="29" />
<hkern u1="m" u2="&#xcd;" k="29" />
<hkern u1="m" u2="&#xcc;" k="29" />
<hkern u1="m" u2="&#xcb;" k="29" />
<hkern u1="m" u2="&#xca;" k="29" />
<hkern u1="m" u2="&#xc9;" k="29" />
<hkern u1="m" u2="&#xc8;" k="29" />
<hkern u1="m" u2="&#xc7;" k="20" />
<hkern u1="m" u2="&#xc5;" k="10" />
<hkern u1="m" u2="&#xc4;" k="10" />
<hkern u1="m" u2="&#xc3;" k="10" />
<hkern u1="m" u2="&#xc2;" k="10" />
<hkern u1="m" u2="&#xc1;" k="10" />
<hkern u1="m" u2="&#xc0;" k="10" />
<hkern u1="m" u2="&#x7d;" k="41" />
<hkern u1="m" u2="y" k="12" />
<hkern u1="m" u2="v" k="18" />
<hkern u1="m" u2="t" k="8" />
<hkern u1="m" u2="f" k="10" />
<hkern u1="m" u2="]" k="47" />
<hkern u1="m" u2="\" k="45" />
<hkern u1="m" u2="Y" k="166" />
<hkern u1="m" u2="X" k="12" />
<hkern u1="m" u2="V" k="106" />
<hkern u1="m" u2="U" k="43" />
<hkern u1="m" u2="T" k="184" />
<hkern u1="m" u2="S" k="23" />
<hkern u1="m" u2="R" k="29" />
<hkern u1="m" u2="Q" k="20" />
<hkern u1="m" u2="P" k="29" />
<hkern u1="m" u2="O" k="20" />
<hkern u1="m" u2="N" k="29" />
<hkern u1="m" u2="M" k="29" />
<hkern u1="m" u2="L" k="29" />
<hkern u1="m" u2="K" k="29" />
<hkern u1="m" u2="I" k="29" />
<hkern u1="m" u2="H" k="29" />
<hkern u1="m" u2="G" k="20" />
<hkern u1="m" u2="F" k="29" />
<hkern u1="m" u2="E" k="29" />
<hkern u1="m" u2="D" k="29" />
<hkern u1="m" u2="C" k="20" />
<hkern u1="m" u2="B" k="29" />
<hkern u1="m" u2="A" k="10" />
<hkern u1="m" u2="&#x3f;" k="37" />
<hkern u1="m" u2="&#x2a;" k="27" />
<hkern u1="m" u2="&#x29;" k="41" />
<hkern u1="m" u2="&#x27;" k="29" />
<hkern u1="m" u2="&#x26;" k="12" />
<hkern u1="m" u2="&#x22;" k="29" />
<hkern u1="n" u2="&#xfb04;" k="10" />
<hkern u1="n" u2="&#xfb03;" k="10" />
<hkern u1="n" u2="&#xfb02;" k="10" />
<hkern u1="n" u2="&#xfb01;" k="10" />
<hkern u1="n" u2="&#x2122;" k="49" />
<hkern u1="n" u2="&#x201d;" k="29" />
<hkern u1="n" u2="&#x201c;" k="23" />
<hkern u1="n" u2="&#x2019;" k="29" />
<hkern u1="n" u2="&#x2018;" k="23" />
<hkern u1="n" u2="&#xff;" k="12" />
<hkern u1="n" u2="&#xfd;" k="12" />
<hkern u1="n" u2="&#xdf;" k="10" />
<hkern u1="n" u2="&#xde;" k="29" />
<hkern u1="n" u2="&#xdd;" k="166" />
<hkern u1="n" u2="&#xdc;" k="43" />
<hkern u1="n" u2="&#xdb;" k="43" />
<hkern u1="n" u2="&#xda;" k="43" />
<hkern u1="n" u2="&#xd9;" k="43" />
<hkern u1="n" u2="&#xd8;" k="20" />
<hkern u1="n" u2="&#xd6;" k="20" />
<hkern u1="n" u2="&#xd5;" k="20" />
<hkern u1="n" u2="&#xd4;" k="20" />
<hkern u1="n" u2="&#xd3;" k="20" />
<hkern u1="n" u2="&#xd2;" k="20" />
<hkern u1="n" u2="&#xd1;" k="29" />
<hkern u1="n" u2="&#xd0;" k="29" />
<hkern u1="n" u2="&#xcf;" k="29" />
<hkern u1="n" u2="&#xce;" k="29" />
<hkern u1="n" u2="&#xcd;" k="29" />
<hkern u1="n" u2="&#xcc;" k="29" />
<hkern u1="n" u2="&#xcb;" k="29" />
<hkern u1="n" u2="&#xca;" k="29" />
<hkern u1="n" u2="&#xc9;" k="29" />
<hkern u1="n" u2="&#xc8;" k="29" />
<hkern u1="n" u2="&#xc7;" k="20" />
<hkern u1="n" u2="&#xc5;" k="10" />
<hkern u1="n" u2="&#xc4;" k="10" />
<hkern u1="n" u2="&#xc3;" k="10" />
<hkern u1="n" u2="&#xc2;" k="10" />
<hkern u1="n" u2="&#xc1;" k="10" />
<hkern u1="n" u2="&#xc0;" k="10" />
<hkern u1="n" u2="&#x7d;" k="41" />
<hkern u1="n" u2="y" k="12" />
<hkern u1="n" u2="v" k="18" />
<hkern u1="n" u2="t" k="8" />
<hkern u1="n" u2="f" k="10" />
<hkern u1="n" u2="]" k="47" />
<hkern u1="n" u2="\" k="45" />
<hkern u1="n" u2="Y" k="166" />
<hkern u1="n" u2="X" k="12" />
<hkern u1="n" u2="V" k="106" />
<hkern u1="n" u2="U" k="43" />
<hkern u1="n" u2="T" k="184" />
<hkern u1="n" u2="S" k="23" />
<hkern u1="n" u2="R" k="29" />
<hkern u1="n" u2="Q" k="20" />
<hkern u1="n" u2="P" k="29" />
<hkern u1="n" u2="O" k="20" />
<hkern u1="n" u2="N" k="29" />
<hkern u1="n" u2="M" k="29" />
<hkern u1="n" u2="L" k="29" />
<hkern u1="n" u2="K" k="29" />
<hkern u1="n" u2="I" k="29" />
<hkern u1="n" u2="H" k="29" />
<hkern u1="n" u2="G" k="20" />
<hkern u1="n" u2="F" k="29" />
<hkern u1="n" u2="E" k="29" />
<hkern u1="n" u2="D" k="29" />
<hkern u1="n" u2="C" k="20" />
<hkern u1="n" u2="B" k="29" />
<hkern u1="n" u2="A" k="10" />
<hkern u1="n" u2="&#x3f;" k="37" />
<hkern u1="n" u2="&#x2a;" k="27" />
<hkern u1="n" u2="&#x29;" k="41" />
<hkern u1="n" u2="&#x27;" k="29" />
<hkern u1="n" u2="&#x26;" k="12" />
<hkern u1="n" u2="&#x22;" k="29" />
<hkern u1="o" u2="&#xfb04;" k="10" />
<hkern u1="o" u2="&#xfb03;" k="10" />
<hkern u1="o" u2="&#xfb02;" k="10" />
<hkern u1="o" u2="&#xfb01;" k="10" />
<hkern u1="o" u2="&#x2122;" k="47" />
<hkern u1="o" u2="&#x201d;" k="33" />
<hkern u1="o" u2="&#x201c;" k="31" />
<hkern u1="o" u2="&#x2019;" k="33" />
<hkern u1="o" u2="&#x2018;" k="31" />
<hkern u1="o" u2="&#xff;" k="10" />
<hkern u1="o" u2="&#xfd;" k="10" />
<hkern u1="o" u2="&#xdf;" k="10" />
<hkern u1="o" u2="&#xde;" k="29" />
<hkern u1="o" u2="&#xdd;" k="178" />
<hkern u1="o" u2="&#xdc;" k="31" />
<hkern u1="o" u2="&#xdb;" k="31" />
<hkern u1="o" u2="&#xda;" k="31" />
<hkern u1="o" u2="&#xd9;" k="31" />
<hkern u1="o" u2="&#xd8;" k="12" />
<hkern u1="o" u2="&#xd6;" k="12" />
<hkern u1="o" u2="&#xd5;" k="12" />
<hkern u1="o" u2="&#xd4;" k="12" />
<hkern u1="o" u2="&#xd3;" k="12" />
<hkern u1="o" u2="&#xd2;" k="12" />
<hkern u1="o" u2="&#xd1;" k="29" />
<hkern u1="o" u2="&#xd0;" k="29" />
<hkern u1="o" u2="&#xcf;" k="29" />
<hkern u1="o" u2="&#xce;" k="29" />
<hkern u1="o" u2="&#xcd;" k="29" />
<hkern u1="o" u2="&#xcc;" k="29" />
<hkern u1="o" u2="&#xcb;" k="29" />
<hkern u1="o" u2="&#xca;" k="29" />
<hkern u1="o" u2="&#xc9;" k="29" />
<hkern u1="o" u2="&#xc8;" k="29" />
<hkern u1="o" u2="&#xc7;" k="12" />
<hkern u1="o" u2="&#xc5;" k="25" />
<hkern u1="o" u2="&#xc4;" k="25" />
<hkern u1="o" u2="&#xc3;" k="25" />
<hkern u1="o" u2="&#xc2;" k="25" />
<hkern u1="o" u2="&#xc1;" k="25" />
<hkern u1="o" u2="&#xc0;" k="25" />
<hkern u1="o" u2="&#x7d;" k="47" />
<hkern u1="o" u2="y" k="10" />
<hkern u1="o" u2="x" k="31" />
<hkern u1="o" u2="v" k="14" />
<hkern u1="o" u2="t" k="8" />
<hkern u1="o" u2="f" k="10" />
<hkern u1="o" u2="]" k="51" />
<hkern u1="o" u2="\" k="45" />
<hkern u1="o" u2="Y" k="178" />
<hkern u1="o" u2="X" k="84" />
<hkern u1="o" u2="V" k="109" />
<hkern u1="o" u2="U" k="31" />
<hkern u1="o" u2="T" k="176" />
<hkern u1="o" u2="S" k="29" />
<hkern u1="o" u2="R" k="29" />
<hkern u1="o" u2="Q" k="12" />
<hkern u1="o" u2="P" k="29" />
<hkern u1="o" u2="O" k="12" />
<hkern u1="o" u2="N" k="29" />
<hkern u1="o" u2="M" k="29" />
<hkern u1="o" u2="L" k="29" />
<hkern u1="o" u2="K" k="29" />
<hkern u1="o" u2="I" k="29" />
<hkern u1="o" u2="H" k="29" />
<hkern u1="o" u2="G" k="12" />
<hkern u1="o" u2="F" k="29" />
<hkern u1="o" u2="E" k="29" />
<hkern u1="o" u2="D" k="29" />
<hkern u1="o" u2="C" k="12" />
<hkern u1="o" u2="B" k="29" />
<hkern u1="o" u2="A" k="25" />
<hkern u1="o" u2="&#x3f;" k="41" />
<hkern u1="o" u2="&#x2a;" k="29" />
<hkern u1="o" u2="&#x29;" k="55" />
<hkern u1="o" u2="&#x27;" k="33" />
<hkern u1="o" u2="&#x22;" k="33" />
<hkern u1="p" u2="&#xfb04;" k="10" />
<hkern u1="p" u2="&#xfb03;" k="10" />
<hkern u1="p" u2="&#xfb02;" k="10" />
<hkern u1="p" u2="&#xfb01;" k="10" />
<hkern u1="p" u2="&#x2122;" k="47" />
<hkern u1="p" u2="&#x201d;" k="33" />
<hkern u1="p" u2="&#x201c;" k="31" />
<hkern u1="p" u2="&#x2019;" k="33" />
<hkern u1="p" u2="&#x2018;" k="31" />
<hkern u1="p" u2="&#xff;" k="10" />
<hkern u1="p" u2="&#xfd;" k="10" />
<hkern u1="p" u2="&#xdf;" k="10" />
<hkern u1="p" u2="&#xde;" k="29" />
<hkern u1="p" u2="&#xdd;" k="178" />
<hkern u1="p" u2="&#xdc;" k="31" />
<hkern u1="p" u2="&#xdb;" k="31" />
<hkern u1="p" u2="&#xda;" k="31" />
<hkern u1="p" u2="&#xd9;" k="31" />
<hkern u1="p" u2="&#xd8;" k="12" />
<hkern u1="p" u2="&#xd6;" k="12" />
<hkern u1="p" u2="&#xd5;" k="12" />
<hkern u1="p" u2="&#xd4;" k="12" />
<hkern u1="p" u2="&#xd3;" k="12" />
<hkern u1="p" u2="&#xd2;" k="12" />
<hkern u1="p" u2="&#xd1;" k="29" />
<hkern u1="p" u2="&#xd0;" k="29" />
<hkern u1="p" u2="&#xcf;" k="29" />
<hkern u1="p" u2="&#xce;" k="29" />
<hkern u1="p" u2="&#xcd;" k="29" />
<hkern u1="p" u2="&#xcc;" k="29" />
<hkern u1="p" u2="&#xcb;" k="29" />
<hkern u1="p" u2="&#xca;" k="29" />
<hkern u1="p" u2="&#xc9;" k="29" />
<hkern u1="p" u2="&#xc8;" k="29" />
<hkern u1="p" u2="&#xc7;" k="12" />
<hkern u1="p" u2="&#xc5;" k="25" />
<hkern u1="p" u2="&#xc4;" k="25" />
<hkern u1="p" u2="&#xc3;" k="25" />
<hkern u1="p" u2="&#xc2;" k="25" />
<hkern u1="p" u2="&#xc1;" k="25" />
<hkern u1="p" u2="&#xc0;" k="25" />
<hkern u1="p" u2="&#x7d;" k="47" />
<hkern u1="p" u2="y" k="10" />
<hkern u1="p" u2="x" k="31" />
<hkern u1="p" u2="v" k="14" />
<hkern u1="p" u2="t" k="8" />
<hkern u1="p" u2="f" k="10" />
<hkern u1="p" u2="]" k="51" />
<hkern u1="p" u2="\" k="45" />
<hkern u1="p" u2="Y" k="178" />
<hkern u1="p" u2="X" k="84" />
<hkern u1="p" u2="V" k="109" />
<hkern u1="p" u2="U" k="31" />
<hkern u1="p" u2="T" k="176" />
<hkern u1="p" u2="S" k="29" />
<hkern u1="p" u2="R" k="29" />
<hkern u1="p" u2="Q" k="12" />
<hkern u1="p" u2="P" k="29" />
<hkern u1="p" u2="O" k="12" />
<hkern u1="p" u2="N" k="29" />
<hkern u1="p" u2="M" k="29" />
<hkern u1="p" u2="L" k="29" />
<hkern u1="p" u2="K" k="29" />
<hkern u1="p" u2="I" k="29" />
<hkern u1="p" u2="H" k="29" />
<hkern u1="p" u2="G" k="12" />
<hkern u1="p" u2="F" k="29" />
<hkern u1="p" u2="E" k="29" />
<hkern u1="p" u2="D" k="29" />
<hkern u1="p" u2="C" k="12" />
<hkern u1="p" u2="B" k="29" />
<hkern u1="p" u2="A" k="25" />
<hkern u1="p" u2="&#x3f;" k="41" />
<hkern u1="p" u2="&#x2a;" k="29" />
<hkern u1="p" u2="&#x29;" k="55" />
<hkern u1="p" u2="&#x27;" k="33" />
<hkern u1="p" u2="&#x22;" k="33" />
<hkern u1="q" u2="&#x2122;" k="37" />
<hkern u1="q" u2="&#xde;" k="20" />
<hkern u1="q" u2="&#xdd;" k="143" />
<hkern u1="q" u2="&#xdc;" k="37" />
<hkern u1="q" u2="&#xdb;" k="37" />
<hkern u1="q" u2="&#xda;" k="37" />
<hkern u1="q" u2="&#xd9;" k="37" />
<hkern u1="q" u2="&#xd8;" k="18" />
<hkern u1="q" u2="&#xd6;" k="18" />
<hkern u1="q" u2="&#xd5;" k="18" />
<hkern u1="q" u2="&#xd4;" k="18" />
<hkern u1="q" u2="&#xd3;" k="18" />
<hkern u1="q" u2="&#xd2;" k="18" />
<hkern u1="q" u2="&#xd1;" k="20" />
<hkern u1="q" u2="&#xd0;" k="20" />
<hkern u1="q" u2="&#xcf;" k="20" />
<hkern u1="q" u2="&#xce;" k="20" />
<hkern u1="q" u2="&#xcd;" k="20" />
<hkern u1="q" u2="&#xcc;" k="20" />
<hkern u1="q" u2="&#xcb;" k="20" />
<hkern u1="q" u2="&#xca;" k="20" />
<hkern u1="q" u2="&#xc9;" k="20" />
<hkern u1="q" u2="&#xc8;" k="20" />
<hkern u1="q" u2="&#xc7;" k="18" />
<hkern u1="q" u2="&#xc5;" k="12" />
<hkern u1="q" u2="&#xc4;" k="12" />
<hkern u1="q" u2="&#xc3;" k="12" />
<hkern u1="q" u2="&#xc2;" k="12" />
<hkern u1="q" u2="&#xc1;" k="12" />
<hkern u1="q" u2="&#xc0;" k="12" />
<hkern u1="q" u2="&#x7d;" k="37" />
<hkern u1="q" u2="]" k="41" />
<hkern u1="q" u2="\" k="31" />
<hkern u1="q" u2="Y" k="143" />
<hkern u1="q" u2="X" k="16" />
<hkern u1="q" u2="V" k="90" />
<hkern u1="q" u2="U" k="37" />
<hkern u1="q" u2="T" k="137" />
<hkern u1="q" u2="S" k="16" />
<hkern u1="q" u2="R" k="20" />
<hkern u1="q" u2="Q" k="18" />
<hkern u1="q" u2="P" k="20" />
<hkern u1="q" u2="O" k="18" />
<hkern u1="q" u2="N" k="20" />
<hkern u1="q" u2="M" k="20" />
<hkern u1="q" u2="L" k="20" />
<hkern u1="q" u2="K" k="20" />
<hkern u1="q" u2="I" k="20" />
<hkern u1="q" u2="H" k="20" />
<hkern u1="q" u2="G" k="18" />
<hkern u1="q" u2="F" k="20" />
<hkern u1="q" u2="E" k="20" />
<hkern u1="q" u2="D" k="20" />
<hkern u1="q" u2="C" k="18" />
<hkern u1="q" u2="B" k="20" />
<hkern u1="q" u2="A" k="12" />
<hkern u1="q" u2="&#x29;" k="37" />
<hkern u1="q" u2="&#x26;" k="14" />
<hkern u1="r" u2="]" k="43" />
<hkern u1="s" u2="&#x2122;" k="45" />
<hkern u1="s" u2="&#xde;" k="23" />
<hkern u1="s" u2="&#xdd;" k="164" />
<hkern u1="s" u2="&#xdc;" k="33" />
<hkern u1="s" u2="&#xdb;" k="33" />
<hkern u1="s" u2="&#xda;" k="33" />
<hkern u1="s" u2="&#xd9;" k="33" />
<hkern u1="s" u2="&#xd8;" k="18" />
<hkern u1="s" u2="&#xd6;" k="18" />
<hkern u1="s" u2="&#xd5;" k="18" />
<hkern u1="s" u2="&#xd4;" k="18" />
<hkern u1="s" u2="&#xd3;" k="18" />
<hkern u1="s" u2="&#xd2;" k="18" />
<hkern u1="s" u2="&#xd1;" k="23" />
<hkern u1="s" u2="&#xd0;" k="23" />
<hkern u1="s" u2="&#xcf;" k="23" />
<hkern u1="s" u2="&#xce;" k="23" />
<hkern u1="s" u2="&#xcd;" k="23" />
<hkern u1="s" u2="&#xcc;" k="23" />
<hkern u1="s" u2="&#xcb;" k="23" />
<hkern u1="s" u2="&#xca;" k="23" />
<hkern u1="s" u2="&#xc9;" k="23" />
<hkern u1="s" u2="&#xc8;" k="23" />
<hkern u1="s" u2="&#xc7;" k="18" />
<hkern u1="s" u2="&#x7d;" k="35" />
<hkern u1="s" u2="v" k="8" />
<hkern u1="s" u2="t" k="8" />
<hkern u1="s" u2="]" k="41" />
<hkern u1="s" u2="\" k="35" />
<hkern u1="s" u2="Y" k="164" />
<hkern u1="s" u2="X" k="25" />
<hkern u1="s" u2="V" k="109" />
<hkern u1="s" u2="U" k="33" />
<hkern u1="s" u2="T" k="160" />
<hkern u1="s" u2="S" k="12" />
<hkern u1="s" u2="R" k="23" />
<hkern u1="s" u2="Q" k="18" />
<hkern u1="s" u2="P" k="23" />
<hkern u1="s" u2="O" k="18" />
<hkern u1="s" u2="N" k="23" />
<hkern u1="s" u2="M" k="23" />
<hkern u1="s" u2="L" k="23" />
<hkern u1="s" u2="K" k="23" />
<hkern u1="s" u2="I" k="23" />
<hkern u1="s" u2="H" k="23" />
<hkern u1="s" u2="G" k="18" />
<hkern u1="s" u2="F" k="23" />
<hkern u1="s" u2="E" k="23" />
<hkern u1="s" u2="D" k="23" />
<hkern u1="s" u2="C" k="18" />
<hkern u1="s" u2="B" k="23" />
<hkern u1="s" u2="&#x3f;" k="35" />
<hkern u1="s" u2="&#x29;" k="39" />
<hkern u1="t" u2="&#x2122;" k="16" />
<hkern u1="t" u2="&#xff;" k="-31" />
<hkern u1="t" u2="&#xfd;" k="-31" />
<hkern u1="t" u2="&#xdd;" k="92" />
<hkern u1="t" u2="&#xdc;" k="14" />
<hkern u1="t" u2="&#xdb;" k="14" />
<hkern u1="t" u2="&#xda;" k="14" />
<hkern u1="t" u2="&#xd9;" k="14" />
<hkern u1="t" u2="y" k="-31" />
<hkern u1="t" u2="v" k="-35" />
<hkern u1="t" u2="t" k="-14" />
<hkern u1="t" u2="]" k="23" />
<hkern u1="t" u2="Y" k="92" />
<hkern u1="t" u2="V" k="37" />
<hkern u1="t" u2="U" k="14" />
<hkern u1="t" u2="T" k="150" />
<hkern u1="t" u2="&#x26;" k="23" />
<hkern u1="u" u2="&#x2122;" k="37" />
<hkern u1="u" u2="&#xde;" k="20" />
<hkern u1="u" u2="&#xdd;" k="143" />
<hkern u1="u" u2="&#xdc;" k="37" />
<hkern u1="u" u2="&#xdb;" k="37" />
<hkern u1="u" u2="&#xda;" k="37" />
<hkern u1="u" u2="&#xd9;" k="37" />
<hkern u1="u" u2="&#xd8;" k="18" />
<hkern u1="u" u2="&#xd6;" k="18" />
<hkern u1="u" u2="&#xd5;" k="18" />
<hkern u1="u" u2="&#xd4;" k="18" />
<hkern u1="u" u2="&#xd3;" k="18" />
<hkern u1="u" u2="&#xd2;" k="18" />
<hkern u1="u" u2="&#xd1;" k="20" />
<hkern u1="u" u2="&#xd0;" k="20" />
<hkern u1="u" u2="&#xcf;" k="20" />
<hkern u1="u" u2="&#xce;" k="20" />
<hkern u1="u" u2="&#xcd;" k="20" />
<hkern u1="u" u2="&#xcc;" k="20" />
<hkern u1="u" u2="&#xcb;" k="20" />
<hkern u1="u" u2="&#xca;" k="20" />
<hkern u1="u" u2="&#xc9;" k="20" />
<hkern u1="u" u2="&#xc8;" k="20" />
<hkern u1="u" u2="&#xc7;" k="18" />
<hkern u1="u" u2="&#xc5;" k="12" />
<hkern u1="u" u2="&#xc4;" k="12" />
<hkern u1="u" u2="&#xc3;" k="12" />
<hkern u1="u" u2="&#xc2;" k="12" />
<hkern u1="u" u2="&#xc1;" k="12" />
<hkern u1="u" u2="&#xc0;" k="12" />
<hkern u1="u" u2="&#x7d;" k="37" />
<hkern u1="u" u2="]" k="41" />
<hkern u1="u" u2="\" k="31" />
<hkern u1="u" u2="Y" k="143" />
<hkern u1="u" u2="X" k="16" />
<hkern u1="u" u2="V" k="90" />
<hkern u1="u" u2="U" k="37" />
<hkern u1="u" u2="T" k="137" />
<hkern u1="u" u2="S" k="16" />
<hkern u1="u" u2="R" k="20" />
<hkern u1="u" u2="Q" k="18" />
<hkern u1="u" u2="P" k="20" />
<hkern u1="u" u2="O" k="18" />
<hkern u1="u" u2="N" k="20" />
<hkern u1="u" u2="M" k="20" />
<hkern u1="u" u2="L" k="20" />
<hkern u1="u" u2="K" k="20" />
<hkern u1="u" u2="I" k="20" />
<hkern u1="u" u2="H" k="20" />
<hkern u1="u" u2="G" k="18" />
<hkern u1="u" u2="F" k="20" />
<hkern u1="u" u2="E" k="20" />
<hkern u1="u" u2="D" k="20" />
<hkern u1="u" u2="C" k="18" />
<hkern u1="u" u2="B" k="20" />
<hkern u1="u" u2="A" k="12" />
<hkern u1="u" u2="&#x29;" k="37" />
<hkern u1="u" u2="&#x26;" k="14" />
<hkern u1="v" u2="&#xfb04;" k="-57" />
<hkern u1="v" u2="&#xfb03;" k="-57" />
<hkern u1="v" u2="&#xfb02;" k="-57" />
<hkern u1="v" u2="&#xfb01;" k="-57" />
<hkern u1="v" u2="&#x2026;" k="63" />
<hkern u1="v" u2="&#x201e;" k="63" />
<hkern u1="v" u2="&#x201a;" k="63" />
<hkern u1="v" u2="&#x2014;" k="16" />
<hkern u1="v" u2="&#x2013;" k="16" />
<hkern u1="v" u2="&#xff;" k="-55" />
<hkern u1="v" u2="&#xfd;" k="-55" />
<hkern u1="v" u2="&#xf8;" k="14" />
<hkern u1="v" u2="&#xf6;" k="14" />
<hkern u1="v" u2="&#xf5;" k="14" />
<hkern u1="v" u2="&#xf4;" k="14" />
<hkern u1="v" u2="&#xf3;" k="14" />
<hkern u1="v" u2="&#xf2;" k="14" />
<hkern u1="v" u2="&#xf0;" k="14" />
<hkern u1="v" u2="&#xeb;" k="14" />
<hkern u1="v" u2="&#xea;" k="14" />
<hkern u1="v" u2="&#xe9;" k="14" />
<hkern u1="v" u2="&#xe8;" k="14" />
<hkern u1="v" u2="&#xe7;" k="14" />
<hkern u1="v" u2="&#xe6;" k="12" />
<hkern u1="v" u2="&#xe5;" k="12" />
<hkern u1="v" u2="&#xe4;" k="12" />
<hkern u1="v" u2="&#xe3;" k="12" />
<hkern u1="v" u2="&#xe2;" k="12" />
<hkern u1="v" u2="&#xe1;" k="12" />
<hkern u1="v" u2="&#xe0;" k="12" />
<hkern u1="v" u2="&#xdf;" k="-57" />
<hkern u1="v" u2="&#xde;" k="18" />
<hkern u1="v" u2="&#xdd;" k="72" />
<hkern u1="v" u2="&#xdc;" k="16" />
<hkern u1="v" u2="&#xdb;" k="16" />
<hkern u1="v" u2="&#xda;" k="16" />
<hkern u1="v" u2="&#xd9;" k="16" />
<hkern u1="v" u2="&#xd1;" k="18" />
<hkern u1="v" u2="&#xd0;" k="18" />
<hkern u1="v" u2="&#xcf;" k="18" />
<hkern u1="v" u2="&#xce;" k="18" />
<hkern u1="v" u2="&#xcd;" k="18" />
<hkern u1="v" u2="&#xcc;" k="18" />
<hkern u1="v" u2="&#xcb;" k="18" />
<hkern u1="v" u2="&#xca;" k="18" />
<hkern u1="v" u2="&#xc9;" k="18" />
<hkern u1="v" u2="&#xc8;" k="18" />
<hkern u1="v" u2="&#xc5;" k="74" />
<hkern u1="v" u2="&#xc4;" k="74" />
<hkern u1="v" u2="&#xc3;" k="74" />
<hkern u1="v" u2="&#xc2;" k="74" />
<hkern u1="v" u2="&#xc1;" k="74" />
<hkern u1="v" u2="&#xc0;" k="74" />
<hkern u1="v" u2="&#x7d;" k="35" />
<hkern u1="v" u2="y" k="-55" />
<hkern u1="v" u2="x" k="-37" />
<hkern u1="v" u2="v" k="-41" />
<hkern u1="v" u2="t" k="-43" />
<hkern u1="v" u2="q" k="14" />
<hkern u1="v" u2="o" k="14" />
<hkern u1="v" u2="g" k="14" />
<hkern u1="v" u2="f" k="-57" />
<hkern u1="v" u2="e" k="14" />
<hkern u1="v" u2="d" k="14" />
<hkern u1="v" u2="c" k="14" />
<hkern u1="v" u2="a" k="12" />
<hkern u1="v" u2="]" k="43" />
<hkern u1="v" u2="Y" k="72" />
<hkern u1="v" u2="X" k="78" />
<hkern u1="v" u2="V" k="25" />
<hkern u1="v" u2="U" k="16" />
<hkern u1="v" u2="T" k="100" />
<hkern u1="v" u2="R" k="18" />
<hkern u1="v" u2="P" k="18" />
<hkern u1="v" u2="N" k="18" />
<hkern u1="v" u2="M" k="18" />
<hkern u1="v" u2="L" k="18" />
<hkern u1="v" u2="K" k="18" />
<hkern u1="v" u2="I" k="18" />
<hkern u1="v" u2="H" k="18" />
<hkern u1="v" u2="F" k="18" />
<hkern u1="v" u2="E" k="18" />
<hkern u1="v" u2="D" k="18" />
<hkern u1="v" u2="B" k="18" />
<hkern u1="v" u2="A" k="74" />
<hkern u1="v" u2="&#x2f;" k="39" />
<hkern u1="v" u2="&#x2e;" k="63" />
<hkern u1="v" u2="&#x2d;" k="16" />
<hkern u1="v" u2="&#x2c;" k="63" />
<hkern u1="v" u2="&#x29;" k="41" />
<hkern u1="v" u2="&#x26;" k="29" />
<hkern u1="x" u2="&#xfb04;" k="-43" />
<hkern u1="x" u2="&#xfb03;" k="-43" />
<hkern u1="x" u2="&#xfb02;" k="-43" />
<hkern u1="x" u2="&#xfb01;" k="-43" />
<hkern u1="x" u2="&#x2122;" k="16" />
<hkern u1="x" u2="&#x2039;" k="27" />
<hkern u1="x" u2="&#x2014;" k="47" />
<hkern u1="x" u2="&#x2013;" k="47" />
<hkern u1="x" u2="&#xff;" k="-31" />
<hkern u1="x" u2="&#xfd;" k="-31" />
<hkern u1="x" u2="&#xf8;" k="31" />
<hkern u1="x" u2="&#xf6;" k="31" />
<hkern u1="x" u2="&#xf5;" k="31" />
<hkern u1="x" u2="&#xf4;" k="31" />
<hkern u1="x" u2="&#xf3;" k="31" />
<hkern u1="x" u2="&#xf2;" k="31" />
<hkern u1="x" u2="&#xf0;" k="31" />
<hkern u1="x" u2="&#xeb;" k="31" />
<hkern u1="x" u2="&#xea;" k="31" />
<hkern u1="x" u2="&#xe9;" k="31" />
<hkern u1="x" u2="&#xe8;" k="31" />
<hkern u1="x" u2="&#xe7;" k="31" />
<hkern u1="x" u2="&#xdf;" k="-43" />
<hkern u1="x" u2="&#xde;" k="10" />
<hkern u1="x" u2="&#xdd;" k="88" />
<hkern u1="x" u2="&#xdc;" k="20" />
<hkern u1="x" u2="&#xdb;" k="20" />
<hkern u1="x" u2="&#xda;" k="20" />
<hkern u1="x" u2="&#xd9;" k="20" />
<hkern u1="x" u2="&#xd8;" k="27" />
<hkern u1="x" u2="&#xd6;" k="27" />
<hkern u1="x" u2="&#xd5;" k="27" />
<hkern u1="x" u2="&#xd4;" k="27" />
<hkern u1="x" u2="&#xd3;" k="27" />
<hkern u1="x" u2="&#xd2;" k="27" />
<hkern u1="x" u2="&#xd1;" k="10" />
<hkern u1="x" u2="&#xd0;" k="10" />
<hkern u1="x" u2="&#xcf;" k="10" />
<hkern u1="x" u2="&#xce;" k="10" />
<hkern u1="x" u2="&#xcd;" k="10" />
<hkern u1="x" u2="&#xcc;" k="10" />
<hkern u1="x" u2="&#xcb;" k="10" />
<hkern u1="x" u2="&#xca;" k="10" />
<hkern u1="x" u2="&#xc9;" k="10" />
<hkern u1="x" u2="&#xc8;" k="10" />
<hkern u1="x" u2="&#xc7;" k="27" />
<hkern u1="x" u2="&#xab;" k="27" />
<hkern u1="x" u2="y" k="-31" />
<hkern u1="x" u2="x" k="-29" />
<hkern u1="x" u2="v" k="-33" />
<hkern u1="x" u2="q" k="31" />
<hkern u1="x" u2="o" k="31" />
<hkern u1="x" u2="g" k="31" />
<hkern u1="x" u2="f" k="-43" />
<hkern u1="x" u2="e" k="31" />
<hkern u1="x" u2="d" k="31" />
<hkern u1="x" u2="c" k="31" />
<hkern u1="x" u2="]" k="23" />
<hkern u1="x" u2="Y" k="88" />
<hkern u1="x" u2="V" k="31" />
<hkern u1="x" u2="U" k="20" />
<hkern u1="x" u2="T" k="139" />
<hkern u1="x" u2="R" k="10" />
<hkern u1="x" u2="Q" k="27" />
<hkern u1="x" u2="P" k="10" />
<hkern u1="x" u2="O" k="27" />
<hkern u1="x" u2="N" k="10" />
<hkern u1="x" u2="M" k="10" />
<hkern u1="x" u2="L" k="10" />
<hkern u1="x" u2="K" k="10" />
<hkern u1="x" u2="I" k="10" />
<hkern u1="x" u2="H" k="10" />
<hkern u1="x" u2="G" k="27" />
<hkern u1="x" u2="F" k="10" />
<hkern u1="x" u2="E" k="10" />
<hkern u1="x" u2="D" k="10" />
<hkern u1="x" u2="C" k="27" />
<hkern u1="x" u2="B" k="10" />
<hkern u1="x" u2="&#x2d;" k="47" />
<hkern u1="x" u2="&#x26;" k="39" />
<hkern u1="y" u2="&#x2026;" k="57" />
<hkern u1="y" u2="&#x201e;" k="57" />
<hkern u1="y" u2="&#x201a;" k="57" />
<hkern u1="y" u2="&#xff;" k="-59" />
<hkern u1="y" u2="&#xfd;" k="-59" />
<hkern u1="y" u2="&#xf8;" k="10" />
<hkern u1="y" u2="&#xf6;" k="10" />
<hkern u1="y" u2="&#xf5;" k="10" />
<hkern u1="y" u2="&#xf4;" k="10" />
<hkern u1="y" u2="&#xf3;" k="10" />
<hkern u1="y" u2="&#xf2;" k="10" />
<hkern u1="y" u2="&#xf0;" k="10" />
<hkern u1="y" u2="&#xeb;" k="10" />
<hkern u1="y" u2="&#xea;" k="10" />
<hkern u1="y" u2="&#xe9;" k="10" />
<hkern u1="y" u2="&#xe8;" k="10" />
<hkern u1="y" u2="&#xe7;" k="10" />
<hkern u1="y" u2="&#xe6;" k="8" />
<hkern u1="y" u2="&#xe5;" k="8" />
<hkern u1="y" u2="&#xe4;" k="8" />
<hkern u1="y" u2="&#xe3;" k="8" />
<hkern u1="y" u2="&#xe2;" k="8" />
<hkern u1="y" u2="&#xe1;" k="8" />
<hkern u1="y" u2="&#xe0;" k="8" />
<hkern u1="y" u2="&#xde;" k="14" />
<hkern u1="y" u2="&#xdd;" k="68" />
<hkern u1="y" u2="&#xdc;" k="12" />
<hkern u1="y" u2="&#xdb;" k="12" />
<hkern u1="y" u2="&#xda;" k="12" />
<hkern u1="y" u2="&#xd9;" k="12" />
<hkern u1="y" u2="&#xd1;" k="14" />
<hkern u1="y" u2="&#xd0;" k="14" />
<hkern u1="y" u2="&#xcf;" k="14" />
<hkern u1="y" u2="&#xce;" k="14" />
<hkern u1="y" u2="&#xcd;" k="14" />
<hkern u1="y" u2="&#xcc;" k="14" />
<hkern u1="y" u2="&#xcb;" k="14" />
<hkern u1="y" u2="&#xca;" k="14" />
<hkern u1="y" u2="&#xc9;" k="14" />
<hkern u1="y" u2="&#xc8;" k="14" />
<hkern u1="y" u2="&#xc5;" k="70" />
<hkern u1="y" u2="&#xc4;" k="70" />
<hkern u1="y" u2="&#xc3;" k="70" />
<hkern u1="y" u2="&#xc2;" k="70" />
<hkern u1="y" u2="&#xc1;" k="70" />
<hkern u1="y" u2="&#xc0;" k="70" />
<hkern u1="y" u2="&#x7d;" k="27" />
<hkern u1="y" u2="y" k="-59" />
<hkern u1="y" u2="x" k="-57" />
<hkern u1="y" u2="v" k="-63" />
<hkern u1="y" u2="t" k="-45" />
<hkern u1="y" u2="q" k="10" />
<hkern u1="y" u2="o" k="10" />
<hkern u1="y" u2="g" k="10" />
<hkern u1="y" u2="e" k="10" />
<hkern u1="y" u2="d" k="10" />
<hkern u1="y" u2="c" k="10" />
<hkern u1="y" u2="a" k="8" />
<hkern u1="y" u2="]" k="37" />
<hkern u1="y" u2="Y" k="68" />
<hkern u1="y" u2="X" k="74" />
<hkern u1="y" u2="V" k="20" />
<hkern u1="y" u2="U" k="12" />
<hkern u1="y" u2="T" k="98" />
<hkern u1="y" u2="R" k="14" />
<hkern u1="y" u2="P" k="14" />
<hkern u1="y" u2="N" k="14" />
<hkern u1="y" u2="M" k="14" />
<hkern u1="y" u2="L" k="14" />
<hkern u1="y" u2="K" k="14" />
<hkern u1="y" u2="I" k="14" />
<hkern u1="y" u2="H" k="14" />
<hkern u1="y" u2="F" k="14" />
<hkern u1="y" u2="E" k="14" />
<hkern u1="y" u2="D" k="14" />
<hkern u1="y" u2="B" k="14" />
<hkern u1="y" u2="A" k="70" />
<hkern u1="y" u2="&#x2f;" k="31" />
<hkern u1="y" u2="&#x2e;" k="57" />
<hkern u1="y" u2="&#x2c;" k="57" />
<hkern u1="y" u2="&#x29;" k="35" />
<hkern u1="y" u2="&#x26;" k="25" />
<hkern u1="&#x7b;" u2="&#xff;" k="23" />
<hkern u1="&#x7b;" u2="&#xfd;" k="23" />
<hkern u1="&#x7b;" u2="&#xfc;" k="41" />
<hkern u1="&#x7b;" u2="&#xfb;" k="41" />
<hkern u1="&#x7b;" u2="&#xfa;" k="41" />
<hkern u1="&#x7b;" u2="&#xf9;" k="41" />
<hkern u1="&#x7b;" u2="&#xf8;" k="45" />
<hkern u1="&#x7b;" u2="&#xf6;" k="45" />
<hkern u1="&#x7b;" u2="&#xf5;" k="45" />
<hkern u1="&#x7b;" u2="&#xf4;" k="45" />
<hkern u1="&#x7b;" u2="&#xf3;" k="45" />
<hkern u1="&#x7b;" u2="&#xf2;" k="45" />
<hkern u1="&#x7b;" u2="&#xf1;" k="37" />
<hkern u1="&#x7b;" u2="&#xf0;" k="45" />
<hkern u1="&#x7b;" u2="&#xeb;" k="45" />
<hkern u1="&#x7b;" u2="&#xea;" k="45" />
<hkern u1="&#x7b;" u2="&#xe9;" k="45" />
<hkern u1="&#x7b;" u2="&#xe8;" k="45" />
<hkern u1="&#x7b;" u2="&#xe7;" k="45" />
<hkern u1="&#x7b;" u2="&#xe6;" k="35" />
<hkern u1="&#x7b;" u2="&#xe5;" k="35" />
<hkern u1="&#x7b;" u2="&#xe4;" k="35" />
<hkern u1="&#x7b;" u2="&#xe3;" k="35" />
<hkern u1="&#x7b;" u2="&#xe2;" k="35" />
<hkern u1="&#x7b;" u2="&#xe1;" k="35" />
<hkern u1="&#x7b;" u2="&#xe0;" k="35" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-25" />
<hkern u1="&#x7b;" u2="&#xd8;" k="35" />
<hkern u1="&#x7b;" u2="&#xd6;" k="35" />
<hkern u1="&#x7b;" u2="&#xd5;" k="35" />
<hkern u1="&#x7b;" u2="&#xd4;" k="35" />
<hkern u1="&#x7b;" u2="&#xd3;" k="35" />
<hkern u1="&#x7b;" u2="&#xd2;" k="35" />
<hkern u1="&#x7b;" u2="&#xce;" k="-25" />
<hkern u1="&#x7b;" u2="&#xc7;" k="35" />
<hkern u1="&#x7b;" u2="&#xc5;" k="27" />
<hkern u1="&#x7b;" u2="&#xc4;" k="27" />
<hkern u1="&#x7b;" u2="&#xc3;" k="27" />
<hkern u1="&#x7b;" u2="&#xc2;" k="27" />
<hkern u1="&#x7b;" u2="&#xc1;" k="27" />
<hkern u1="&#x7b;" u2="&#xc0;" k="27" />
<hkern u1="&#x7b;" u2="y" k="23" />
<hkern u1="&#x7b;" u2="v" k="35" />
<hkern u1="&#x7b;" u2="u" k="41" />
<hkern u1="&#x7b;" u2="t" k="35" />
<hkern u1="&#x7b;" u2="s" k="37" />
<hkern u1="&#x7b;" u2="r" k="37" />
<hkern u1="&#x7b;" u2="q" k="45" />
<hkern u1="&#x7b;" u2="p" k="37" />
<hkern u1="&#x7b;" u2="o" k="45" />
<hkern u1="&#x7b;" u2="n" k="37" />
<hkern u1="&#x7b;" u2="m" k="37" />
<hkern u1="&#x7b;" u2="g" k="45" />
<hkern u1="&#x7b;" u2="e" k="45" />
<hkern u1="&#x7b;" u2="d" k="45" />
<hkern u1="&#x7b;" u2="c" k="45" />
<hkern u1="&#x7b;" u2="a" k="35" />
<hkern u1="&#x7b;" u2="Y" k="-25" />
<hkern u1="&#x7b;" u2="Q" k="35" />
<hkern u1="&#x7b;" u2="O" k="35" />
<hkern u1="&#x7b;" u2="G" k="35" />
<hkern u1="&#x7b;" u2="C" k="35" />
<hkern u1="&#x7b;" u2="A" k="27" />
<hkern u1="&#xa1;" u2="&#xdd;" k="66" />
<hkern u1="&#xa1;" u2="Y" k="66" />
<hkern u1="&#xa1;" u2="V" k="33" />
<hkern u1="&#xa1;" u2="T" k="98" />
<hkern u1="&#xab;" u2="&#xdd;" k="66" />
<hkern u1="&#xab;" u2="Y" k="66" />
<hkern u1="&#xab;" u2="V" k="25" />
<hkern u1="&#xab;" u2="T" k="104" />
<hkern u1="&#xae;" u2="&#xdd;" k="51" />
<hkern u1="&#xae;" u2="&#xc5;" k="23" />
<hkern u1="&#xae;" u2="&#xc4;" k="23" />
<hkern u1="&#xae;" u2="&#xc3;" k="23" />
<hkern u1="&#xae;" u2="&#xc2;" k="23" />
<hkern u1="&#xae;" u2="&#xc1;" k="23" />
<hkern u1="&#xae;" u2="&#xc0;" k="23" />
<hkern u1="&#xae;" u2="Y" k="51" />
<hkern u1="&#xae;" u2="V" k="20" />
<hkern u1="&#xae;" u2="T" k="35" />
<hkern u1="&#xae;" u2="A" k="23" />
<hkern u1="&#xbb;" u2="&#x201d;" k="45" />
<hkern u1="&#xbb;" u2="&#x2019;" k="45" />
<hkern u1="&#xbb;" u2="&#xdd;" k="121" />
<hkern u1="&#xbb;" u2="x" k="27" />
<hkern u1="&#xbb;" u2="Y" k="121" />
<hkern u1="&#xbb;" u2="X" k="31" />
<hkern u1="&#xbb;" u2="V" k="63" />
<hkern u1="&#xbb;" u2="T" k="121" />
<hkern u1="&#xbb;" u2="&#x27;" k="45" />
<hkern u1="&#xbb;" u2="&#x22;" k="45" />
<hkern u1="&#xbf;" u2="&#xfb04;" k="35" />
<hkern u1="&#xbf;" u2="&#xfb03;" k="35" />
<hkern u1="&#xbf;" u2="&#xfb02;" k="35" />
<hkern u1="&#xbf;" u2="&#xfb01;" k="35" />
<hkern u1="&#xbf;" u2="&#xff;" k="27" />
<hkern u1="&#xbf;" u2="&#xfe;" k="37" />
<hkern u1="&#xbf;" u2="&#xfd;" k="27" />
<hkern u1="&#xbf;" u2="&#xfc;" k="47" />
<hkern u1="&#xbf;" u2="&#xfb;" k="47" />
<hkern u1="&#xbf;" u2="&#xfa;" k="47" />
<hkern u1="&#xbf;" u2="&#xf9;" k="47" />
<hkern u1="&#xbf;" u2="&#xf8;" k="51" />
<hkern u1="&#xbf;" u2="&#xf6;" k="51" />
<hkern u1="&#xbf;" u2="&#xf5;" k="51" />
<hkern u1="&#xbf;" u2="&#xf4;" k="51" />
<hkern u1="&#xbf;" u2="&#xf3;" k="51" />
<hkern u1="&#xbf;" u2="&#xf2;" k="51" />
<hkern u1="&#xbf;" u2="&#xf1;" k="37" />
<hkern u1="&#xbf;" u2="&#xf0;" k="51" />
<hkern u1="&#xbf;" u2="&#xef;" k="37" />
<hkern u1="&#xbf;" u2="&#xee;" k="37" />
<hkern u1="&#xbf;" u2="&#xed;" k="37" />
<hkern u1="&#xbf;" u2="&#xec;" k="37" />
<hkern u1="&#xbf;" u2="&#xeb;" k="51" />
<hkern u1="&#xbf;" u2="&#xea;" k="51" />
<hkern u1="&#xbf;" u2="&#xe9;" k="51" />
<hkern u1="&#xbf;" u2="&#xe8;" k="51" />
<hkern u1="&#xbf;" u2="&#xe7;" k="51" />
<hkern u1="&#xbf;" u2="&#xe6;" k="43" />
<hkern u1="&#xbf;" u2="&#xe5;" k="43" />
<hkern u1="&#xbf;" u2="&#xe4;" k="43" />
<hkern u1="&#xbf;" u2="&#xe3;" k="43" />
<hkern u1="&#xbf;" u2="&#xe2;" k="43" />
<hkern u1="&#xbf;" u2="&#xe1;" k="43" />
<hkern u1="&#xbf;" u2="&#xe0;" k="43" />
<hkern u1="&#xbf;" u2="&#xdf;" k="35" />
<hkern u1="&#xbf;" u2="&#xde;" k="33" />
<hkern u1="&#xbf;" u2="&#xdd;" k="113" />
<hkern u1="&#xbf;" u2="&#xdc;" k="51" />
<hkern u1="&#xbf;" u2="&#xdb;" k="51" />
<hkern u1="&#xbf;" u2="&#xda;" k="51" />
<hkern u1="&#xbf;" u2="&#xd9;" k="51" />
<hkern u1="&#xbf;" u2="&#xd8;" k="45" />
<hkern u1="&#xbf;" u2="&#xd6;" k="45" />
<hkern u1="&#xbf;" u2="&#xd5;" k="45" />
<hkern u1="&#xbf;" u2="&#xd4;" k="45" />
<hkern u1="&#xbf;" u2="&#xd3;" k="45" />
<hkern u1="&#xbf;" u2="&#xd2;" k="45" />
<hkern u1="&#xbf;" u2="&#xd1;" k="33" />
<hkern u1="&#xbf;" u2="&#xd0;" k="33" />
<hkern u1="&#xbf;" u2="&#xcf;" k="33" />
<hkern u1="&#xbf;" u2="&#xce;" k="33" />
<hkern u1="&#xbf;" u2="&#xcd;" k="33" />
<hkern u1="&#xbf;" u2="&#xcc;" k="33" />
<hkern u1="&#xbf;" u2="&#xcb;" k="33" />
<hkern u1="&#xbf;" u2="&#xca;" k="33" />
<hkern u1="&#xbf;" u2="&#xc9;" k="33" />
<hkern u1="&#xbf;" u2="&#xc8;" k="33" />
<hkern u1="&#xbf;" u2="&#xc7;" k="45" />
<hkern u1="&#xbf;" u2="&#xc5;" k="31" />
<hkern u1="&#xbf;" u2="&#xc4;" k="31" />
<hkern u1="&#xbf;" u2="&#xc3;" k="31" />
<hkern u1="&#xbf;" u2="&#xc2;" k="31" />
<hkern u1="&#xbf;" u2="&#xc1;" k="31" />
<hkern u1="&#xbf;" u2="&#xc0;" k="31" />
<hkern u1="&#xbf;" u2="y" k="27" />
<hkern u1="&#xbf;" u2="x" k="23" />
<hkern u1="&#xbf;" u2="v" k="43" />
<hkern u1="&#xbf;" u2="u" k="47" />
<hkern u1="&#xbf;" u2="t" k="41" />
<hkern u1="&#xbf;" u2="s" k="39" />
<hkern u1="&#xbf;" u2="r" k="37" />
<hkern u1="&#xbf;" u2="q" k="51" />
<hkern u1="&#xbf;" u2="p" k="37" />
<hkern u1="&#xbf;" u2="o" k="51" />
<hkern u1="&#xbf;" u2="n" k="37" />
<hkern u1="&#xbf;" u2="m" k="37" />
<hkern u1="&#xbf;" u2="k" k="37" />
<hkern u1="&#xbf;" u2="j" k="-37" />
<hkern u1="&#xbf;" u2="i" k="37" />
<hkern u1="&#xbf;" u2="h" k="37" />
<hkern u1="&#xbf;" u2="g" k="51" />
<hkern u1="&#xbf;" u2="f" k="35" />
<hkern u1="&#xbf;" u2="e" k="51" />
<hkern u1="&#xbf;" u2="d" k="51" />
<hkern u1="&#xbf;" u2="c" k="51" />
<hkern u1="&#xbf;" u2="b" k="37" />
<hkern u1="&#xbf;" u2="a" k="43" />
<hkern u1="&#xbf;" u2="Y" k="113" />
<hkern u1="&#xbf;" u2="V" k="80" />
<hkern u1="&#xbf;" u2="U" k="51" />
<hkern u1="&#xbf;" u2="T" k="141" />
<hkern u1="&#xbf;" u2="S" k="33" />
<hkern u1="&#xbf;" u2="R" k="33" />
<hkern u1="&#xbf;" u2="Q" k="45" />
<hkern u1="&#xbf;" u2="P" k="33" />
<hkern u1="&#xbf;" u2="O" k="45" />
<hkern u1="&#xbf;" u2="N" k="33" />
<hkern u1="&#xbf;" u2="M" k="33" />
<hkern u1="&#xbf;" u2="L" k="33" />
<hkern u1="&#xbf;" u2="K" k="33" />
<hkern u1="&#xbf;" u2="I" k="33" />
<hkern u1="&#xbf;" u2="H" k="33" />
<hkern u1="&#xbf;" u2="G" k="45" />
<hkern u1="&#xbf;" u2="F" k="33" />
<hkern u1="&#xbf;" u2="E" k="33" />
<hkern u1="&#xbf;" u2="D" k="33" />
<hkern u1="&#xbf;" u2="C" k="45" />
<hkern u1="&#xbf;" u2="B" k="33" />
<hkern u1="&#xbf;" u2="A" k="31" />
<hkern u1="&#xc0;" u2="&#x2122;" k="90" />
<hkern u1="&#xc0;" u2="&#xae;" k="20" />
<hkern u1="&#xc0;" u2="&#x7d;" k="25" />
<hkern u1="&#xc0;" u2="v" k="74" />
<hkern u1="&#xc0;" u2="]" k="31" />
<hkern u1="&#xc0;" u2="\" k="66" />
<hkern u1="&#xc0;" u2="X" k="-49" />
<hkern u1="&#xc0;" u2="V" k="90" />
<hkern u1="&#xc0;" u2="&#x3f;" k="37" />
<hkern u1="&#xc0;" u2="&#x2a;" k="84" />
<hkern u1="&#xc1;" u2="&#x2122;" k="90" />
<hkern u1="&#xc1;" u2="&#xae;" k="20" />
<hkern u1="&#xc1;" u2="&#x7d;" k="25" />
<hkern u1="&#xc1;" u2="v" k="74" />
<hkern u1="&#xc1;" u2="]" k="31" />
<hkern u1="&#xc1;" u2="\" k="66" />
<hkern u1="&#xc1;" u2="X" k="-49" />
<hkern u1="&#xc1;" u2="V" k="90" />
<hkern u1="&#xc1;" u2="&#x3f;" k="37" />
<hkern u1="&#xc1;" u2="&#x2a;" k="84" />
<hkern u1="&#xc2;" u2="&#x2122;" k="90" />
<hkern u1="&#xc2;" u2="&#xae;" k="20" />
<hkern u1="&#xc2;" u2="&#x7d;" k="25" />
<hkern u1="&#xc2;" u2="v" k="74" />
<hkern u1="&#xc2;" u2="]" k="31" />
<hkern u1="&#xc2;" u2="\" k="66" />
<hkern u1="&#xc2;" u2="X" k="-49" />
<hkern u1="&#xc2;" u2="V" k="90" />
<hkern u1="&#xc2;" u2="&#x3f;" k="37" />
<hkern u1="&#xc2;" u2="&#x2a;" k="84" />
<hkern u1="&#xc3;" u2="&#x2122;" k="90" />
<hkern u1="&#xc3;" u2="&#xae;" k="20" />
<hkern u1="&#xc3;" u2="&#x7d;" k="25" />
<hkern u1="&#xc3;" u2="v" k="74" />
<hkern u1="&#xc3;" u2="]" k="31" />
<hkern u1="&#xc3;" u2="\" k="66" />
<hkern u1="&#xc3;" u2="X" k="-49" />
<hkern u1="&#xc3;" u2="V" k="90" />
<hkern u1="&#xc3;" u2="&#x3f;" k="37" />
<hkern u1="&#xc3;" u2="&#x2a;" k="84" />
<hkern u1="&#xc4;" u2="&#x2122;" k="90" />
<hkern u1="&#xc4;" u2="&#xae;" k="20" />
<hkern u1="&#xc4;" u2="&#x7d;" k="25" />
<hkern u1="&#xc4;" u2="v" k="74" />
<hkern u1="&#xc4;" u2="]" k="31" />
<hkern u1="&#xc4;" u2="\" k="66" />
<hkern u1="&#xc4;" u2="X" k="-49" />
<hkern u1="&#xc4;" u2="V" k="90" />
<hkern u1="&#xc4;" u2="&#x3f;" k="37" />
<hkern u1="&#xc4;" u2="&#x2a;" k="84" />
<hkern u1="&#xc5;" u2="&#x2122;" k="90" />
<hkern u1="&#xc5;" u2="&#xae;" k="20" />
<hkern u1="&#xc5;" u2="&#x7d;" k="25" />
<hkern u1="&#xc5;" u2="v" k="74" />
<hkern u1="&#xc5;" u2="]" k="31" />
<hkern u1="&#xc5;" u2="\" k="66" />
<hkern u1="&#xc5;" u2="X" k="-49" />
<hkern u1="&#xc5;" u2="V" k="90" />
<hkern u1="&#xc5;" u2="&#x3f;" k="37" />
<hkern u1="&#xc5;" u2="&#x2a;" k="84" />
<hkern u1="&#xc6;" u2="&#xfb04;" k="23" />
<hkern u1="&#xc6;" u2="&#xfb03;" k="23" />
<hkern u1="&#xc6;" u2="&#xfb02;" k="23" />
<hkern u1="&#xc6;" u2="&#xfb01;" k="23" />
<hkern u1="&#xc6;" u2="&#xff;" k="29" />
<hkern u1="&#xc6;" u2="&#xfd;" k="29" />
<hkern u1="&#xc6;" u2="&#xfc;" k="27" />
<hkern u1="&#xc6;" u2="&#xfb;" k="27" />
<hkern u1="&#xc6;" u2="&#xfa;" k="27" />
<hkern u1="&#xc6;" u2="&#xf9;" k="27" />
<hkern u1="&#xc6;" u2="&#xf8;" k="29" />
<hkern u1="&#xc6;" u2="&#xf6;" k="29" />
<hkern u1="&#xc6;" u2="&#xf5;" k="29" />
<hkern u1="&#xc6;" u2="&#xf4;" k="29" />
<hkern u1="&#xc6;" u2="&#xf3;" k="29" />
<hkern u1="&#xc6;" u2="&#xf2;" k="29" />
<hkern u1="&#xc6;" u2="&#xf1;" k="14" />
<hkern u1="&#xc6;" u2="&#xf0;" k="29" />
<hkern u1="&#xc6;" u2="&#xeb;" k="29" />
<hkern u1="&#xc6;" u2="&#xea;" k="29" />
<hkern u1="&#xc6;" u2="&#xe9;" k="29" />
<hkern u1="&#xc6;" u2="&#xe8;" k="29" />
<hkern u1="&#xc6;" u2="&#xe7;" k="29" />
<hkern u1="&#xc6;" u2="&#xe6;" k="14" />
<hkern u1="&#xc6;" u2="&#xe5;" k="14" />
<hkern u1="&#xc6;" u2="&#xe4;" k="14" />
<hkern u1="&#xc6;" u2="&#xe3;" k="14" />
<hkern u1="&#xc6;" u2="&#xe2;" k="14" />
<hkern u1="&#xc6;" u2="&#xe1;" k="14" />
<hkern u1="&#xc6;" u2="&#xe0;" k="14" />
<hkern u1="&#xc6;" u2="&#xdf;" k="23" />
<hkern u1="&#xc6;" u2="&#xd8;" k="14" />
<hkern u1="&#xc6;" u2="&#xd6;" k="14" />
<hkern u1="&#xc6;" u2="&#xd5;" k="14" />
<hkern u1="&#xc6;" u2="&#xd4;" k="14" />
<hkern u1="&#xc6;" u2="&#xd3;" k="14" />
<hkern u1="&#xc6;" u2="&#xd2;" k="14" />
<hkern u1="&#xc6;" u2="&#xc7;" k="14" />
<hkern u1="&#xc6;" u2="y" k="29" />
<hkern u1="&#xc6;" u2="v" k="33" />
<hkern u1="&#xc6;" u2="u" k="27" />
<hkern u1="&#xc6;" u2="t" k="35" />
<hkern u1="&#xc6;" u2="s" k="12" />
<hkern u1="&#xc6;" u2="r" k="14" />
<hkern u1="&#xc6;" u2="q" k="29" />
<hkern u1="&#xc6;" u2="p" k="14" />
<hkern u1="&#xc6;" u2="o" k="29" />
<hkern u1="&#xc6;" u2="n" k="14" />
<hkern u1="&#xc6;" u2="m" k="14" />
<hkern u1="&#xc6;" u2="g" k="29" />
<hkern u1="&#xc6;" u2="f" k="23" />
<hkern u1="&#xc6;" u2="e" k="29" />
<hkern u1="&#xc6;" u2="d" k="29" />
<hkern u1="&#xc6;" u2="c" k="29" />
<hkern u1="&#xc6;" u2="a" k="14" />
<hkern u1="&#xc6;" u2="Q" k="14" />
<hkern u1="&#xc6;" u2="O" k="14" />
<hkern u1="&#xc6;" u2="G" k="14" />
<hkern u1="&#xc6;" u2="C" k="14" />
<hkern u1="&#xc6;" u2="&#x26;" k="14" />
<hkern u1="&#xc7;" u2="&#xfb04;" k="14" />
<hkern u1="&#xc7;" u2="&#xfb03;" k="14" />
<hkern u1="&#xc7;" u2="&#xfb02;" k="14" />
<hkern u1="&#xc7;" u2="&#xfb01;" k="14" />
<hkern u1="&#xc7;" u2="&#x2014;" k="45" />
<hkern u1="&#xc7;" u2="&#x2013;" k="45" />
<hkern u1="&#xc7;" u2="&#xff;" k="94" />
<hkern u1="&#xc7;" u2="&#xfd;" k="94" />
<hkern u1="&#xc7;" u2="&#xfc;" k="23" />
<hkern u1="&#xc7;" u2="&#xfb;" k="23" />
<hkern u1="&#xc7;" u2="&#xfa;" k="23" />
<hkern u1="&#xc7;" u2="&#xf9;" k="23" />
<hkern u1="&#xc7;" u2="&#xf8;" k="33" />
<hkern u1="&#xc7;" u2="&#xf6;" k="33" />
<hkern u1="&#xc7;" u2="&#xf5;" k="33" />
<hkern u1="&#xc7;" u2="&#xf4;" k="33" />
<hkern u1="&#xc7;" u2="&#xf3;" k="33" />
<hkern u1="&#xc7;" u2="&#xf2;" k="33" />
<hkern u1="&#xc7;" u2="&#xf1;" k="14" />
<hkern u1="&#xc7;" u2="&#xf0;" k="33" />
<hkern u1="&#xc7;" u2="&#xeb;" k="33" />
<hkern u1="&#xc7;" u2="&#xea;" k="33" />
<hkern u1="&#xc7;" u2="&#xe9;" k="33" />
<hkern u1="&#xc7;" u2="&#xe8;" k="33" />
<hkern u1="&#xc7;" u2="&#xe7;" k="33" />
<hkern u1="&#xc7;" u2="&#xdf;" k="14" />
<hkern u1="&#xc7;" u2="&#xd8;" k="18" />
<hkern u1="&#xc7;" u2="&#xd6;" k="18" />
<hkern u1="&#xc7;" u2="&#xd5;" k="18" />
<hkern u1="&#xc7;" u2="&#xd4;" k="18" />
<hkern u1="&#xc7;" u2="&#xd3;" k="18" />
<hkern u1="&#xc7;" u2="&#xd2;" k="18" />
<hkern u1="&#xc7;" u2="&#xc7;" k="18" />
<hkern u1="&#xc7;" u2="y" k="94" />
<hkern u1="&#xc7;" u2="v" k="102" />
<hkern u1="&#xc7;" u2="u" k="23" />
<hkern u1="&#xc7;" u2="t" k="41" />
<hkern u1="&#xc7;" u2="s" k="20" />
<hkern u1="&#xc7;" u2="r" k="14" />
<hkern u1="&#xc7;" u2="q" k="33" />
<hkern u1="&#xc7;" u2="p" k="14" />
<hkern u1="&#xc7;" u2="o" k="33" />
<hkern u1="&#xc7;" u2="n" k="14" />
<hkern u1="&#xc7;" u2="m" k="14" />
<hkern u1="&#xc7;" u2="g" k="33" />
<hkern u1="&#xc7;" u2="f" k="14" />
<hkern u1="&#xc7;" u2="e" k="33" />
<hkern u1="&#xc7;" u2="d" k="33" />
<hkern u1="&#xc7;" u2="c" k="33" />
<hkern u1="&#xc7;" u2="Q" k="18" />
<hkern u1="&#xc7;" u2="O" k="18" />
<hkern u1="&#xc7;" u2="G" k="18" />
<hkern u1="&#xc7;" u2="C" k="18" />
<hkern u1="&#xc7;" u2="&#x2d;" k="45" />
<hkern u1="&#xc7;" u2="&#x26;" k="14" />
<hkern u1="&#xc8;" u2="&#xfb04;" k="23" />
<hkern u1="&#xc8;" u2="&#xfb03;" k="23" />
<hkern u1="&#xc8;" u2="&#xfb02;" k="23" />
<hkern u1="&#xc8;" u2="&#xfb01;" k="23" />
<hkern u1="&#xc8;" u2="&#xff;" k="29" />
<hkern u1="&#xc8;" u2="&#xfd;" k="29" />
<hkern u1="&#xc8;" u2="&#xfc;" k="27" />
<hkern u1="&#xc8;" u2="&#xfb;" k="27" />
<hkern u1="&#xc8;" u2="&#xfa;" k="27" />
<hkern u1="&#xc8;" u2="&#xf9;" k="27" />
<hkern u1="&#xc8;" u2="&#xf8;" k="29" />
<hkern u1="&#xc8;" u2="&#xf6;" k="29" />
<hkern u1="&#xc8;" u2="&#xf5;" k="29" />
<hkern u1="&#xc8;" u2="&#xf4;" k="29" />
<hkern u1="&#xc8;" u2="&#xf3;" k="29" />
<hkern u1="&#xc8;" u2="&#xf2;" k="29" />
<hkern u1="&#xc8;" u2="&#xf1;" k="14" />
<hkern u1="&#xc8;" u2="&#xf0;" k="29" />
<hkern u1="&#xc8;" u2="&#xeb;" k="29" />
<hkern u1="&#xc8;" u2="&#xea;" k="29" />
<hkern u1="&#xc8;" u2="&#xe9;" k="29" />
<hkern u1="&#xc8;" u2="&#xe8;" k="29" />
<hkern u1="&#xc8;" u2="&#xe7;" k="29" />
<hkern u1="&#xc8;" u2="&#xe6;" k="14" />
<hkern u1="&#xc8;" u2="&#xe5;" k="14" />
<hkern u1="&#xc8;" u2="&#xe4;" k="14" />
<hkern u1="&#xc8;" u2="&#xe3;" k="14" />
<hkern u1="&#xc8;" u2="&#xe2;" k="14" />
<hkern u1="&#xc8;" u2="&#xe1;" k="14" />
<hkern u1="&#xc8;" u2="&#xe0;" k="14" />
<hkern u1="&#xc8;" u2="&#xdf;" k="23" />
<hkern u1="&#xc8;" u2="&#xd8;" k="14" />
<hkern u1="&#xc8;" u2="&#xd6;" k="14" />
<hkern u1="&#xc8;" u2="&#xd5;" k="14" />
<hkern u1="&#xc8;" u2="&#xd4;" k="14" />
<hkern u1="&#xc8;" u2="&#xd3;" k="14" />
<hkern u1="&#xc8;" u2="&#xd2;" k="14" />
<hkern u1="&#xc8;" u2="&#xc7;" k="14" />
<hkern u1="&#xc8;" u2="y" k="29" />
<hkern u1="&#xc8;" u2="v" k="33" />
<hkern u1="&#xc8;" u2="u" k="27" />
<hkern u1="&#xc8;" u2="t" k="35" />
<hkern u1="&#xc8;" u2="s" k="12" />
<hkern u1="&#xc8;" u2="r" k="14" />
<hkern u1="&#xc8;" u2="q" k="29" />
<hkern u1="&#xc8;" u2="p" k="14" />
<hkern u1="&#xc8;" u2="o" k="29" />
<hkern u1="&#xc8;" u2="n" k="14" />
<hkern u1="&#xc8;" u2="m" k="14" />
<hkern u1="&#xc8;" u2="g" k="29" />
<hkern u1="&#xc8;" u2="f" k="23" />
<hkern u1="&#xc8;" u2="e" k="29" />
<hkern u1="&#xc8;" u2="d" k="29" />
<hkern u1="&#xc8;" u2="c" k="29" />
<hkern u1="&#xc8;" u2="a" k="14" />
<hkern u1="&#xc8;" u2="Q" k="14" />
<hkern u1="&#xc8;" u2="O" k="14" />
<hkern u1="&#xc8;" u2="G" k="14" />
<hkern u1="&#xc8;" u2="C" k="14" />
<hkern u1="&#xc8;" u2="&#x26;" k="14" />
<hkern u1="&#xc9;" u2="&#xfb04;" k="23" />
<hkern u1="&#xc9;" u2="&#xfb03;" k="23" />
<hkern u1="&#xc9;" u2="&#xfb02;" k="23" />
<hkern u1="&#xc9;" u2="&#xfb01;" k="23" />
<hkern u1="&#xc9;" u2="&#xff;" k="29" />
<hkern u1="&#xc9;" u2="&#xfd;" k="29" />
<hkern u1="&#xc9;" u2="&#xfc;" k="27" />
<hkern u1="&#xc9;" u2="&#xfb;" k="27" />
<hkern u1="&#xc9;" u2="&#xfa;" k="27" />
<hkern u1="&#xc9;" u2="&#xf9;" k="27" />
<hkern u1="&#xc9;" u2="&#xf8;" k="29" />
<hkern u1="&#xc9;" u2="&#xf6;" k="29" />
<hkern u1="&#xc9;" u2="&#xf5;" k="29" />
<hkern u1="&#xc9;" u2="&#xf4;" k="29" />
<hkern u1="&#xc9;" u2="&#xf3;" k="29" />
<hkern u1="&#xc9;" u2="&#xf2;" k="29" />
<hkern u1="&#xc9;" u2="&#xf1;" k="14" />
<hkern u1="&#xc9;" u2="&#xf0;" k="29" />
<hkern u1="&#xc9;" u2="&#xeb;" k="29" />
<hkern u1="&#xc9;" u2="&#xea;" k="29" />
<hkern u1="&#xc9;" u2="&#xe9;" k="29" />
<hkern u1="&#xc9;" u2="&#xe8;" k="29" />
<hkern u1="&#xc9;" u2="&#xe7;" k="29" />
<hkern u1="&#xc9;" u2="&#xe6;" k="14" />
<hkern u1="&#xc9;" u2="&#xe5;" k="14" />
<hkern u1="&#xc9;" u2="&#xe4;" k="14" />
<hkern u1="&#xc9;" u2="&#xe3;" k="14" />
<hkern u1="&#xc9;" u2="&#xe2;" k="14" />
<hkern u1="&#xc9;" u2="&#xe1;" k="14" />
<hkern u1="&#xc9;" u2="&#xe0;" k="14" />
<hkern u1="&#xc9;" u2="&#xdf;" k="23" />
<hkern u1="&#xc9;" u2="&#xd8;" k="14" />
<hkern u1="&#xc9;" u2="&#xd6;" k="14" />
<hkern u1="&#xc9;" u2="&#xd5;" k="14" />
<hkern u1="&#xc9;" u2="&#xd4;" k="14" />
<hkern u1="&#xc9;" u2="&#xd3;" k="14" />
<hkern u1="&#xc9;" u2="&#xd2;" k="14" />
<hkern u1="&#xc9;" u2="&#xc7;" k="14" />
<hkern u1="&#xc9;" u2="y" k="29" />
<hkern u1="&#xc9;" u2="v" k="33" />
<hkern u1="&#xc9;" u2="u" k="27" />
<hkern u1="&#xc9;" u2="t" k="35" />
<hkern u1="&#xc9;" u2="s" k="12" />
<hkern u1="&#xc9;" u2="r" k="14" />
<hkern u1="&#xc9;" u2="q" k="29" />
<hkern u1="&#xc9;" u2="p" k="14" />
<hkern u1="&#xc9;" u2="o" k="29" />
<hkern u1="&#xc9;" u2="n" k="14" />
<hkern u1="&#xc9;" u2="m" k="14" />
<hkern u1="&#xc9;" u2="g" k="29" />
<hkern u1="&#xc9;" u2="f" k="23" />
<hkern u1="&#xc9;" u2="e" k="29" />
<hkern u1="&#xc9;" u2="d" k="29" />
<hkern u1="&#xc9;" u2="c" k="29" />
<hkern u1="&#xc9;" u2="a" k="14" />
<hkern u1="&#xc9;" u2="Q" k="14" />
<hkern u1="&#xc9;" u2="O" k="14" />
<hkern u1="&#xc9;" u2="G" k="14" />
<hkern u1="&#xc9;" u2="C" k="14" />
<hkern u1="&#xc9;" u2="&#x26;" k="14" />
<hkern u1="&#xca;" u2="&#xfb04;" k="23" />
<hkern u1="&#xca;" u2="&#xfb03;" k="23" />
<hkern u1="&#xca;" u2="&#xfb02;" k="23" />
<hkern u1="&#xca;" u2="&#xfb01;" k="23" />
<hkern u1="&#xca;" u2="&#xff;" k="29" />
<hkern u1="&#xca;" u2="&#xfd;" k="29" />
<hkern u1="&#xca;" u2="&#xfc;" k="27" />
<hkern u1="&#xca;" u2="&#xfb;" k="27" />
<hkern u1="&#xca;" u2="&#xfa;" k="27" />
<hkern u1="&#xca;" u2="&#xf9;" k="27" />
<hkern u1="&#xca;" u2="&#xf8;" k="29" />
<hkern u1="&#xca;" u2="&#xf6;" k="29" />
<hkern u1="&#xca;" u2="&#xf5;" k="29" />
<hkern u1="&#xca;" u2="&#xf4;" k="29" />
<hkern u1="&#xca;" u2="&#xf3;" k="29" />
<hkern u1="&#xca;" u2="&#xf2;" k="29" />
<hkern u1="&#xca;" u2="&#xf1;" k="14" />
<hkern u1="&#xca;" u2="&#xf0;" k="29" />
<hkern u1="&#xca;" u2="&#xeb;" k="29" />
<hkern u1="&#xca;" u2="&#xea;" k="29" />
<hkern u1="&#xca;" u2="&#xe9;" k="29" />
<hkern u1="&#xca;" u2="&#xe8;" k="29" />
<hkern u1="&#xca;" u2="&#xe7;" k="29" />
<hkern u1="&#xca;" u2="&#xe6;" k="14" />
<hkern u1="&#xca;" u2="&#xe5;" k="14" />
<hkern u1="&#xca;" u2="&#xe4;" k="14" />
<hkern u1="&#xca;" u2="&#xe3;" k="14" />
<hkern u1="&#xca;" u2="&#xe2;" k="14" />
<hkern u1="&#xca;" u2="&#xe1;" k="14" />
<hkern u1="&#xca;" u2="&#xe0;" k="14" />
<hkern u1="&#xca;" u2="&#xdf;" k="23" />
<hkern u1="&#xca;" u2="&#xd8;" k="14" />
<hkern u1="&#xca;" u2="&#xd6;" k="14" />
<hkern u1="&#xca;" u2="&#xd5;" k="14" />
<hkern u1="&#xca;" u2="&#xd4;" k="14" />
<hkern u1="&#xca;" u2="&#xd3;" k="14" />
<hkern u1="&#xca;" u2="&#xd2;" k="14" />
<hkern u1="&#xca;" u2="&#xc7;" k="14" />
<hkern u1="&#xca;" u2="y" k="29" />
<hkern u1="&#xca;" u2="v" k="33" />
<hkern u1="&#xca;" u2="u" k="27" />
<hkern u1="&#xca;" u2="t" k="35" />
<hkern u1="&#xca;" u2="s" k="12" />
<hkern u1="&#xca;" u2="r" k="14" />
<hkern u1="&#xca;" u2="q" k="29" />
<hkern u1="&#xca;" u2="p" k="14" />
<hkern u1="&#xca;" u2="o" k="29" />
<hkern u1="&#xca;" u2="n" k="14" />
<hkern u1="&#xca;" u2="m" k="14" />
<hkern u1="&#xca;" u2="g" k="29" />
<hkern u1="&#xca;" u2="f" k="23" />
<hkern u1="&#xca;" u2="e" k="29" />
<hkern u1="&#xca;" u2="d" k="29" />
<hkern u1="&#xca;" u2="c" k="29" />
<hkern u1="&#xca;" u2="a" k="14" />
<hkern u1="&#xca;" u2="Q" k="14" />
<hkern u1="&#xca;" u2="O" k="14" />
<hkern u1="&#xca;" u2="G" k="14" />
<hkern u1="&#xca;" u2="C" k="14" />
<hkern u1="&#xca;" u2="&#x26;" k="14" />
<hkern u1="&#xcb;" u2="&#xfb04;" k="23" />
<hkern u1="&#xcb;" u2="&#xfb03;" k="23" />
<hkern u1="&#xcb;" u2="&#xfb02;" k="23" />
<hkern u1="&#xcb;" u2="&#xfb01;" k="23" />
<hkern u1="&#xcb;" u2="&#xff;" k="29" />
<hkern u1="&#xcb;" u2="&#xfd;" k="29" />
<hkern u1="&#xcb;" u2="&#xfc;" k="27" />
<hkern u1="&#xcb;" u2="&#xfb;" k="27" />
<hkern u1="&#xcb;" u2="&#xfa;" k="27" />
<hkern u1="&#xcb;" u2="&#xf9;" k="27" />
<hkern u1="&#xcb;" u2="&#xf8;" k="29" />
<hkern u1="&#xcb;" u2="&#xf6;" k="29" />
<hkern u1="&#xcb;" u2="&#xf5;" k="29" />
<hkern u1="&#xcb;" u2="&#xf4;" k="29" />
<hkern u1="&#xcb;" u2="&#xf3;" k="29" />
<hkern u1="&#xcb;" u2="&#xf2;" k="29" />
<hkern u1="&#xcb;" u2="&#xf1;" k="14" />
<hkern u1="&#xcb;" u2="&#xf0;" k="29" />
<hkern u1="&#xcb;" u2="&#xeb;" k="29" />
<hkern u1="&#xcb;" u2="&#xea;" k="29" />
<hkern u1="&#xcb;" u2="&#xe9;" k="29" />
<hkern u1="&#xcb;" u2="&#xe8;" k="29" />
<hkern u1="&#xcb;" u2="&#xe7;" k="29" />
<hkern u1="&#xcb;" u2="&#xe6;" k="14" />
<hkern u1="&#xcb;" u2="&#xe5;" k="14" />
<hkern u1="&#xcb;" u2="&#xe4;" k="14" />
<hkern u1="&#xcb;" u2="&#xe3;" k="14" />
<hkern u1="&#xcb;" u2="&#xe2;" k="14" />
<hkern u1="&#xcb;" u2="&#xe1;" k="14" />
<hkern u1="&#xcb;" u2="&#xe0;" k="14" />
<hkern u1="&#xcb;" u2="&#xdf;" k="23" />
<hkern u1="&#xcb;" u2="&#xd8;" k="14" />
<hkern u1="&#xcb;" u2="&#xd6;" k="14" />
<hkern u1="&#xcb;" u2="&#xd5;" k="14" />
<hkern u1="&#xcb;" u2="&#xd4;" k="14" />
<hkern u1="&#xcb;" u2="&#xd3;" k="14" />
<hkern u1="&#xcb;" u2="&#xd2;" k="14" />
<hkern u1="&#xcb;" u2="&#xc7;" k="14" />
<hkern u1="&#xcb;" u2="y" k="29" />
<hkern u1="&#xcb;" u2="v" k="33" />
<hkern u1="&#xcb;" u2="u" k="27" />
<hkern u1="&#xcb;" u2="t" k="35" />
<hkern u1="&#xcb;" u2="s" k="12" />
<hkern u1="&#xcb;" u2="r" k="14" />
<hkern u1="&#xcb;" u2="q" k="29" />
<hkern u1="&#xcb;" u2="p" k="14" />
<hkern u1="&#xcb;" u2="o" k="29" />
<hkern u1="&#xcb;" u2="n" k="14" />
<hkern u1="&#xcb;" u2="m" k="14" />
<hkern u1="&#xcb;" u2="g" k="29" />
<hkern u1="&#xcb;" u2="f" k="23" />
<hkern u1="&#xcb;" u2="e" k="29" />
<hkern u1="&#xcb;" u2="d" k="29" />
<hkern u1="&#xcb;" u2="c" k="29" />
<hkern u1="&#xcb;" u2="a" k="14" />
<hkern u1="&#xcb;" u2="Q" k="14" />
<hkern u1="&#xcb;" u2="O" k="14" />
<hkern u1="&#xcb;" u2="G" k="14" />
<hkern u1="&#xcb;" u2="C" k="14" />
<hkern u1="&#xcb;" u2="&#x26;" k="14" />
<hkern u1="&#xcc;" u2="&#xfb04;" k="16" />
<hkern u1="&#xcc;" u2="&#xfb03;" k="16" />
<hkern u1="&#xcc;" u2="&#xfb02;" k="16" />
<hkern u1="&#xcc;" u2="&#xfb01;" k="16" />
<hkern u1="&#xcc;" u2="&#xff;" k="14" />
<hkern u1="&#xcc;" u2="&#xfe;" k="20" />
<hkern u1="&#xcc;" u2="&#xfd;" k="14" />
<hkern u1="&#xcc;" u2="&#xfc;" k="27" />
<hkern u1="&#xcc;" u2="&#xfb;" k="27" />
<hkern u1="&#xcc;" u2="&#xfa;" k="27" />
<hkern u1="&#xcc;" u2="&#xf9;" k="27" />
<hkern u1="&#xcc;" u2="&#xf8;" k="29" />
<hkern u1="&#xcc;" u2="&#xf6;" k="29" />
<hkern u1="&#xcc;" u2="&#xf5;" k="29" />
<hkern u1="&#xcc;" u2="&#xf4;" k="29" />
<hkern u1="&#xcc;" u2="&#xf3;" k="29" />
<hkern u1="&#xcc;" u2="&#xf2;" k="29" />
<hkern u1="&#xcc;" u2="&#xf1;" k="20" />
<hkern u1="&#xcc;" u2="&#xf0;" k="29" />
<hkern u1="&#xcc;" u2="&#xef;" k="20" />
<hkern u1="&#xcc;" u2="&#xee;" k="20" />
<hkern u1="&#xcc;" u2="&#xed;" k="20" />
<hkern u1="&#xcc;" u2="&#xec;" k="20" />
<hkern u1="&#xcc;" u2="&#xeb;" k="29" />
<hkern u1="&#xcc;" u2="&#xea;" k="29" />
<hkern u1="&#xcc;" u2="&#xe9;" k="29" />
<hkern u1="&#xcc;" u2="&#xe8;" k="29" />
<hkern u1="&#xcc;" u2="&#xe7;" k="29" />
<hkern u1="&#xcc;" u2="&#xe6;" k="23" />
<hkern u1="&#xcc;" u2="&#xe5;" k="23" />
<hkern u1="&#xcc;" u2="&#xe4;" k="23" />
<hkern u1="&#xcc;" u2="&#xe3;" k="23" />
<hkern u1="&#xcc;" u2="&#xe2;" k="23" />
<hkern u1="&#xcc;" u2="&#xe1;" k="23" />
<hkern u1="&#xcc;" u2="&#xe0;" k="23" />
<hkern u1="&#xcc;" u2="&#xdf;" k="16" />
<hkern u1="&#xcc;" u2="&#xdd;" k="10" />
<hkern u1="&#xcc;" u2="y" k="14" />
<hkern u1="&#xcc;" u2="v" k="18" />
<hkern u1="&#xcc;" u2="u" k="27" />
<hkern u1="&#xcc;" u2="t" k="23" />
<hkern u1="&#xcc;" u2="s" k="23" />
<hkern u1="&#xcc;" u2="r" k="20" />
<hkern u1="&#xcc;" u2="q" k="29" />
<hkern u1="&#xcc;" u2="p" k="20" />
<hkern u1="&#xcc;" u2="o" k="29" />
<hkern u1="&#xcc;" u2="n" k="20" />
<hkern u1="&#xcc;" u2="m" k="20" />
<hkern u1="&#xcc;" u2="k" k="20" />
<hkern u1="&#xcc;" u2="j" k="20" />
<hkern u1="&#xcc;" u2="i" k="20" />
<hkern u1="&#xcc;" u2="h" k="20" />
<hkern u1="&#xcc;" u2="g" k="29" />
<hkern u1="&#xcc;" u2="f" k="16" />
<hkern u1="&#xcc;" u2="e" k="29" />
<hkern u1="&#xcc;" u2="d" k="29" />
<hkern u1="&#xcc;" u2="c" k="29" />
<hkern u1="&#xcc;" u2="b" k="20" />
<hkern u1="&#xcc;" u2="a" k="23" />
<hkern u1="&#xcc;" u2="Y" k="10" />
<hkern u1="&#xcd;" u2="&#xfb04;" k="16" />
<hkern u1="&#xcd;" u2="&#xfb03;" k="16" />
<hkern u1="&#xcd;" u2="&#xfb02;" k="16" />
<hkern u1="&#xcd;" u2="&#xfb01;" k="16" />
<hkern u1="&#xcd;" u2="&#xff;" k="14" />
<hkern u1="&#xcd;" u2="&#xfe;" k="20" />
<hkern u1="&#xcd;" u2="&#xfd;" k="14" />
<hkern u1="&#xcd;" u2="&#xfc;" k="27" />
<hkern u1="&#xcd;" u2="&#xfb;" k="27" />
<hkern u1="&#xcd;" u2="&#xfa;" k="27" />
<hkern u1="&#xcd;" u2="&#xf9;" k="27" />
<hkern u1="&#xcd;" u2="&#xf8;" k="29" />
<hkern u1="&#xcd;" u2="&#xf6;" k="29" />
<hkern u1="&#xcd;" u2="&#xf5;" k="29" />
<hkern u1="&#xcd;" u2="&#xf4;" k="29" />
<hkern u1="&#xcd;" u2="&#xf3;" k="29" />
<hkern u1="&#xcd;" u2="&#xf2;" k="29" />
<hkern u1="&#xcd;" u2="&#xf1;" k="20" />
<hkern u1="&#xcd;" u2="&#xf0;" k="29" />
<hkern u1="&#xcd;" u2="&#xef;" k="20" />
<hkern u1="&#xcd;" u2="&#xee;" k="20" />
<hkern u1="&#xcd;" u2="&#xed;" k="20" />
<hkern u1="&#xcd;" u2="&#xec;" k="20" />
<hkern u1="&#xcd;" u2="&#xeb;" k="29" />
<hkern u1="&#xcd;" u2="&#xea;" k="29" />
<hkern u1="&#xcd;" u2="&#xe9;" k="29" />
<hkern u1="&#xcd;" u2="&#xe8;" k="29" />
<hkern u1="&#xcd;" u2="&#xe7;" k="29" />
<hkern u1="&#xcd;" u2="&#xe6;" k="23" />
<hkern u1="&#xcd;" u2="&#xe5;" k="23" />
<hkern u1="&#xcd;" u2="&#xe4;" k="23" />
<hkern u1="&#xcd;" u2="&#xe3;" k="23" />
<hkern u1="&#xcd;" u2="&#xe2;" k="23" />
<hkern u1="&#xcd;" u2="&#xe1;" k="23" />
<hkern u1="&#xcd;" u2="&#xe0;" k="23" />
<hkern u1="&#xcd;" u2="&#xdf;" k="16" />
<hkern u1="&#xcd;" u2="&#xdd;" k="10" />
<hkern u1="&#xcd;" u2="y" k="14" />
<hkern u1="&#xcd;" u2="v" k="18" />
<hkern u1="&#xcd;" u2="u" k="27" />
<hkern u1="&#xcd;" u2="t" k="23" />
<hkern u1="&#xcd;" u2="s" k="23" />
<hkern u1="&#xcd;" u2="r" k="20" />
<hkern u1="&#xcd;" u2="q" k="29" />
<hkern u1="&#xcd;" u2="p" k="20" />
<hkern u1="&#xcd;" u2="o" k="29" />
<hkern u1="&#xcd;" u2="n" k="20" />
<hkern u1="&#xcd;" u2="m" k="20" />
<hkern u1="&#xcd;" u2="k" k="20" />
<hkern u1="&#xcd;" u2="j" k="20" />
<hkern u1="&#xcd;" u2="i" k="20" />
<hkern u1="&#xcd;" u2="h" k="20" />
<hkern u1="&#xcd;" u2="g" k="29" />
<hkern u1="&#xcd;" u2="f" k="16" />
<hkern u1="&#xcd;" u2="e" k="29" />
<hkern u1="&#xcd;" u2="d" k="29" />
<hkern u1="&#xcd;" u2="c" k="29" />
<hkern u1="&#xcd;" u2="b" k="20" />
<hkern u1="&#xcd;" u2="a" k="23" />
<hkern u1="&#xcd;" u2="Y" k="10" />
<hkern u1="&#xce;" u2="&#xfb04;" k="16" />
<hkern u1="&#xce;" u2="&#xfb03;" k="16" />
<hkern u1="&#xce;" u2="&#xfb02;" k="16" />
<hkern u1="&#xce;" u2="&#xfb01;" k="16" />
<hkern u1="&#xce;" u2="&#xff;" k="14" />
<hkern u1="&#xce;" u2="&#xfe;" k="20" />
<hkern u1="&#xce;" u2="&#xfd;" k="14" />
<hkern u1="&#xce;" u2="&#xfc;" k="27" />
<hkern u1="&#xce;" u2="&#xfb;" k="27" />
<hkern u1="&#xce;" u2="&#xfa;" k="27" />
<hkern u1="&#xce;" u2="&#xf9;" k="27" />
<hkern u1="&#xce;" u2="&#xf8;" k="29" />
<hkern u1="&#xce;" u2="&#xf6;" k="29" />
<hkern u1="&#xce;" u2="&#xf5;" k="29" />
<hkern u1="&#xce;" u2="&#xf4;" k="29" />
<hkern u1="&#xce;" u2="&#xf3;" k="29" />
<hkern u1="&#xce;" u2="&#xf2;" k="29" />
<hkern u1="&#xce;" u2="&#xf1;" k="20" />
<hkern u1="&#xce;" u2="&#xf0;" k="29" />
<hkern u1="&#xce;" u2="&#xef;" k="20" />
<hkern u1="&#xce;" u2="&#xee;" k="20" />
<hkern u1="&#xce;" u2="&#xed;" k="20" />
<hkern u1="&#xce;" u2="&#xec;" k="20" />
<hkern u1="&#xce;" u2="&#xeb;" k="29" />
<hkern u1="&#xce;" u2="&#xea;" k="29" />
<hkern u1="&#xce;" u2="&#xe9;" k="29" />
<hkern u1="&#xce;" u2="&#xe8;" k="29" />
<hkern u1="&#xce;" u2="&#xe7;" k="29" />
<hkern u1="&#xce;" u2="&#xe6;" k="23" />
<hkern u1="&#xce;" u2="&#xe5;" k="23" />
<hkern u1="&#xce;" u2="&#xe4;" k="23" />
<hkern u1="&#xce;" u2="&#xe3;" k="23" />
<hkern u1="&#xce;" u2="&#xe2;" k="23" />
<hkern u1="&#xce;" u2="&#xe1;" k="23" />
<hkern u1="&#xce;" u2="&#xe0;" k="23" />
<hkern u1="&#xce;" u2="&#xdf;" k="16" />
<hkern u1="&#xce;" u2="&#xdd;" k="10" />
<hkern u1="&#xce;" u2="&#x7d;" k="-23" />
<hkern u1="&#xce;" u2="y" k="14" />
<hkern u1="&#xce;" u2="v" k="18" />
<hkern u1="&#xce;" u2="u" k="27" />
<hkern u1="&#xce;" u2="t" k="23" />
<hkern u1="&#xce;" u2="s" k="23" />
<hkern u1="&#xce;" u2="r" k="20" />
<hkern u1="&#xce;" u2="q" k="29" />
<hkern u1="&#xce;" u2="p" k="20" />
<hkern u1="&#xce;" u2="o" k="29" />
<hkern u1="&#xce;" u2="n" k="20" />
<hkern u1="&#xce;" u2="m" k="20" />
<hkern u1="&#xce;" u2="k" k="20" />
<hkern u1="&#xce;" u2="j" k="20" />
<hkern u1="&#xce;" u2="i" k="20" />
<hkern u1="&#xce;" u2="h" k="20" />
<hkern u1="&#xce;" u2="g" k="29" />
<hkern u1="&#xce;" u2="f" k="16" />
<hkern u1="&#xce;" u2="e" k="29" />
<hkern u1="&#xce;" u2="d" k="29" />
<hkern u1="&#xce;" u2="c" k="29" />
<hkern u1="&#xce;" u2="b" k="20" />
<hkern u1="&#xce;" u2="a" k="23" />
<hkern u1="&#xce;" u2="Y" k="10" />
<hkern u1="&#xce;" u2="&#x29;" k="-35" />
<hkern u1="&#xcf;" u2="&#xfb04;" k="16" />
<hkern u1="&#xcf;" u2="&#xfb03;" k="16" />
<hkern u1="&#xcf;" u2="&#xfb02;" k="16" />
<hkern u1="&#xcf;" u2="&#xfb01;" k="16" />
<hkern u1="&#xcf;" u2="&#xff;" k="14" />
<hkern u1="&#xcf;" u2="&#xfe;" k="20" />
<hkern u1="&#xcf;" u2="&#xfd;" k="14" />
<hkern u1="&#xcf;" u2="&#xfc;" k="27" />
<hkern u1="&#xcf;" u2="&#xfb;" k="27" />
<hkern u1="&#xcf;" u2="&#xfa;" k="27" />
<hkern u1="&#xcf;" u2="&#xf9;" k="27" />
<hkern u1="&#xcf;" u2="&#xf8;" k="29" />
<hkern u1="&#xcf;" u2="&#xf6;" k="29" />
<hkern u1="&#xcf;" u2="&#xf5;" k="29" />
<hkern u1="&#xcf;" u2="&#xf4;" k="29" />
<hkern u1="&#xcf;" u2="&#xf3;" k="29" />
<hkern u1="&#xcf;" u2="&#xf2;" k="29" />
<hkern u1="&#xcf;" u2="&#xf1;" k="20" />
<hkern u1="&#xcf;" u2="&#xf0;" k="29" />
<hkern u1="&#xcf;" u2="&#xef;" k="20" />
<hkern u1="&#xcf;" u2="&#xee;" k="20" />
<hkern u1="&#xcf;" u2="&#xed;" k="20" />
<hkern u1="&#xcf;" u2="&#xec;" k="20" />
<hkern u1="&#xcf;" u2="&#xeb;" k="29" />
<hkern u1="&#xcf;" u2="&#xea;" k="29" />
<hkern u1="&#xcf;" u2="&#xe9;" k="29" />
<hkern u1="&#xcf;" u2="&#xe8;" k="29" />
<hkern u1="&#xcf;" u2="&#xe7;" k="29" />
<hkern u1="&#xcf;" u2="&#xe6;" k="23" />
<hkern u1="&#xcf;" u2="&#xe5;" k="23" />
<hkern u1="&#xcf;" u2="&#xe4;" k="23" />
<hkern u1="&#xcf;" u2="&#xe3;" k="23" />
<hkern u1="&#xcf;" u2="&#xe2;" k="23" />
<hkern u1="&#xcf;" u2="&#xe1;" k="23" />
<hkern u1="&#xcf;" u2="&#xe0;" k="23" />
<hkern u1="&#xcf;" u2="&#xdf;" k="16" />
<hkern u1="&#xcf;" u2="&#xdd;" k="10" />
<hkern u1="&#xcf;" u2="y" k="14" />
<hkern u1="&#xcf;" u2="v" k="18" />
<hkern u1="&#xcf;" u2="u" k="27" />
<hkern u1="&#xcf;" u2="t" k="23" />
<hkern u1="&#xcf;" u2="s" k="23" />
<hkern u1="&#xcf;" u2="r" k="20" />
<hkern u1="&#xcf;" u2="q" k="29" />
<hkern u1="&#xcf;" u2="p" k="20" />
<hkern u1="&#xcf;" u2="o" k="29" />
<hkern u1="&#xcf;" u2="n" k="20" />
<hkern u1="&#xcf;" u2="m" k="20" />
<hkern u1="&#xcf;" u2="k" k="20" />
<hkern u1="&#xcf;" u2="j" k="20" />
<hkern u1="&#xcf;" u2="i" k="20" />
<hkern u1="&#xcf;" u2="h" k="20" />
<hkern u1="&#xcf;" u2="g" k="29" />
<hkern u1="&#xcf;" u2="f" k="16" />
<hkern u1="&#xcf;" u2="e" k="29" />
<hkern u1="&#xcf;" u2="d" k="29" />
<hkern u1="&#xcf;" u2="c" k="29" />
<hkern u1="&#xcf;" u2="b" k="20" />
<hkern u1="&#xcf;" u2="a" k="23" />
<hkern u1="&#xcf;" u2="Y" k="10" />
<hkern u1="&#xd0;" u2="&#x2122;" k="16" />
<hkern u1="&#xd0;" u2="&#x2026;" k="39" />
<hkern u1="&#xd0;" u2="&#x201e;" k="39" />
<hkern u1="&#xd0;" u2="&#x201a;" k="39" />
<hkern u1="&#xd0;" u2="&#xfe;" k="18" />
<hkern u1="&#xd0;" u2="&#xfc;" k="16" />
<hkern u1="&#xd0;" u2="&#xfb;" k="16" />
<hkern u1="&#xd0;" u2="&#xfa;" k="16" />
<hkern u1="&#xd0;" u2="&#xf9;" k="16" />
<hkern u1="&#xd0;" u2="&#xf8;" k="12" />
<hkern u1="&#xd0;" u2="&#xf6;" k="12" />
<hkern u1="&#xd0;" u2="&#xf5;" k="12" />
<hkern u1="&#xd0;" u2="&#xf4;" k="12" />
<hkern u1="&#xd0;" u2="&#xf3;" k="12" />
<hkern u1="&#xd0;" u2="&#xf2;" k="12" />
<hkern u1="&#xd0;" u2="&#xf1;" k="18" />
<hkern u1="&#xd0;" u2="&#xf0;" k="12" />
<hkern u1="&#xd0;" u2="&#xef;" k="20" />
<hkern u1="&#xd0;" u2="&#xee;" k="20" />
<hkern u1="&#xd0;" u2="&#xed;" k="20" />
<hkern u1="&#xd0;" u2="&#xec;" k="20" />
<hkern u1="&#xd0;" u2="&#xeb;" k="12" />
<hkern u1="&#xd0;" u2="&#xea;" k="12" />
<hkern u1="&#xd0;" u2="&#xe9;" k="12" />
<hkern u1="&#xd0;" u2="&#xe8;" k="12" />
<hkern u1="&#xd0;" u2="&#xe7;" k="12" />
<hkern u1="&#xd0;" u2="&#xe6;" k="20" />
<hkern u1="&#xd0;" u2="&#xe5;" k="20" />
<hkern u1="&#xd0;" u2="&#xe4;" k="20" />
<hkern u1="&#xd0;" u2="&#xe3;" k="20" />
<hkern u1="&#xd0;" u2="&#xe2;" k="20" />
<hkern u1="&#xd0;" u2="&#xe1;" k="20" />
<hkern u1="&#xd0;" u2="&#xe0;" k="20" />
<hkern u1="&#xd0;" u2="&#xdd;" k="68" />
<hkern u1="&#xd0;" u2="&#xc6;" k="96" />
<hkern u1="&#xd0;" u2="&#xc5;" k="31" />
<hkern u1="&#xd0;" u2="&#xc4;" k="31" />
<hkern u1="&#xd0;" u2="&#xc3;" k="31" />
<hkern u1="&#xd0;" u2="&#xc2;" k="31" />
<hkern u1="&#xd0;" u2="&#xc1;" k="31" />
<hkern u1="&#xd0;" u2="&#xc0;" k="31" />
<hkern u1="&#xd0;" u2="&#x7d;" k="37" />
<hkern u1="&#xd0;" u2="x" k="27" />
<hkern u1="&#xd0;" u2="u" k="16" />
<hkern u1="&#xd0;" u2="s" k="12" />
<hkern u1="&#xd0;" u2="r" k="18" />
<hkern u1="&#xd0;" u2="q" k="12" />
<hkern u1="&#xd0;" u2="p" k="18" />
<hkern u1="&#xd0;" u2="o" k="12" />
<hkern u1="&#xd0;" u2="n" k="18" />
<hkern u1="&#xd0;" u2="m" k="18" />
<hkern u1="&#xd0;" u2="k" k="18" />
<hkern u1="&#xd0;" u2="j" k="20" />
<hkern u1="&#xd0;" u2="i" k="20" />
<hkern u1="&#xd0;" u2="h" k="18" />
<hkern u1="&#xd0;" u2="g" k="12" />
<hkern u1="&#xd0;" u2="e" k="12" />
<hkern u1="&#xd0;" u2="d" k="12" />
<hkern u1="&#xd0;" u2="c" k="12" />
<hkern u1="&#xd0;" u2="b" k="18" />
<hkern u1="&#xd0;" u2="a" k="20" />
<hkern u1="&#xd0;" u2="]" k="37" />
<hkern u1="&#xd0;" u2="Y" k="68" />
<hkern u1="&#xd0;" u2="X" k="57" />
<hkern u1="&#xd0;" u2="V" k="29" />
<hkern u1="&#xd0;" u2="T" k="57" />
<hkern u1="&#xd0;" u2="A" k="31" />
<hkern u1="&#xd0;" u2="&#x2f;" k="20" />
<hkern u1="&#xd0;" u2="&#x2e;" k="39" />
<hkern u1="&#xd0;" u2="&#x2c;" k="39" />
<hkern u1="&#xd0;" u2="&#x29;" k="43" />
<hkern u1="&#xd1;" u2="&#xfb04;" k="16" />
<hkern u1="&#xd1;" u2="&#xfb03;" k="16" />
<hkern u1="&#xd1;" u2="&#xfb02;" k="16" />
<hkern u1="&#xd1;" u2="&#xfb01;" k="16" />
<hkern u1="&#xd1;" u2="&#xff;" k="14" />
<hkern u1="&#xd1;" u2="&#xfe;" k="20" />
<hkern u1="&#xd1;" u2="&#xfd;" k="14" />
<hkern u1="&#xd1;" u2="&#xfc;" k="27" />
<hkern u1="&#xd1;" u2="&#xfb;" k="27" />
<hkern u1="&#xd1;" u2="&#xfa;" k="27" />
<hkern u1="&#xd1;" u2="&#xf9;" k="27" />
<hkern u1="&#xd1;" u2="&#xf8;" k="29" />
<hkern u1="&#xd1;" u2="&#xf6;" k="29" />
<hkern u1="&#xd1;" u2="&#xf5;" k="29" />
<hkern u1="&#xd1;" u2="&#xf4;" k="29" />
<hkern u1="&#xd1;" u2="&#xf3;" k="29" />
<hkern u1="&#xd1;" u2="&#xf2;" k="29" />
<hkern u1="&#xd1;" u2="&#xf1;" k="20" />
<hkern u1="&#xd1;" u2="&#xf0;" k="29" />
<hkern u1="&#xd1;" u2="&#xef;" k="20" />
<hkern u1="&#xd1;" u2="&#xee;" k="20" />
<hkern u1="&#xd1;" u2="&#xed;" k="20" />
<hkern u1="&#xd1;" u2="&#xec;" k="20" />
<hkern u1="&#xd1;" u2="&#xeb;" k="29" />
<hkern u1="&#xd1;" u2="&#xea;" k="29" />
<hkern u1="&#xd1;" u2="&#xe9;" k="29" />
<hkern u1="&#xd1;" u2="&#xe8;" k="29" />
<hkern u1="&#xd1;" u2="&#xe7;" k="29" />
<hkern u1="&#xd1;" u2="&#xe6;" k="23" />
<hkern u1="&#xd1;" u2="&#xe5;" k="23" />
<hkern u1="&#xd1;" u2="&#xe4;" k="23" />
<hkern u1="&#xd1;" u2="&#xe3;" k="23" />
<hkern u1="&#xd1;" u2="&#xe2;" k="23" />
<hkern u1="&#xd1;" u2="&#xe1;" k="23" />
<hkern u1="&#xd1;" u2="&#xe0;" k="23" />
<hkern u1="&#xd1;" u2="&#xdf;" k="16" />
<hkern u1="&#xd1;" u2="&#xdd;" k="10" />
<hkern u1="&#xd1;" u2="y" k="14" />
<hkern u1="&#xd1;" u2="v" k="18" />
<hkern u1="&#xd1;" u2="u" k="27" />
<hkern u1="&#xd1;" u2="t" k="23" />
<hkern u1="&#xd1;" u2="s" k="23" />
<hkern u1="&#xd1;" u2="r" k="20" />
<hkern u1="&#xd1;" u2="q" k="29" />
<hkern u1="&#xd1;" u2="p" k="20" />
<hkern u1="&#xd1;" u2="o" k="29" />
<hkern u1="&#xd1;" u2="n" k="20" />
<hkern u1="&#xd1;" u2="m" k="20" />
<hkern u1="&#xd1;" u2="k" k="20" />
<hkern u1="&#xd1;" u2="j" k="20" />
<hkern u1="&#xd1;" u2="i" k="20" />
<hkern u1="&#xd1;" u2="h" k="20" />
<hkern u1="&#xd1;" u2="g" k="29" />
<hkern u1="&#xd1;" u2="f" k="16" />
<hkern u1="&#xd1;" u2="e" k="29" />
<hkern u1="&#xd1;" u2="d" k="29" />
<hkern u1="&#xd1;" u2="c" k="29" />
<hkern u1="&#xd1;" u2="b" k="20" />
<hkern u1="&#xd1;" u2="a" k="23" />
<hkern u1="&#xd1;" u2="Y" k="10" />
<hkern u1="&#xd2;" u2="&#x2122;" k="16" />
<hkern u1="&#xd2;" u2="&#x2026;" k="39" />
<hkern u1="&#xd2;" u2="&#x201e;" k="39" />
<hkern u1="&#xd2;" u2="&#x201a;" k="39" />
<hkern u1="&#xd2;" u2="&#xfe;" k="18" />
<hkern u1="&#xd2;" u2="&#xfc;" k="16" />
<hkern u1="&#xd2;" u2="&#xfb;" k="16" />
<hkern u1="&#xd2;" u2="&#xfa;" k="16" />
<hkern u1="&#xd2;" u2="&#xf9;" k="16" />
<hkern u1="&#xd2;" u2="&#xf8;" k="12" />
<hkern u1="&#xd2;" u2="&#xf6;" k="12" />
<hkern u1="&#xd2;" u2="&#xf5;" k="12" />
<hkern u1="&#xd2;" u2="&#xf4;" k="12" />
<hkern u1="&#xd2;" u2="&#xf3;" k="12" />
<hkern u1="&#xd2;" u2="&#xf2;" k="12" />
<hkern u1="&#xd2;" u2="&#xf1;" k="18" />
<hkern u1="&#xd2;" u2="&#xf0;" k="12" />
<hkern u1="&#xd2;" u2="&#xef;" k="20" />
<hkern u1="&#xd2;" u2="&#xee;" k="20" />
<hkern u1="&#xd2;" u2="&#xed;" k="20" />
<hkern u1="&#xd2;" u2="&#xec;" k="20" />
<hkern u1="&#xd2;" u2="&#xeb;" k="12" />
<hkern u1="&#xd2;" u2="&#xea;" k="12" />
<hkern u1="&#xd2;" u2="&#xe9;" k="12" />
<hkern u1="&#xd2;" u2="&#xe8;" k="12" />
<hkern u1="&#xd2;" u2="&#xe7;" k="12" />
<hkern u1="&#xd2;" u2="&#xe6;" k="20" />
<hkern u1="&#xd2;" u2="&#xe5;" k="20" />
<hkern u1="&#xd2;" u2="&#xe4;" k="20" />
<hkern u1="&#xd2;" u2="&#xe3;" k="20" />
<hkern u1="&#xd2;" u2="&#xe2;" k="20" />
<hkern u1="&#xd2;" u2="&#xe1;" k="20" />
<hkern u1="&#xd2;" u2="&#xe0;" k="20" />
<hkern u1="&#xd2;" u2="&#xdd;" k="63" />
<hkern u1="&#xd2;" u2="&#xc6;" k="90" />
<hkern u1="&#xd2;" u2="&#xc5;" k="29" />
<hkern u1="&#xd2;" u2="&#xc4;" k="29" />
<hkern u1="&#xd2;" u2="&#xc3;" k="29" />
<hkern u1="&#xd2;" u2="&#xc2;" k="29" />
<hkern u1="&#xd2;" u2="&#xc1;" k="29" />
<hkern u1="&#xd2;" u2="&#xc0;" k="29" />
<hkern u1="&#xd2;" u2="&#x7d;" k="35" />
<hkern u1="&#xd2;" u2="x" k="27" />
<hkern u1="&#xd2;" u2="u" k="16" />
<hkern u1="&#xd2;" u2="s" k="12" />
<hkern u1="&#xd2;" u2="r" k="18" />
<hkern u1="&#xd2;" u2="q" k="12" />
<hkern u1="&#xd2;" u2="p" k="18" />
<hkern u1="&#xd2;" u2="o" k="12" />
<hkern u1="&#xd2;" u2="n" k="18" />
<hkern u1="&#xd2;" u2="m" k="18" />
<hkern u1="&#xd2;" u2="k" k="18" />
<hkern u1="&#xd2;" u2="j" k="20" />
<hkern u1="&#xd2;" u2="i" k="20" />
<hkern u1="&#xd2;" u2="h" k="18" />
<hkern u1="&#xd2;" u2="g" k="12" />
<hkern u1="&#xd2;" u2="e" k="12" />
<hkern u1="&#xd2;" u2="d" k="12" />
<hkern u1="&#xd2;" u2="c" k="12" />
<hkern u1="&#xd2;" u2="b" k="18" />
<hkern u1="&#xd2;" u2="a" k="20" />
<hkern u1="&#xd2;" u2="]" k="37" />
<hkern u1="&#xd2;" u2="Y" k="63" />
<hkern u1="&#xd2;" u2="X" k="53" />
<hkern u1="&#xd2;" u2="V" k="27" />
<hkern u1="&#xd2;" u2="T" k="49" />
<hkern u1="&#xd2;" u2="A" k="29" />
<hkern u1="&#xd2;" u2="&#x2e;" k="39" />
<hkern u1="&#xd2;" u2="&#x2c;" k="39" />
<hkern u1="&#xd2;" u2="&#x29;" k="43" />
<hkern u1="&#xd3;" u2="&#x2122;" k="16" />
<hkern u1="&#xd3;" u2="&#x2026;" k="39" />
<hkern u1="&#xd3;" u2="&#x201e;" k="39" />
<hkern u1="&#xd3;" u2="&#x201a;" k="39" />
<hkern u1="&#xd3;" u2="&#xfe;" k="18" />
<hkern u1="&#xd3;" u2="&#xfc;" k="16" />
<hkern u1="&#xd3;" u2="&#xfb;" k="16" />
<hkern u1="&#xd3;" u2="&#xfa;" k="16" />
<hkern u1="&#xd3;" u2="&#xf9;" k="16" />
<hkern u1="&#xd3;" u2="&#xf8;" k="12" />
<hkern u1="&#xd3;" u2="&#xf6;" k="12" />
<hkern u1="&#xd3;" u2="&#xf5;" k="12" />
<hkern u1="&#xd3;" u2="&#xf4;" k="12" />
<hkern u1="&#xd3;" u2="&#xf3;" k="12" />
<hkern u1="&#xd3;" u2="&#xf2;" k="12" />
<hkern u1="&#xd3;" u2="&#xf1;" k="18" />
<hkern u1="&#xd3;" u2="&#xf0;" k="12" />
<hkern u1="&#xd3;" u2="&#xef;" k="20" />
<hkern u1="&#xd3;" u2="&#xee;" k="20" />
<hkern u1="&#xd3;" u2="&#xed;" k="20" />
<hkern u1="&#xd3;" u2="&#xec;" k="20" />
<hkern u1="&#xd3;" u2="&#xeb;" k="12" />
<hkern u1="&#xd3;" u2="&#xea;" k="12" />
<hkern u1="&#xd3;" u2="&#xe9;" k="12" />
<hkern u1="&#xd3;" u2="&#xe8;" k="12" />
<hkern u1="&#xd3;" u2="&#xe7;" k="12" />
<hkern u1="&#xd3;" u2="&#xe6;" k="20" />
<hkern u1="&#xd3;" u2="&#xe5;" k="20" />
<hkern u1="&#xd3;" u2="&#xe4;" k="20" />
<hkern u1="&#xd3;" u2="&#xe3;" k="20" />
<hkern u1="&#xd3;" u2="&#xe2;" k="20" />
<hkern u1="&#xd3;" u2="&#xe1;" k="20" />
<hkern u1="&#xd3;" u2="&#xe0;" k="20" />
<hkern u1="&#xd3;" u2="&#xdd;" k="63" />
<hkern u1="&#xd3;" u2="&#xc6;" k="90" />
<hkern u1="&#xd3;" u2="&#xc5;" k="29" />
<hkern u1="&#xd3;" u2="&#xc4;" k="29" />
<hkern u1="&#xd3;" u2="&#xc3;" k="29" />
<hkern u1="&#xd3;" u2="&#xc2;" k="29" />
<hkern u1="&#xd3;" u2="&#xc1;" k="29" />
<hkern u1="&#xd3;" u2="&#xc0;" k="29" />
<hkern u1="&#xd3;" u2="&#x7d;" k="35" />
<hkern u1="&#xd3;" u2="x" k="27" />
<hkern u1="&#xd3;" u2="u" k="16" />
<hkern u1="&#xd3;" u2="s" k="12" />
<hkern u1="&#xd3;" u2="r" k="18" />
<hkern u1="&#xd3;" u2="q" k="12" />
<hkern u1="&#xd3;" u2="p" k="18" />
<hkern u1="&#xd3;" u2="o" k="12" />
<hkern u1="&#xd3;" u2="n" k="18" />
<hkern u1="&#xd3;" u2="m" k="18" />
<hkern u1="&#xd3;" u2="k" k="18" />
<hkern u1="&#xd3;" u2="j" k="20" />
<hkern u1="&#xd3;" u2="i" k="20" />
<hkern u1="&#xd3;" u2="h" k="18" />
<hkern u1="&#xd3;" u2="g" k="12" />
<hkern u1="&#xd3;" u2="e" k="12" />
<hkern u1="&#xd3;" u2="d" k="12" />
<hkern u1="&#xd3;" u2="c" k="12" />
<hkern u1="&#xd3;" u2="b" k="18" />
<hkern u1="&#xd3;" u2="a" k="20" />
<hkern u1="&#xd3;" u2="]" k="37" />
<hkern u1="&#xd3;" u2="Y" k="63" />
<hkern u1="&#xd3;" u2="X" k="53" />
<hkern u1="&#xd3;" u2="V" k="27" />
<hkern u1="&#xd3;" u2="T" k="49" />
<hkern u1="&#xd3;" u2="A" k="29" />
<hkern u1="&#xd3;" u2="&#x2e;" k="39" />
<hkern u1="&#xd3;" u2="&#x2c;" k="39" />
<hkern u1="&#xd3;" u2="&#x29;" k="43" />
<hkern u1="&#xd4;" u2="&#x2122;" k="16" />
<hkern u1="&#xd4;" u2="&#x2026;" k="39" />
<hkern u1="&#xd4;" u2="&#x201e;" k="39" />
<hkern u1="&#xd4;" u2="&#x201a;" k="39" />
<hkern u1="&#xd4;" u2="&#xfe;" k="18" />
<hkern u1="&#xd4;" u2="&#xfc;" k="16" />
<hkern u1="&#xd4;" u2="&#xfb;" k="16" />
<hkern u1="&#xd4;" u2="&#xfa;" k="16" />
<hkern u1="&#xd4;" u2="&#xf9;" k="16" />
<hkern u1="&#xd4;" u2="&#xf8;" k="12" />
<hkern u1="&#xd4;" u2="&#xf6;" k="12" />
<hkern u1="&#xd4;" u2="&#xf5;" k="12" />
<hkern u1="&#xd4;" u2="&#xf4;" k="12" />
<hkern u1="&#xd4;" u2="&#xf3;" k="12" />
<hkern u1="&#xd4;" u2="&#xf2;" k="12" />
<hkern u1="&#xd4;" u2="&#xf1;" k="18" />
<hkern u1="&#xd4;" u2="&#xf0;" k="12" />
<hkern u1="&#xd4;" u2="&#xef;" k="20" />
<hkern u1="&#xd4;" u2="&#xee;" k="20" />
<hkern u1="&#xd4;" u2="&#xed;" k="20" />
<hkern u1="&#xd4;" u2="&#xec;" k="20" />
<hkern u1="&#xd4;" u2="&#xeb;" k="12" />
<hkern u1="&#xd4;" u2="&#xea;" k="12" />
<hkern u1="&#xd4;" u2="&#xe9;" k="12" />
<hkern u1="&#xd4;" u2="&#xe8;" k="12" />
<hkern u1="&#xd4;" u2="&#xe7;" k="12" />
<hkern u1="&#xd4;" u2="&#xe6;" k="20" />
<hkern u1="&#xd4;" u2="&#xe5;" k="20" />
<hkern u1="&#xd4;" u2="&#xe4;" k="20" />
<hkern u1="&#xd4;" u2="&#xe3;" k="20" />
<hkern u1="&#xd4;" u2="&#xe2;" k="20" />
<hkern u1="&#xd4;" u2="&#xe1;" k="20" />
<hkern u1="&#xd4;" u2="&#xe0;" k="20" />
<hkern u1="&#xd4;" u2="&#xdd;" k="63" />
<hkern u1="&#xd4;" u2="&#xc6;" k="90" />
<hkern u1="&#xd4;" u2="&#xc5;" k="29" />
<hkern u1="&#xd4;" u2="&#xc4;" k="29" />
<hkern u1="&#xd4;" u2="&#xc3;" k="29" />
<hkern u1="&#xd4;" u2="&#xc2;" k="29" />
<hkern u1="&#xd4;" u2="&#xc1;" k="29" />
<hkern u1="&#xd4;" u2="&#xc0;" k="29" />
<hkern u1="&#xd4;" u2="&#x7d;" k="35" />
<hkern u1="&#xd4;" u2="x" k="27" />
<hkern u1="&#xd4;" u2="u" k="16" />
<hkern u1="&#xd4;" u2="s" k="12" />
<hkern u1="&#xd4;" u2="r" k="18" />
<hkern u1="&#xd4;" u2="q" k="12" />
<hkern u1="&#xd4;" u2="p" k="18" />
<hkern u1="&#xd4;" u2="o" k="12" />
<hkern u1="&#xd4;" u2="n" k="18" />
<hkern u1="&#xd4;" u2="m" k="18" />
<hkern u1="&#xd4;" u2="k" k="18" />
<hkern u1="&#xd4;" u2="j" k="20" />
<hkern u1="&#xd4;" u2="i" k="20" />
<hkern u1="&#xd4;" u2="h" k="18" />
<hkern u1="&#xd4;" u2="g" k="12" />
<hkern u1="&#xd4;" u2="e" k="12" />
<hkern u1="&#xd4;" u2="d" k="12" />
<hkern u1="&#xd4;" u2="c" k="12" />
<hkern u1="&#xd4;" u2="b" k="18" />
<hkern u1="&#xd4;" u2="a" k="20" />
<hkern u1="&#xd4;" u2="]" k="37" />
<hkern u1="&#xd4;" u2="Y" k="63" />
<hkern u1="&#xd4;" u2="X" k="53" />
<hkern u1="&#xd4;" u2="V" k="27" />
<hkern u1="&#xd4;" u2="T" k="49" />
<hkern u1="&#xd4;" u2="A" k="29" />
<hkern u1="&#xd4;" u2="&#x2e;" k="39" />
<hkern u1="&#xd4;" u2="&#x2c;" k="39" />
<hkern u1="&#xd4;" u2="&#x29;" k="43" />
<hkern u1="&#xd5;" u2="&#x2122;" k="16" />
<hkern u1="&#xd5;" u2="&#x2026;" k="39" />
<hkern u1="&#xd5;" u2="&#x201e;" k="39" />
<hkern u1="&#xd5;" u2="&#x201a;" k="39" />
<hkern u1="&#xd5;" u2="&#xfe;" k="18" />
<hkern u1="&#xd5;" u2="&#xfc;" k="16" />
<hkern u1="&#xd5;" u2="&#xfb;" k="16" />
<hkern u1="&#xd5;" u2="&#xfa;" k="16" />
<hkern u1="&#xd5;" u2="&#xf9;" k="16" />
<hkern u1="&#xd5;" u2="&#xf8;" k="12" />
<hkern u1="&#xd5;" u2="&#xf6;" k="12" />
<hkern u1="&#xd5;" u2="&#xf5;" k="12" />
<hkern u1="&#xd5;" u2="&#xf4;" k="12" />
<hkern u1="&#xd5;" u2="&#xf3;" k="12" />
<hkern u1="&#xd5;" u2="&#xf2;" k="12" />
<hkern u1="&#xd5;" u2="&#xf1;" k="18" />
<hkern u1="&#xd5;" u2="&#xf0;" k="12" />
<hkern u1="&#xd5;" u2="&#xef;" k="20" />
<hkern u1="&#xd5;" u2="&#xee;" k="20" />
<hkern u1="&#xd5;" u2="&#xed;" k="20" />
<hkern u1="&#xd5;" u2="&#xec;" k="20" />
<hkern u1="&#xd5;" u2="&#xeb;" k="12" />
<hkern u1="&#xd5;" u2="&#xea;" k="12" />
<hkern u1="&#xd5;" u2="&#xe9;" k="12" />
<hkern u1="&#xd5;" u2="&#xe8;" k="12" />
<hkern u1="&#xd5;" u2="&#xe7;" k="12" />
<hkern u1="&#xd5;" u2="&#xe6;" k="20" />
<hkern u1="&#xd5;" u2="&#xe5;" k="20" />
<hkern u1="&#xd5;" u2="&#xe4;" k="20" />
<hkern u1="&#xd5;" u2="&#xe3;" k="20" />
<hkern u1="&#xd5;" u2="&#xe2;" k="20" />
<hkern u1="&#xd5;" u2="&#xe1;" k="20" />
<hkern u1="&#xd5;" u2="&#xe0;" k="20" />
<hkern u1="&#xd5;" u2="&#xdd;" k="63" />
<hkern u1="&#xd5;" u2="&#xc6;" k="90" />
<hkern u1="&#xd5;" u2="&#xc5;" k="29" />
<hkern u1="&#xd5;" u2="&#xc4;" k="29" />
<hkern u1="&#xd5;" u2="&#xc3;" k="29" />
<hkern u1="&#xd5;" u2="&#xc2;" k="29" />
<hkern u1="&#xd5;" u2="&#xc1;" k="29" />
<hkern u1="&#xd5;" u2="&#xc0;" k="29" />
<hkern u1="&#xd5;" u2="&#x7d;" k="35" />
<hkern u1="&#xd5;" u2="x" k="27" />
<hkern u1="&#xd5;" u2="u" k="16" />
<hkern u1="&#xd5;" u2="s" k="12" />
<hkern u1="&#xd5;" u2="r" k="18" />
<hkern u1="&#xd5;" u2="q" k="12" />
<hkern u1="&#xd5;" u2="p" k="18" />
<hkern u1="&#xd5;" u2="o" k="12" />
<hkern u1="&#xd5;" u2="n" k="18" />
<hkern u1="&#xd5;" u2="m" k="18" />
<hkern u1="&#xd5;" u2="k" k="18" />
<hkern u1="&#xd5;" u2="j" k="20" />
<hkern u1="&#xd5;" u2="i" k="20" />
<hkern u1="&#xd5;" u2="h" k="18" />
<hkern u1="&#xd5;" u2="g" k="12" />
<hkern u1="&#xd5;" u2="e" k="12" />
<hkern u1="&#xd5;" u2="d" k="12" />
<hkern u1="&#xd5;" u2="c" k="12" />
<hkern u1="&#xd5;" u2="b" k="18" />
<hkern u1="&#xd5;" u2="a" k="20" />
<hkern u1="&#xd5;" u2="]" k="37" />
<hkern u1="&#xd5;" u2="Y" k="63" />
<hkern u1="&#xd5;" u2="X" k="53" />
<hkern u1="&#xd5;" u2="V" k="27" />
<hkern u1="&#xd5;" u2="T" k="49" />
<hkern u1="&#xd5;" u2="A" k="29" />
<hkern u1="&#xd5;" u2="&#x2e;" k="39" />
<hkern u1="&#xd5;" u2="&#x2c;" k="39" />
<hkern u1="&#xd5;" u2="&#x29;" k="43" />
<hkern u1="&#xd6;" u2="&#x2122;" k="16" />
<hkern u1="&#xd6;" u2="&#x2026;" k="39" />
<hkern u1="&#xd6;" u2="&#x201e;" k="39" />
<hkern u1="&#xd6;" u2="&#x201a;" k="39" />
<hkern u1="&#xd6;" u2="&#xfe;" k="18" />
<hkern u1="&#xd6;" u2="&#xfc;" k="16" />
<hkern u1="&#xd6;" u2="&#xfb;" k="16" />
<hkern u1="&#xd6;" u2="&#xfa;" k="16" />
<hkern u1="&#xd6;" u2="&#xf9;" k="16" />
<hkern u1="&#xd6;" u2="&#xf8;" k="12" />
<hkern u1="&#xd6;" u2="&#xf6;" k="12" />
<hkern u1="&#xd6;" u2="&#xf5;" k="12" />
<hkern u1="&#xd6;" u2="&#xf4;" k="12" />
<hkern u1="&#xd6;" u2="&#xf3;" k="12" />
<hkern u1="&#xd6;" u2="&#xf2;" k="12" />
<hkern u1="&#xd6;" u2="&#xf1;" k="18" />
<hkern u1="&#xd6;" u2="&#xf0;" k="12" />
<hkern u1="&#xd6;" u2="&#xef;" k="20" />
<hkern u1="&#xd6;" u2="&#xee;" k="20" />
<hkern u1="&#xd6;" u2="&#xed;" k="20" />
<hkern u1="&#xd6;" u2="&#xec;" k="20" />
<hkern u1="&#xd6;" u2="&#xeb;" k="12" />
<hkern u1="&#xd6;" u2="&#xea;" k="12" />
<hkern u1="&#xd6;" u2="&#xe9;" k="12" />
<hkern u1="&#xd6;" u2="&#xe8;" k="12" />
<hkern u1="&#xd6;" u2="&#xe7;" k="12" />
<hkern u1="&#xd6;" u2="&#xe6;" k="20" />
<hkern u1="&#xd6;" u2="&#xe5;" k="20" />
<hkern u1="&#xd6;" u2="&#xe4;" k="20" />
<hkern u1="&#xd6;" u2="&#xe3;" k="20" />
<hkern u1="&#xd6;" u2="&#xe2;" k="20" />
<hkern u1="&#xd6;" u2="&#xe1;" k="20" />
<hkern u1="&#xd6;" u2="&#xe0;" k="20" />
<hkern u1="&#xd6;" u2="&#xdd;" k="63" />
<hkern u1="&#xd6;" u2="&#xc6;" k="90" />
<hkern u1="&#xd6;" u2="&#xc5;" k="29" />
<hkern u1="&#xd6;" u2="&#xc4;" k="29" />
<hkern u1="&#xd6;" u2="&#xc3;" k="29" />
<hkern u1="&#xd6;" u2="&#xc2;" k="29" />
<hkern u1="&#xd6;" u2="&#xc1;" k="29" />
<hkern u1="&#xd6;" u2="&#xc0;" k="29" />
<hkern u1="&#xd6;" u2="&#x7d;" k="35" />
<hkern u1="&#xd6;" u2="x" k="27" />
<hkern u1="&#xd6;" u2="u" k="16" />
<hkern u1="&#xd6;" u2="s" k="12" />
<hkern u1="&#xd6;" u2="r" k="18" />
<hkern u1="&#xd6;" u2="q" k="12" />
<hkern u1="&#xd6;" u2="p" k="18" />
<hkern u1="&#xd6;" u2="o" k="12" />
<hkern u1="&#xd6;" u2="n" k="18" />
<hkern u1="&#xd6;" u2="m" k="18" />
<hkern u1="&#xd6;" u2="k" k="18" />
<hkern u1="&#xd6;" u2="j" k="20" />
<hkern u1="&#xd6;" u2="i" k="20" />
<hkern u1="&#xd6;" u2="h" k="18" />
<hkern u1="&#xd6;" u2="g" k="12" />
<hkern u1="&#xd6;" u2="e" k="12" />
<hkern u1="&#xd6;" u2="d" k="12" />
<hkern u1="&#xd6;" u2="c" k="12" />
<hkern u1="&#xd6;" u2="b" k="18" />
<hkern u1="&#xd6;" u2="a" k="20" />
<hkern u1="&#xd6;" u2="]" k="37" />
<hkern u1="&#xd6;" u2="Y" k="63" />
<hkern u1="&#xd6;" u2="X" k="53" />
<hkern u1="&#xd6;" u2="V" k="27" />
<hkern u1="&#xd6;" u2="T" k="49" />
<hkern u1="&#xd6;" u2="A" k="29" />
<hkern u1="&#xd6;" u2="&#x2e;" k="39" />
<hkern u1="&#xd6;" u2="&#x2c;" k="39" />
<hkern u1="&#xd6;" u2="&#x29;" k="43" />
<hkern u1="&#xd8;" u2="&#x2122;" k="16" />
<hkern u1="&#xd8;" u2="&#x2026;" k="39" />
<hkern u1="&#xd8;" u2="&#x201e;" k="39" />
<hkern u1="&#xd8;" u2="&#x201a;" k="39" />
<hkern u1="&#xd8;" u2="&#xfe;" k="18" />
<hkern u1="&#xd8;" u2="&#xfc;" k="16" />
<hkern u1="&#xd8;" u2="&#xfb;" k="16" />
<hkern u1="&#xd8;" u2="&#xfa;" k="16" />
<hkern u1="&#xd8;" u2="&#xf9;" k="16" />
<hkern u1="&#xd8;" u2="&#xf8;" k="12" />
<hkern u1="&#xd8;" u2="&#xf6;" k="12" />
<hkern u1="&#xd8;" u2="&#xf5;" k="12" />
<hkern u1="&#xd8;" u2="&#xf4;" k="12" />
<hkern u1="&#xd8;" u2="&#xf3;" k="12" />
<hkern u1="&#xd8;" u2="&#xf2;" k="12" />
<hkern u1="&#xd8;" u2="&#xf1;" k="18" />
<hkern u1="&#xd8;" u2="&#xf0;" k="12" />
<hkern u1="&#xd8;" u2="&#xef;" k="20" />
<hkern u1="&#xd8;" u2="&#xee;" k="20" />
<hkern u1="&#xd8;" u2="&#xed;" k="20" />
<hkern u1="&#xd8;" u2="&#xec;" k="20" />
<hkern u1="&#xd8;" u2="&#xeb;" k="12" />
<hkern u1="&#xd8;" u2="&#xea;" k="12" />
<hkern u1="&#xd8;" u2="&#xe9;" k="12" />
<hkern u1="&#xd8;" u2="&#xe8;" k="12" />
<hkern u1="&#xd8;" u2="&#xe7;" k="12" />
<hkern u1="&#xd8;" u2="&#xe6;" k="20" />
<hkern u1="&#xd8;" u2="&#xe5;" k="20" />
<hkern u1="&#xd8;" u2="&#xe4;" k="20" />
<hkern u1="&#xd8;" u2="&#xe3;" k="20" />
<hkern u1="&#xd8;" u2="&#xe2;" k="20" />
<hkern u1="&#xd8;" u2="&#xe1;" k="20" />
<hkern u1="&#xd8;" u2="&#xe0;" k="20" />
<hkern u1="&#xd8;" u2="&#xdd;" k="63" />
<hkern u1="&#xd8;" u2="&#xc6;" k="90" />
<hkern u1="&#xd8;" u2="&#xc5;" k="29" />
<hkern u1="&#xd8;" u2="&#xc4;" k="29" />
<hkern u1="&#xd8;" u2="&#xc3;" k="29" />
<hkern u1="&#xd8;" u2="&#xc2;" k="29" />
<hkern u1="&#xd8;" u2="&#xc1;" k="29" />
<hkern u1="&#xd8;" u2="&#xc0;" k="29" />
<hkern u1="&#xd8;" u2="&#x7d;" k="35" />
<hkern u1="&#xd8;" u2="x" k="27" />
<hkern u1="&#xd8;" u2="u" k="16" />
<hkern u1="&#xd8;" u2="s" k="12" />
<hkern u1="&#xd8;" u2="r" k="18" />
<hkern u1="&#xd8;" u2="q" k="12" />
<hkern u1="&#xd8;" u2="p" k="18" />
<hkern u1="&#xd8;" u2="o" k="12" />
<hkern u1="&#xd8;" u2="n" k="18" />
<hkern u1="&#xd8;" u2="m" k="18" />
<hkern u1="&#xd8;" u2="k" k="18" />
<hkern u1="&#xd8;" u2="j" k="20" />
<hkern u1="&#xd8;" u2="i" k="20" />
<hkern u1="&#xd8;" u2="h" k="18" />
<hkern u1="&#xd8;" u2="g" k="12" />
<hkern u1="&#xd8;" u2="e" k="12" />
<hkern u1="&#xd8;" u2="d" k="12" />
<hkern u1="&#xd8;" u2="c" k="12" />
<hkern u1="&#xd8;" u2="b" k="18" />
<hkern u1="&#xd8;" u2="a" k="20" />
<hkern u1="&#xd8;" u2="]" k="37" />
<hkern u1="&#xd8;" u2="Y" k="63" />
<hkern u1="&#xd8;" u2="X" k="53" />
<hkern u1="&#xd8;" u2="V" k="27" />
<hkern u1="&#xd8;" u2="T" k="49" />
<hkern u1="&#xd8;" u2="A" k="29" />
<hkern u1="&#xd8;" u2="&#x2e;" k="39" />
<hkern u1="&#xd8;" u2="&#x2c;" k="39" />
<hkern u1="&#xd8;" u2="&#x29;" k="43" />
<hkern u1="&#xd9;" u2="&#xfb04;" k="16" />
<hkern u1="&#xd9;" u2="&#xfb03;" k="16" />
<hkern u1="&#xd9;" u2="&#xfb02;" k="16" />
<hkern u1="&#xd9;" u2="&#xfb01;" k="16" />
<hkern u1="&#xd9;" u2="&#x2026;" k="37" />
<hkern u1="&#xd9;" u2="&#x201e;" k="37" />
<hkern u1="&#xd9;" u2="&#x201a;" k="37" />
<hkern u1="&#xd9;" u2="&#xff;" k="12" />
<hkern u1="&#xd9;" u2="&#xfe;" k="27" />
<hkern u1="&#xd9;" u2="&#xfd;" k="12" />
<hkern u1="&#xd9;" u2="&#xfc;" k="37" />
<hkern u1="&#xd9;" u2="&#xfb;" k="37" />
<hkern u1="&#xd9;" u2="&#xfa;" k="37" />
<hkern u1="&#xd9;" u2="&#xf9;" k="37" />
<hkern u1="&#xd9;" u2="&#xf8;" k="31" />
<hkern u1="&#xd9;" u2="&#xf6;" k="31" />
<hkern u1="&#xd9;" u2="&#xf5;" k="31" />
<hkern u1="&#xd9;" u2="&#xf4;" k="31" />
<hkern u1="&#xd9;" u2="&#xf3;" k="31" />
<hkern u1="&#xd9;" u2="&#xf2;" k="31" />
<hkern u1="&#xd9;" u2="&#xf1;" k="37" />
<hkern u1="&#xd9;" u2="&#xf0;" k="31" />
<hkern u1="&#xd9;" u2="&#xef;" k="31" />
<hkern u1="&#xd9;" u2="&#xee;" k="31" />
<hkern u1="&#xd9;" u2="&#xed;" k="31" />
<hkern u1="&#xd9;" u2="&#xec;" k="31" />
<hkern u1="&#xd9;" u2="&#xeb;" k="31" />
<hkern u1="&#xd9;" u2="&#xea;" k="31" />
<hkern u1="&#xd9;" u2="&#xe9;" k="31" />
<hkern u1="&#xd9;" u2="&#xe8;" k="31" />
<hkern u1="&#xd9;" u2="&#xe7;" k="31" />
<hkern u1="&#xd9;" u2="&#xe6;" k="35" />
<hkern u1="&#xd9;" u2="&#xe5;" k="35" />
<hkern u1="&#xd9;" u2="&#xe4;" k="35" />
<hkern u1="&#xd9;" u2="&#xe3;" k="35" />
<hkern u1="&#xd9;" u2="&#xe2;" k="35" />
<hkern u1="&#xd9;" u2="&#xe1;" k="35" />
<hkern u1="&#xd9;" u2="&#xe0;" k="35" />
<hkern u1="&#xd9;" u2="&#xdf;" k="16" />
<hkern u1="&#xd9;" u2="&#xc6;" k="20" />
<hkern u1="&#xd9;" u2="&#xc5;" k="41" />
<hkern u1="&#xd9;" u2="&#xc4;" k="41" />
<hkern u1="&#xd9;" u2="&#xc3;" k="41" />
<hkern u1="&#xd9;" u2="&#xc2;" k="41" />
<hkern u1="&#xd9;" u2="&#xc1;" k="41" />
<hkern u1="&#xd9;" u2="&#xc0;" k="41" />
<hkern u1="&#xd9;" u2="y" k="12" />
<hkern u1="&#xd9;" u2="x" k="20" />
<hkern u1="&#xd9;" u2="v" k="16" />
<hkern u1="&#xd9;" u2="u" k="37" />
<hkern u1="&#xd9;" u2="t" k="20" />
<hkern u1="&#xd9;" u2="s" k="33" />
<hkern u1="&#xd9;" u2="r" k="37" />
<hkern u1="&#xd9;" u2="q" k="31" />
<hkern u1="&#xd9;" u2="p" k="37" />
<hkern u1="&#xd9;" u2="o" k="31" />
<hkern u1="&#xd9;" u2="n" k="37" />
<hkern u1="&#xd9;" u2="m" k="37" />
<hkern u1="&#xd9;" u2="k" k="27" />
<hkern u1="&#xd9;" u2="j" k="31" />
<hkern u1="&#xd9;" u2="i" k="31" />
<hkern u1="&#xd9;" u2="h" k="27" />
<hkern u1="&#xd9;" u2="g" k="31" />
<hkern u1="&#xd9;" u2="f" k="16" />
<hkern u1="&#xd9;" u2="e" k="31" />
<hkern u1="&#xd9;" u2="d" k="31" />
<hkern u1="&#xd9;" u2="c" k="31" />
<hkern u1="&#xd9;" u2="b" k="27" />
<hkern u1="&#xd9;" u2="a" k="35" />
<hkern u1="&#xd9;" u2="A" k="41" />
<hkern u1="&#xd9;" u2="&#x2f;" k="29" />
<hkern u1="&#xd9;" u2="&#x2e;" k="37" />
<hkern u1="&#xd9;" u2="&#x2c;" k="37" />
<hkern u1="&#xd9;" u2="&#x29;" k="23" />
<hkern u1="&#xda;" u2="&#xfb04;" k="16" />
<hkern u1="&#xda;" u2="&#xfb03;" k="16" />
<hkern u1="&#xda;" u2="&#xfb02;" k="16" />
<hkern u1="&#xda;" u2="&#xfb01;" k="16" />
<hkern u1="&#xda;" u2="&#x2026;" k="37" />
<hkern u1="&#xda;" u2="&#x201e;" k="37" />
<hkern u1="&#xda;" u2="&#x201a;" k="37" />
<hkern u1="&#xda;" u2="&#xff;" k="12" />
<hkern u1="&#xda;" u2="&#xfe;" k="27" />
<hkern u1="&#xda;" u2="&#xfd;" k="12" />
<hkern u1="&#xda;" u2="&#xfc;" k="37" />
<hkern u1="&#xda;" u2="&#xfb;" k="37" />
<hkern u1="&#xda;" u2="&#xfa;" k="37" />
<hkern u1="&#xda;" u2="&#xf9;" k="37" />
<hkern u1="&#xda;" u2="&#xf8;" k="31" />
<hkern u1="&#xda;" u2="&#xf6;" k="31" />
<hkern u1="&#xda;" u2="&#xf5;" k="31" />
<hkern u1="&#xda;" u2="&#xf4;" k="31" />
<hkern u1="&#xda;" u2="&#xf3;" k="31" />
<hkern u1="&#xda;" u2="&#xf2;" k="31" />
<hkern u1="&#xda;" u2="&#xf1;" k="37" />
<hkern u1="&#xda;" u2="&#xf0;" k="31" />
<hkern u1="&#xda;" u2="&#xef;" k="31" />
<hkern u1="&#xda;" u2="&#xee;" k="31" />
<hkern u1="&#xda;" u2="&#xed;" k="31" />
<hkern u1="&#xda;" u2="&#xec;" k="31" />
<hkern u1="&#xda;" u2="&#xeb;" k="31" />
<hkern u1="&#xda;" u2="&#xea;" k="31" />
<hkern u1="&#xda;" u2="&#xe9;" k="31" />
<hkern u1="&#xda;" u2="&#xe8;" k="31" />
<hkern u1="&#xda;" u2="&#xe7;" k="31" />
<hkern u1="&#xda;" u2="&#xe6;" k="35" />
<hkern u1="&#xda;" u2="&#xe5;" k="35" />
<hkern u1="&#xda;" u2="&#xe4;" k="35" />
<hkern u1="&#xda;" u2="&#xe3;" k="35" />
<hkern u1="&#xda;" u2="&#xe2;" k="35" />
<hkern u1="&#xda;" u2="&#xe1;" k="35" />
<hkern u1="&#xda;" u2="&#xe0;" k="35" />
<hkern u1="&#xda;" u2="&#xdf;" k="16" />
<hkern u1="&#xda;" u2="&#xc6;" k="20" />
<hkern u1="&#xda;" u2="&#xc5;" k="41" />
<hkern u1="&#xda;" u2="&#xc4;" k="41" />
<hkern u1="&#xda;" u2="&#xc3;" k="41" />
<hkern u1="&#xda;" u2="&#xc2;" k="41" />
<hkern u1="&#xda;" u2="&#xc1;" k="41" />
<hkern u1="&#xda;" u2="&#xc0;" k="41" />
<hkern u1="&#xda;" u2="y" k="12" />
<hkern u1="&#xda;" u2="x" k="20" />
<hkern u1="&#xda;" u2="v" k="16" />
<hkern u1="&#xda;" u2="u" k="37" />
<hkern u1="&#xda;" u2="t" k="20" />
<hkern u1="&#xda;" u2="s" k="33" />
<hkern u1="&#xda;" u2="r" k="37" />
<hkern u1="&#xda;" u2="q" k="31" />
<hkern u1="&#xda;" u2="p" k="37" />
<hkern u1="&#xda;" u2="o" k="31" />
<hkern u1="&#xda;" u2="n" k="37" />
<hkern u1="&#xda;" u2="m" k="37" />
<hkern u1="&#xda;" u2="k" k="27" />
<hkern u1="&#xda;" u2="j" k="31" />
<hkern u1="&#xda;" u2="i" k="31" />
<hkern u1="&#xda;" u2="h" k="27" />
<hkern u1="&#xda;" u2="g" k="31" />
<hkern u1="&#xda;" u2="f" k="16" />
<hkern u1="&#xda;" u2="e" k="31" />
<hkern u1="&#xda;" u2="d" k="31" />
<hkern u1="&#xda;" u2="c" k="31" />
<hkern u1="&#xda;" u2="b" k="27" />
<hkern u1="&#xda;" u2="a" k="35" />
<hkern u1="&#xda;" u2="A" k="41" />
<hkern u1="&#xda;" u2="&#x2f;" k="29" />
<hkern u1="&#xda;" u2="&#x2e;" k="37" />
<hkern u1="&#xda;" u2="&#x2c;" k="37" />
<hkern u1="&#xda;" u2="&#x29;" k="23" />
<hkern u1="&#xdb;" u2="&#xfb04;" k="16" />
<hkern u1="&#xdb;" u2="&#xfb03;" k="16" />
<hkern u1="&#xdb;" u2="&#xfb02;" k="16" />
<hkern u1="&#xdb;" u2="&#xfb01;" k="16" />
<hkern u1="&#xdb;" u2="&#x2026;" k="37" />
<hkern u1="&#xdb;" u2="&#x201e;" k="37" />
<hkern u1="&#xdb;" u2="&#x201a;" k="37" />
<hkern u1="&#xdb;" u2="&#xff;" k="12" />
<hkern u1="&#xdb;" u2="&#xfe;" k="27" />
<hkern u1="&#xdb;" u2="&#xfd;" k="12" />
<hkern u1="&#xdb;" u2="&#xfc;" k="37" />
<hkern u1="&#xdb;" u2="&#xfb;" k="37" />
<hkern u1="&#xdb;" u2="&#xfa;" k="37" />
<hkern u1="&#xdb;" u2="&#xf9;" k="37" />
<hkern u1="&#xdb;" u2="&#xf8;" k="31" />
<hkern u1="&#xdb;" u2="&#xf6;" k="31" />
<hkern u1="&#xdb;" u2="&#xf5;" k="31" />
<hkern u1="&#xdb;" u2="&#xf4;" k="31" />
<hkern u1="&#xdb;" u2="&#xf3;" k="31" />
<hkern u1="&#xdb;" u2="&#xf2;" k="31" />
<hkern u1="&#xdb;" u2="&#xf1;" k="37" />
<hkern u1="&#xdb;" u2="&#xf0;" k="31" />
<hkern u1="&#xdb;" u2="&#xef;" k="31" />
<hkern u1="&#xdb;" u2="&#xee;" k="31" />
<hkern u1="&#xdb;" u2="&#xed;" k="31" />
<hkern u1="&#xdb;" u2="&#xec;" k="31" />
<hkern u1="&#xdb;" u2="&#xeb;" k="31" />
<hkern u1="&#xdb;" u2="&#xea;" k="31" />
<hkern u1="&#xdb;" u2="&#xe9;" k="31" />
<hkern u1="&#xdb;" u2="&#xe8;" k="31" />
<hkern u1="&#xdb;" u2="&#xe7;" k="31" />
<hkern u1="&#xdb;" u2="&#xe6;" k="35" />
<hkern u1="&#xdb;" u2="&#xe5;" k="35" />
<hkern u1="&#xdb;" u2="&#xe4;" k="35" />
<hkern u1="&#xdb;" u2="&#xe3;" k="35" />
<hkern u1="&#xdb;" u2="&#xe2;" k="35" />
<hkern u1="&#xdb;" u2="&#xe1;" k="35" />
<hkern u1="&#xdb;" u2="&#xe0;" k="35" />
<hkern u1="&#xdb;" u2="&#xdf;" k="16" />
<hkern u1="&#xdb;" u2="&#xc6;" k="20" />
<hkern u1="&#xdb;" u2="&#xc5;" k="41" />
<hkern u1="&#xdb;" u2="&#xc4;" k="41" />
<hkern u1="&#xdb;" u2="&#xc3;" k="41" />
<hkern u1="&#xdb;" u2="&#xc2;" k="41" />
<hkern u1="&#xdb;" u2="&#xc1;" k="41" />
<hkern u1="&#xdb;" u2="&#xc0;" k="41" />
<hkern u1="&#xdb;" u2="y" k="12" />
<hkern u1="&#xdb;" u2="x" k="20" />
<hkern u1="&#xdb;" u2="v" k="16" />
<hkern u1="&#xdb;" u2="u" k="37" />
<hkern u1="&#xdb;" u2="t" k="20" />
<hkern u1="&#xdb;" u2="s" k="33" />
<hkern u1="&#xdb;" u2="r" k="37" />
<hkern u1="&#xdb;" u2="q" k="31" />
<hkern u1="&#xdb;" u2="p" k="37" />
<hkern u1="&#xdb;" u2="o" k="31" />
<hkern u1="&#xdb;" u2="n" k="37" />
<hkern u1="&#xdb;" u2="m" k="37" />
<hkern u1="&#xdb;" u2="k" k="27" />
<hkern u1="&#xdb;" u2="j" k="31" />
<hkern u1="&#xdb;" u2="i" k="31" />
<hkern u1="&#xdb;" u2="h" k="27" />
<hkern u1="&#xdb;" u2="g" k="31" />
<hkern u1="&#xdb;" u2="f" k="16" />
<hkern u1="&#xdb;" u2="e" k="31" />
<hkern u1="&#xdb;" u2="d" k="31" />
<hkern u1="&#xdb;" u2="c" k="31" />
<hkern u1="&#xdb;" u2="b" k="27" />
<hkern u1="&#xdb;" u2="a" k="35" />
<hkern u1="&#xdb;" u2="A" k="41" />
<hkern u1="&#xdb;" u2="&#x2f;" k="29" />
<hkern u1="&#xdb;" u2="&#x2e;" k="37" />
<hkern u1="&#xdb;" u2="&#x2c;" k="37" />
<hkern u1="&#xdb;" u2="&#x29;" k="23" />
<hkern u1="&#xdc;" u2="&#xfb04;" k="16" />
<hkern u1="&#xdc;" u2="&#xfb03;" k="16" />
<hkern u1="&#xdc;" u2="&#xfb02;" k="16" />
<hkern u1="&#xdc;" u2="&#xfb01;" k="16" />
<hkern u1="&#xdc;" u2="&#x2026;" k="37" />
<hkern u1="&#xdc;" u2="&#x201e;" k="37" />
<hkern u1="&#xdc;" u2="&#x201a;" k="37" />
<hkern u1="&#xdc;" u2="&#xff;" k="12" />
<hkern u1="&#xdc;" u2="&#xfe;" k="27" />
<hkern u1="&#xdc;" u2="&#xfd;" k="12" />
<hkern u1="&#xdc;" u2="&#xfc;" k="37" />
<hkern u1="&#xdc;" u2="&#xfb;" k="37" />
<hkern u1="&#xdc;" u2="&#xfa;" k="37" />
<hkern u1="&#xdc;" u2="&#xf9;" k="37" />
<hkern u1="&#xdc;" u2="&#xf8;" k="31" />
<hkern u1="&#xdc;" u2="&#xf6;" k="31" />
<hkern u1="&#xdc;" u2="&#xf5;" k="31" />
<hkern u1="&#xdc;" u2="&#xf4;" k="31" />
<hkern u1="&#xdc;" u2="&#xf3;" k="31" />
<hkern u1="&#xdc;" u2="&#xf2;" k="31" />
<hkern u1="&#xdc;" u2="&#xf1;" k="37" />
<hkern u1="&#xdc;" u2="&#xf0;" k="31" />
<hkern u1="&#xdc;" u2="&#xef;" k="31" />
<hkern u1="&#xdc;" u2="&#xee;" k="31" />
<hkern u1="&#xdc;" u2="&#xed;" k="31" />
<hkern u1="&#xdc;" u2="&#xec;" k="31" />
<hkern u1="&#xdc;" u2="&#xeb;" k="31" />
<hkern u1="&#xdc;" u2="&#xea;" k="31" />
<hkern u1="&#xdc;" u2="&#xe9;" k="31" />
<hkern u1="&#xdc;" u2="&#xe8;" k="31" />
<hkern u1="&#xdc;" u2="&#xe7;" k="31" />
<hkern u1="&#xdc;" u2="&#xe6;" k="35" />
<hkern u1="&#xdc;" u2="&#xe5;" k="35" />
<hkern u1="&#xdc;" u2="&#xe4;" k="35" />
<hkern u1="&#xdc;" u2="&#xe3;" k="35" />
<hkern u1="&#xdc;" u2="&#xe2;" k="35" />
<hkern u1="&#xdc;" u2="&#xe1;" k="35" />
<hkern u1="&#xdc;" u2="&#xe0;" k="35" />
<hkern u1="&#xdc;" u2="&#xdf;" k="16" />
<hkern u1="&#xdc;" u2="&#xc6;" k="20" />
<hkern u1="&#xdc;" u2="&#xc5;" k="41" />
<hkern u1="&#xdc;" u2="&#xc4;" k="41" />
<hkern u1="&#xdc;" u2="&#xc3;" k="41" />
<hkern u1="&#xdc;" u2="&#xc2;" k="41" />
<hkern u1="&#xdc;" u2="&#xc1;" k="41" />
<hkern u1="&#xdc;" u2="&#xc0;" k="41" />
<hkern u1="&#xdc;" u2="y" k="12" />
<hkern u1="&#xdc;" u2="x" k="20" />
<hkern u1="&#xdc;" u2="v" k="16" />
<hkern u1="&#xdc;" u2="u" k="37" />
<hkern u1="&#xdc;" u2="t" k="20" />
<hkern u1="&#xdc;" u2="s" k="33" />
<hkern u1="&#xdc;" u2="r" k="37" />
<hkern u1="&#xdc;" u2="q" k="31" />
<hkern u1="&#xdc;" u2="p" k="37" />
<hkern u1="&#xdc;" u2="o" k="31" />
<hkern u1="&#xdc;" u2="n" k="37" />
<hkern u1="&#xdc;" u2="m" k="37" />
<hkern u1="&#xdc;" u2="k" k="27" />
<hkern u1="&#xdc;" u2="j" k="31" />
<hkern u1="&#xdc;" u2="i" k="31" />
<hkern u1="&#xdc;" u2="h" k="27" />
<hkern u1="&#xdc;" u2="g" k="31" />
<hkern u1="&#xdc;" u2="f" k="16" />
<hkern u1="&#xdc;" u2="e" k="31" />
<hkern u1="&#xdc;" u2="d" k="31" />
<hkern u1="&#xdc;" u2="c" k="31" />
<hkern u1="&#xdc;" u2="b" k="27" />
<hkern u1="&#xdc;" u2="a" k="35" />
<hkern u1="&#xdc;" u2="A" k="41" />
<hkern u1="&#xdc;" u2="&#x2f;" k="29" />
<hkern u1="&#xdc;" u2="&#x2e;" k="37" />
<hkern u1="&#xdc;" u2="&#x2c;" k="37" />
<hkern u1="&#xdc;" u2="&#x29;" k="23" />
<hkern u1="&#xdd;" u2="&#xfb04;" k="49" />
<hkern u1="&#xdd;" u2="&#xfb03;" k="49" />
<hkern u1="&#xdd;" u2="&#xfb02;" k="49" />
<hkern u1="&#xdd;" u2="&#xfb01;" k="49" />
<hkern u1="&#xdd;" u2="&#x2122;" k="-53" />
<hkern u1="&#xdd;" u2="&#x203a;" k="66" />
<hkern u1="&#xdd;" u2="&#x2039;" k="121" />
<hkern u1="&#xdd;" u2="&#x2026;" k="154" />
<hkern u1="&#xdd;" u2="&#x201e;" k="154" />
<hkern u1="&#xdd;" u2="&#x201a;" k="154" />
<hkern u1="&#xdd;" u2="&#x2014;" k="135" />
<hkern u1="&#xdd;" u2="&#x2013;" k="135" />
<hkern u1="&#xdd;" u2="&#xff;" k="70" />
<hkern u1="&#xdd;" u2="&#xfe;" k="16" />
<hkern u1="&#xdd;" u2="&#xfd;" k="70" />
<hkern u1="&#xdd;" u2="&#xfc;" k="139" />
<hkern u1="&#xdd;" u2="&#xfb;" k="139" />
<hkern u1="&#xdd;" u2="&#xfa;" k="139" />
<hkern u1="&#xdd;" u2="&#xf9;" k="139" />
<hkern u1="&#xdd;" u2="&#xf8;" k="178" />
<hkern u1="&#xdd;" u2="&#xf6;" k="178" />
<hkern u1="&#xdd;" u2="&#xf5;" k="178" />
<hkern u1="&#xdd;" u2="&#xf4;" k="178" />
<hkern u1="&#xdd;" u2="&#xf3;" k="178" />
<hkern u1="&#xdd;" u2="&#xf2;" k="178" />
<hkern u1="&#xdd;" u2="&#xf1;" k="143" />
<hkern u1="&#xdd;" u2="&#xf0;" k="178" />
<hkern u1="&#xdd;" u2="&#xef;" k="-47" />
<hkern u1="&#xdd;" u2="&#xee;" k="18" />
<hkern u1="&#xdd;" u2="&#xed;" k="18" />
<hkern u1="&#xdd;" u2="&#xec;" k="-29" />
<hkern u1="&#xdd;" u2="&#xeb;" k="178" />
<hkern u1="&#xdd;" u2="&#xea;" k="178" />
<hkern u1="&#xdd;" u2="&#xe9;" k="178" />
<hkern u1="&#xdd;" u2="&#xe8;" k="178" />
<hkern u1="&#xdd;" u2="&#xe7;" k="178" />
<hkern u1="&#xdd;" u2="&#xe6;" k="166" />
<hkern u1="&#xdd;" u2="&#xe5;" k="166" />
<hkern u1="&#xdd;" u2="&#xe4;" k="166" />
<hkern u1="&#xdd;" u2="&#xe3;" k="166" />
<hkern u1="&#xdd;" u2="&#xe2;" k="166" />
<hkern u1="&#xdd;" u2="&#xe1;" k="166" />
<hkern u1="&#xdd;" u2="&#xe0;" k="166" />
<hkern u1="&#xdd;" u2="&#xdf;" k="49" />
<hkern u1="&#xdd;" u2="&#xde;" k="10" />
<hkern u1="&#xdd;" u2="&#xdd;" k="-102" />
<hkern u1="&#xdd;" u2="&#xd8;" k="61" />
<hkern u1="&#xdd;" u2="&#xd6;" k="61" />
<hkern u1="&#xdd;" u2="&#xd5;" k="61" />
<hkern u1="&#xdd;" u2="&#xd4;" k="61" />
<hkern u1="&#xdd;" u2="&#xd3;" k="61" />
<hkern u1="&#xdd;" u2="&#xd2;" k="61" />
<hkern u1="&#xdd;" u2="&#xd1;" k="10" />
<hkern u1="&#xdd;" u2="&#xd0;" k="10" />
<hkern u1="&#xdd;" u2="&#xcf;" k="10" />
<hkern u1="&#xdd;" u2="&#xce;" k="10" />
<hkern u1="&#xdd;" u2="&#xcd;" k="10" />
<hkern u1="&#xdd;" u2="&#xcc;" k="10" />
<hkern u1="&#xdd;" u2="&#xcb;" k="10" />
<hkern u1="&#xdd;" u2="&#xca;" k="10" />
<hkern u1="&#xdd;" u2="&#xc9;" k="10" />
<hkern u1="&#xdd;" u2="&#xc8;" k="10" />
<hkern u1="&#xdd;" u2="&#xc7;" k="61" />
<hkern u1="&#xdd;" u2="&#xc6;" k="147" />
<hkern u1="&#xdd;" u2="&#xc5;" k="131" />
<hkern u1="&#xdd;" u2="&#xc4;" k="131" />
<hkern u1="&#xdd;" u2="&#xc3;" k="131" />
<hkern u1="&#xdd;" u2="&#xc2;" k="131" />
<hkern u1="&#xdd;" u2="&#xc1;" k="131" />
<hkern u1="&#xdd;" u2="&#xc0;" k="131" />
<hkern u1="&#xdd;" u2="&#xbb;" k="66" />
<hkern u1="&#xdd;" u2="&#xae;" k="47" />
<hkern u1="&#xdd;" u2="&#xab;" k="121" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-25" />
<hkern u1="&#xdd;" u2="y" k="70" />
<hkern u1="&#xdd;" u2="x" k="88" />
<hkern u1="&#xdd;" u2="v" k="72" />
<hkern u1="&#xdd;" u2="u" k="139" />
<hkern u1="&#xdd;" u2="t" k="113" />
<hkern u1="&#xdd;" u2="s" k="174" />
<hkern u1="&#xdd;" u2="r" k="143" />
<hkern u1="&#xdd;" u2="q" k="178" />
<hkern u1="&#xdd;" u2="p" k="143" />
<hkern u1="&#xdd;" u2="o" k="178" />
<hkern u1="&#xdd;" u2="n" k="143" />
<hkern u1="&#xdd;" u2="m" k="143" />
<hkern u1="&#xdd;" u2="k" k="16" />
<hkern u1="&#xdd;" u2="j" k="18" />
<hkern u1="&#xdd;" u2="i" k="18" />
<hkern u1="&#xdd;" u2="h" k="16" />
<hkern u1="&#xdd;" u2="g" k="178" />
<hkern u1="&#xdd;" u2="f" k="49" />
<hkern u1="&#xdd;" u2="e" k="178" />
<hkern u1="&#xdd;" u2="d" k="178" />
<hkern u1="&#xdd;" u2="c" k="178" />
<hkern u1="&#xdd;" u2="b" k="16" />
<hkern u1="&#xdd;" u2="a" k="166" />
<hkern u1="&#xdd;" u2="]" k="-20" />
<hkern u1="&#xdd;" u2="Y" k="-102" />
<hkern u1="&#xdd;" u2="X" k="-92" />
<hkern u1="&#xdd;" u2="V" k="-92" />
<hkern u1="&#xdd;" u2="T" k="-86" />
<hkern u1="&#xdd;" u2="S" k="18" />
<hkern u1="&#xdd;" u2="R" k="10" />
<hkern u1="&#xdd;" u2="Q" k="61" />
<hkern u1="&#xdd;" u2="P" k="10" />
<hkern u1="&#xdd;" u2="O" k="61" />
<hkern u1="&#xdd;" u2="N" k="10" />
<hkern u1="&#xdd;" u2="M" k="10" />
<hkern u1="&#xdd;" u2="L" k="10" />
<hkern u1="&#xdd;" u2="K" k="10" />
<hkern u1="&#xdd;" u2="I" k="10" />
<hkern u1="&#xdd;" u2="H" k="10" />
<hkern u1="&#xdd;" u2="G" k="61" />
<hkern u1="&#xdd;" u2="F" k="10" />
<hkern u1="&#xdd;" u2="E" k="10" />
<hkern u1="&#xdd;" u2="D" k="10" />
<hkern u1="&#xdd;" u2="C" k="61" />
<hkern u1="&#xdd;" u2="B" k="10" />
<hkern u1="&#xdd;" u2="A" k="131" />
<hkern u1="&#xdd;" u2="&#x40;" k="72" />
<hkern u1="&#xdd;" u2="&#x3b;" k="53" />
<hkern u1="&#xdd;" u2="&#x3a;" k="53" />
<hkern u1="&#xdd;" u2="&#x2f;" k="90" />
<hkern u1="&#xdd;" u2="&#x2e;" k="154" />
<hkern u1="&#xdd;" u2="&#x2d;" k="135" />
<hkern u1="&#xdd;" u2="&#x2c;" k="154" />
<hkern u1="&#xdd;" u2="&#x26;" k="53" />
<hkern u1="&#xde;" u2="&#x2026;" k="72" />
<hkern u1="&#xde;" u2="&#x201e;" k="72" />
<hkern u1="&#xde;" u2="&#x201a;" k="72" />
<hkern u1="&#xde;" u2="&#xfe;" k="10" />
<hkern u1="&#xde;" u2="&#xf1;" k="10" />
<hkern u1="&#xde;" u2="&#xef;" k="10" />
<hkern u1="&#xde;" u2="&#xee;" k="10" />
<hkern u1="&#xde;" u2="&#xed;" k="10" />
<hkern u1="&#xde;" u2="&#xec;" k="10" />
<hkern u1="&#xde;" u2="&#xe6;" k="16" />
<hkern u1="&#xde;" u2="&#xe5;" k="16" />
<hkern u1="&#xde;" u2="&#xe4;" k="16" />
<hkern u1="&#xde;" u2="&#xe3;" k="16" />
<hkern u1="&#xde;" u2="&#xe2;" k="16" />
<hkern u1="&#xde;" u2="&#xe1;" k="16" />
<hkern u1="&#xde;" u2="&#xe0;" k="16" />
<hkern u1="&#xde;" u2="&#xdd;" k="70" />
<hkern u1="&#xde;" u2="&#xc6;" k="121" />
<hkern u1="&#xde;" u2="&#xc5;" k="35" />
<hkern u1="&#xde;" u2="&#xc4;" k="35" />
<hkern u1="&#xde;" u2="&#xc3;" k="35" />
<hkern u1="&#xde;" u2="&#xc2;" k="35" />
<hkern u1="&#xde;" u2="&#xc1;" k="35" />
<hkern u1="&#xde;" u2="&#xc0;" k="35" />
<hkern u1="&#xde;" u2="&#x7d;" k="31" />
<hkern u1="&#xde;" u2="x" k="20" />
<hkern u1="&#xde;" u2="r" k="10" />
<hkern u1="&#xde;" u2="p" k="10" />
<hkern u1="&#xde;" u2="n" k="10" />
<hkern u1="&#xde;" u2="m" k="10" />
<hkern u1="&#xde;" u2="k" k="10" />
<hkern u1="&#xde;" u2="j" k="10" />
<hkern u1="&#xde;" u2="i" k="10" />
<hkern u1="&#xde;" u2="h" k="10" />
<hkern u1="&#xde;" u2="b" k="10" />
<hkern u1="&#xde;" u2="a" k="16" />
<hkern u1="&#xde;" u2="]" k="35" />
<hkern u1="&#xde;" u2="Y" k="70" />
<hkern u1="&#xde;" u2="X" k="90" />
<hkern u1="&#xde;" u2="V" k="23" />
<hkern u1="&#xde;" u2="T" k="92" />
<hkern u1="&#xde;" u2="A" k="35" />
<hkern u1="&#xde;" u2="&#x2f;" k="20" />
<hkern u1="&#xde;" u2="&#x2e;" k="72" />
<hkern u1="&#xde;" u2="&#x2c;" k="72" />
<hkern u1="&#xde;" u2="&#x29;" k="41" />
<hkern u1="&#xdf;" u2="&#x2122;" k="18" />
<hkern u1="&#xdf;" u2="&#x7d;" k="27" />
<hkern u1="&#xdf;" u2="x" k="12" />
<hkern u1="&#xdf;" u2="v" k="12" />
<hkern u1="&#xdf;" u2="t" k="8" />
<hkern u1="&#xdf;" u2="]" k="27" />
<hkern u1="&#xdf;" u2="&#x29;" k="35" />
<hkern u1="&#xe0;" u2="&#xfb04;" k="12" />
<hkern u1="&#xe0;" u2="&#xfb03;" k="12" />
<hkern u1="&#xe0;" u2="&#xfb02;" k="12" />
<hkern u1="&#xe0;" u2="&#xfb01;" k="12" />
<hkern u1="&#xe0;" u2="&#x2122;" k="51" />
<hkern u1="&#xe0;" u2="&#x201d;" k="29" />
<hkern u1="&#xe0;" u2="&#x201c;" k="27" />
<hkern u1="&#xe0;" u2="&#x2019;" k="29" />
<hkern u1="&#xe0;" u2="&#x2018;" k="27" />
<hkern u1="&#xe0;" u2="&#xff;" k="14" />
<hkern u1="&#xe0;" u2="&#xfd;" k="14" />
<hkern u1="&#xe0;" u2="&#xfc;" k="8" />
<hkern u1="&#xe0;" u2="&#xfb;" k="8" />
<hkern u1="&#xe0;" u2="&#xfa;" k="8" />
<hkern u1="&#xe0;" u2="&#xf9;" k="8" />
<hkern u1="&#xe0;" u2="&#xdf;" k="12" />
<hkern u1="&#xe0;" u2="&#xde;" k="29" />
<hkern u1="&#xe0;" u2="&#xdd;" k="176" />
<hkern u1="&#xe0;" u2="&#xdc;" k="45" />
<hkern u1="&#xe0;" u2="&#xdb;" k="45" />
<hkern u1="&#xe0;" u2="&#xda;" k="45" />
<hkern u1="&#xe0;" u2="&#xd9;" k="45" />
<hkern u1="&#xe0;" u2="&#xd8;" k="20" />
<hkern u1="&#xe0;" u2="&#xd6;" k="20" />
<hkern u1="&#xe0;" u2="&#xd5;" k="20" />
<hkern u1="&#xe0;" u2="&#xd4;" k="20" />
<hkern u1="&#xe0;" u2="&#xd3;" k="20" />
<hkern u1="&#xe0;" u2="&#xd2;" k="20" />
<hkern u1="&#xe0;" u2="&#xd1;" k="29" />
<hkern u1="&#xe0;" u2="&#xd0;" k="29" />
<hkern u1="&#xe0;" u2="&#xcf;" k="29" />
<hkern u1="&#xe0;" u2="&#xce;" k="29" />
<hkern u1="&#xe0;" u2="&#xcd;" k="29" />
<hkern u1="&#xe0;" u2="&#xcc;" k="29" />
<hkern u1="&#xe0;" u2="&#xcb;" k="29" />
<hkern u1="&#xe0;" u2="&#xca;" k="29" />
<hkern u1="&#xe0;" u2="&#xc9;" k="29" />
<hkern u1="&#xe0;" u2="&#xc8;" k="29" />
<hkern u1="&#xe0;" u2="&#xc7;" k="20" />
<hkern u1="&#xe0;" u2="&#xc5;" k="10" />
<hkern u1="&#xe0;" u2="&#xc4;" k="10" />
<hkern u1="&#xe0;" u2="&#xc3;" k="10" />
<hkern u1="&#xe0;" u2="&#xc2;" k="10" />
<hkern u1="&#xe0;" u2="&#xc1;" k="10" />
<hkern u1="&#xe0;" u2="&#xc0;" k="10" />
<hkern u1="&#xe0;" u2="&#x7d;" k="41" />
<hkern u1="&#xe0;" u2="y" k="14" />
<hkern u1="&#xe0;" u2="v" k="20" />
<hkern u1="&#xe0;" u2="u" k="8" />
<hkern u1="&#xe0;" u2="t" k="10" />
<hkern u1="&#xe0;" u2="f" k="12" />
<hkern u1="&#xe0;" u2="]" k="47" />
<hkern u1="&#xe0;" u2="\" k="47" />
<hkern u1="&#xe0;" u2="Y" k="176" />
<hkern u1="&#xe0;" u2="X" k="12" />
<hkern u1="&#xe0;" u2="V" k="109" />
<hkern u1="&#xe0;" u2="U" k="45" />
<hkern u1="&#xe0;" u2="T" k="186" />
<hkern u1="&#xe0;" u2="S" k="25" />
<hkern u1="&#xe0;" u2="R" k="29" />
<hkern u1="&#xe0;" u2="Q" k="20" />
<hkern u1="&#xe0;" u2="P" k="29" />
<hkern u1="&#xe0;" u2="O" k="20" />
<hkern u1="&#xe0;" u2="N" k="29" />
<hkern u1="&#xe0;" u2="M" k="29" />
<hkern u1="&#xe0;" u2="L" k="29" />
<hkern u1="&#xe0;" u2="K" k="29" />
<hkern u1="&#xe0;" u2="I" k="29" />
<hkern u1="&#xe0;" u2="H" k="29" />
<hkern u1="&#xe0;" u2="G" k="20" />
<hkern u1="&#xe0;" u2="F" k="29" />
<hkern u1="&#xe0;" u2="E" k="29" />
<hkern u1="&#xe0;" u2="D" k="29" />
<hkern u1="&#xe0;" u2="C" k="20" />
<hkern u1="&#xe0;" u2="B" k="29" />
<hkern u1="&#xe0;" u2="A" k="10" />
<hkern u1="&#xe0;" u2="&#x3f;" k="39" />
<hkern u1="&#xe0;" u2="&#x2a;" k="29" />
<hkern u1="&#xe0;" u2="&#x29;" k="41" />
<hkern u1="&#xe0;" u2="&#x27;" k="29" />
<hkern u1="&#xe0;" u2="&#x26;" k="12" />
<hkern u1="&#xe0;" u2="&#x22;" k="29" />
<hkern u1="&#xe1;" u2="&#xfb04;" k="12" />
<hkern u1="&#xe1;" u2="&#xfb03;" k="12" />
<hkern u1="&#xe1;" u2="&#xfb02;" k="12" />
<hkern u1="&#xe1;" u2="&#xfb01;" k="12" />
<hkern u1="&#xe1;" u2="&#x2122;" k="51" />
<hkern u1="&#xe1;" u2="&#x201d;" k="29" />
<hkern u1="&#xe1;" u2="&#x201c;" k="27" />
<hkern u1="&#xe1;" u2="&#x2019;" k="29" />
<hkern u1="&#xe1;" u2="&#x2018;" k="27" />
<hkern u1="&#xe1;" u2="&#xff;" k="14" />
<hkern u1="&#xe1;" u2="&#xfd;" k="14" />
<hkern u1="&#xe1;" u2="&#xfc;" k="8" />
<hkern u1="&#xe1;" u2="&#xfb;" k="8" />
<hkern u1="&#xe1;" u2="&#xfa;" k="8" />
<hkern u1="&#xe1;" u2="&#xf9;" k="8" />
<hkern u1="&#xe1;" u2="&#xdf;" k="12" />
<hkern u1="&#xe1;" u2="&#xde;" k="29" />
<hkern u1="&#xe1;" u2="&#xdd;" k="176" />
<hkern u1="&#xe1;" u2="&#xdc;" k="45" />
<hkern u1="&#xe1;" u2="&#xdb;" k="45" />
<hkern u1="&#xe1;" u2="&#xda;" k="45" />
<hkern u1="&#xe1;" u2="&#xd9;" k="45" />
<hkern u1="&#xe1;" u2="&#xd8;" k="20" />
<hkern u1="&#xe1;" u2="&#xd6;" k="20" />
<hkern u1="&#xe1;" u2="&#xd5;" k="20" />
<hkern u1="&#xe1;" u2="&#xd4;" k="20" />
<hkern u1="&#xe1;" u2="&#xd3;" k="20" />
<hkern u1="&#xe1;" u2="&#xd2;" k="20" />
<hkern u1="&#xe1;" u2="&#xd1;" k="29" />
<hkern u1="&#xe1;" u2="&#xd0;" k="29" />
<hkern u1="&#xe1;" u2="&#xcf;" k="29" />
<hkern u1="&#xe1;" u2="&#xce;" k="29" />
<hkern u1="&#xe1;" u2="&#xcd;" k="29" />
<hkern u1="&#xe1;" u2="&#xcc;" k="29" />
<hkern u1="&#xe1;" u2="&#xcb;" k="29" />
<hkern u1="&#xe1;" u2="&#xca;" k="29" />
<hkern u1="&#xe1;" u2="&#xc9;" k="29" />
<hkern u1="&#xe1;" u2="&#xc8;" k="29" />
<hkern u1="&#xe1;" u2="&#xc7;" k="20" />
<hkern u1="&#xe1;" u2="&#xc5;" k="10" />
<hkern u1="&#xe1;" u2="&#xc4;" k="10" />
<hkern u1="&#xe1;" u2="&#xc3;" k="10" />
<hkern u1="&#xe1;" u2="&#xc2;" k="10" />
<hkern u1="&#xe1;" u2="&#xc1;" k="10" />
<hkern u1="&#xe1;" u2="&#xc0;" k="10" />
<hkern u1="&#xe1;" u2="&#x7d;" k="41" />
<hkern u1="&#xe1;" u2="y" k="14" />
<hkern u1="&#xe1;" u2="v" k="20" />
<hkern u1="&#xe1;" u2="u" k="8" />
<hkern u1="&#xe1;" u2="t" k="10" />
<hkern u1="&#xe1;" u2="f" k="12" />
<hkern u1="&#xe1;" u2="]" k="47" />
<hkern u1="&#xe1;" u2="\" k="47" />
<hkern u1="&#xe1;" u2="Y" k="176" />
<hkern u1="&#xe1;" u2="X" k="12" />
<hkern u1="&#xe1;" u2="V" k="109" />
<hkern u1="&#xe1;" u2="U" k="45" />
<hkern u1="&#xe1;" u2="T" k="186" />
<hkern u1="&#xe1;" u2="S" k="25" />
<hkern u1="&#xe1;" u2="R" k="29" />
<hkern u1="&#xe1;" u2="Q" k="20" />
<hkern u1="&#xe1;" u2="P" k="29" />
<hkern u1="&#xe1;" u2="O" k="20" />
<hkern u1="&#xe1;" u2="N" k="29" />
<hkern u1="&#xe1;" u2="M" k="29" />
<hkern u1="&#xe1;" u2="L" k="29" />
<hkern u1="&#xe1;" u2="K" k="29" />
<hkern u1="&#xe1;" u2="I" k="29" />
<hkern u1="&#xe1;" u2="H" k="29" />
<hkern u1="&#xe1;" u2="G" k="20" />
<hkern u1="&#xe1;" u2="F" k="29" />
<hkern u1="&#xe1;" u2="E" k="29" />
<hkern u1="&#xe1;" u2="D" k="29" />
<hkern u1="&#xe1;" u2="C" k="20" />
<hkern u1="&#xe1;" u2="B" k="29" />
<hkern u1="&#xe1;" u2="A" k="10" />
<hkern u1="&#xe1;" u2="&#x3f;" k="39" />
<hkern u1="&#xe1;" u2="&#x2a;" k="29" />
<hkern u1="&#xe1;" u2="&#x29;" k="41" />
<hkern u1="&#xe1;" u2="&#x27;" k="29" />
<hkern u1="&#xe1;" u2="&#x26;" k="12" />
<hkern u1="&#xe1;" u2="&#x22;" k="29" />
<hkern u1="&#xe2;" u2="&#xfb04;" k="12" />
<hkern u1="&#xe2;" u2="&#xfb03;" k="12" />
<hkern u1="&#xe2;" u2="&#xfb02;" k="12" />
<hkern u1="&#xe2;" u2="&#xfb01;" k="12" />
<hkern u1="&#xe2;" u2="&#x2122;" k="51" />
<hkern u1="&#xe2;" u2="&#x201d;" k="29" />
<hkern u1="&#xe2;" u2="&#x201c;" k="27" />
<hkern u1="&#xe2;" u2="&#x2019;" k="29" />
<hkern u1="&#xe2;" u2="&#x2018;" k="27" />
<hkern u1="&#xe2;" u2="&#xff;" k="14" />
<hkern u1="&#xe2;" u2="&#xfd;" k="14" />
<hkern u1="&#xe2;" u2="&#xfc;" k="8" />
<hkern u1="&#xe2;" u2="&#xfb;" k="8" />
<hkern u1="&#xe2;" u2="&#xfa;" k="8" />
<hkern u1="&#xe2;" u2="&#xf9;" k="8" />
<hkern u1="&#xe2;" u2="&#xdf;" k="12" />
<hkern u1="&#xe2;" u2="&#xde;" k="29" />
<hkern u1="&#xe2;" u2="&#xdd;" k="176" />
<hkern u1="&#xe2;" u2="&#xdc;" k="45" />
<hkern u1="&#xe2;" u2="&#xdb;" k="45" />
<hkern u1="&#xe2;" u2="&#xda;" k="45" />
<hkern u1="&#xe2;" u2="&#xd9;" k="45" />
<hkern u1="&#xe2;" u2="&#xd8;" k="20" />
<hkern u1="&#xe2;" u2="&#xd6;" k="20" />
<hkern u1="&#xe2;" u2="&#xd5;" k="20" />
<hkern u1="&#xe2;" u2="&#xd4;" k="20" />
<hkern u1="&#xe2;" u2="&#xd3;" k="20" />
<hkern u1="&#xe2;" u2="&#xd2;" k="20" />
<hkern u1="&#xe2;" u2="&#xd1;" k="29" />
<hkern u1="&#xe2;" u2="&#xd0;" k="29" />
<hkern u1="&#xe2;" u2="&#xcf;" k="29" />
<hkern u1="&#xe2;" u2="&#xce;" k="29" />
<hkern u1="&#xe2;" u2="&#xcd;" k="29" />
<hkern u1="&#xe2;" u2="&#xcc;" k="29" />
<hkern u1="&#xe2;" u2="&#xcb;" k="29" />
<hkern u1="&#xe2;" u2="&#xca;" k="29" />
<hkern u1="&#xe2;" u2="&#xc9;" k="29" />
<hkern u1="&#xe2;" u2="&#xc8;" k="29" />
<hkern u1="&#xe2;" u2="&#xc7;" k="20" />
<hkern u1="&#xe2;" u2="&#xc5;" k="10" />
<hkern u1="&#xe2;" u2="&#xc4;" k="10" />
<hkern u1="&#xe2;" u2="&#xc3;" k="10" />
<hkern u1="&#xe2;" u2="&#xc2;" k="10" />
<hkern u1="&#xe2;" u2="&#xc1;" k="10" />
<hkern u1="&#xe2;" u2="&#xc0;" k="10" />
<hkern u1="&#xe2;" u2="&#x7d;" k="41" />
<hkern u1="&#xe2;" u2="y" k="14" />
<hkern u1="&#xe2;" u2="v" k="20" />
<hkern u1="&#xe2;" u2="u" k="8" />
<hkern u1="&#xe2;" u2="t" k="10" />
<hkern u1="&#xe2;" u2="f" k="12" />
<hkern u1="&#xe2;" u2="]" k="47" />
<hkern u1="&#xe2;" u2="\" k="47" />
<hkern u1="&#xe2;" u2="Y" k="176" />
<hkern u1="&#xe2;" u2="X" k="12" />
<hkern u1="&#xe2;" u2="V" k="109" />
<hkern u1="&#xe2;" u2="U" k="45" />
<hkern u1="&#xe2;" u2="T" k="186" />
<hkern u1="&#xe2;" u2="S" k="25" />
<hkern u1="&#xe2;" u2="R" k="29" />
<hkern u1="&#xe2;" u2="Q" k="20" />
<hkern u1="&#xe2;" u2="P" k="29" />
<hkern u1="&#xe2;" u2="O" k="20" />
<hkern u1="&#xe2;" u2="N" k="29" />
<hkern u1="&#xe2;" u2="M" k="29" />
<hkern u1="&#xe2;" u2="L" k="29" />
<hkern u1="&#xe2;" u2="K" k="29" />
<hkern u1="&#xe2;" u2="I" k="29" />
<hkern u1="&#xe2;" u2="H" k="29" />
<hkern u1="&#xe2;" u2="G" k="20" />
<hkern u1="&#xe2;" u2="F" k="29" />
<hkern u1="&#xe2;" u2="E" k="29" />
<hkern u1="&#xe2;" u2="D" k="29" />
<hkern u1="&#xe2;" u2="C" k="20" />
<hkern u1="&#xe2;" u2="B" k="29" />
<hkern u1="&#xe2;" u2="A" k="10" />
<hkern u1="&#xe2;" u2="&#x3f;" k="39" />
<hkern u1="&#xe2;" u2="&#x2a;" k="29" />
<hkern u1="&#xe2;" u2="&#x29;" k="41" />
<hkern u1="&#xe2;" u2="&#x27;" k="29" />
<hkern u1="&#xe2;" u2="&#x26;" k="12" />
<hkern u1="&#xe2;" u2="&#x22;" k="29" />
<hkern u1="&#xe3;" u2="&#xfb04;" k="12" />
<hkern u1="&#xe3;" u2="&#xfb03;" k="12" />
<hkern u1="&#xe3;" u2="&#xfb02;" k="12" />
<hkern u1="&#xe3;" u2="&#xfb01;" k="12" />
<hkern u1="&#xe3;" u2="&#x2122;" k="51" />
<hkern u1="&#xe3;" u2="&#x201d;" k="29" />
<hkern u1="&#xe3;" u2="&#x201c;" k="27" />
<hkern u1="&#xe3;" u2="&#x2019;" k="29" />
<hkern u1="&#xe3;" u2="&#x2018;" k="27" />
<hkern u1="&#xe3;" u2="&#xff;" k="14" />
<hkern u1="&#xe3;" u2="&#xfd;" k="14" />
<hkern u1="&#xe3;" u2="&#xfc;" k="8" />
<hkern u1="&#xe3;" u2="&#xfb;" k="8" />
<hkern u1="&#xe3;" u2="&#xfa;" k="8" />
<hkern u1="&#xe3;" u2="&#xf9;" k="8" />
<hkern u1="&#xe3;" u2="&#xdf;" k="12" />
<hkern u1="&#xe3;" u2="&#xde;" k="29" />
<hkern u1="&#xe3;" u2="&#xdd;" k="176" />
<hkern u1="&#xe3;" u2="&#xdc;" k="45" />
<hkern u1="&#xe3;" u2="&#xdb;" k="45" />
<hkern u1="&#xe3;" u2="&#xda;" k="45" />
<hkern u1="&#xe3;" u2="&#xd9;" k="45" />
<hkern u1="&#xe3;" u2="&#xd8;" k="20" />
<hkern u1="&#xe3;" u2="&#xd6;" k="20" />
<hkern u1="&#xe3;" u2="&#xd5;" k="20" />
<hkern u1="&#xe3;" u2="&#xd4;" k="20" />
<hkern u1="&#xe3;" u2="&#xd3;" k="20" />
<hkern u1="&#xe3;" u2="&#xd2;" k="20" />
<hkern u1="&#xe3;" u2="&#xd1;" k="29" />
<hkern u1="&#xe3;" u2="&#xd0;" k="29" />
<hkern u1="&#xe3;" u2="&#xcf;" k="29" />
<hkern u1="&#xe3;" u2="&#xce;" k="29" />
<hkern u1="&#xe3;" u2="&#xcd;" k="29" />
<hkern u1="&#xe3;" u2="&#xcc;" k="29" />
<hkern u1="&#xe3;" u2="&#xcb;" k="29" />
<hkern u1="&#xe3;" u2="&#xca;" k="29" />
<hkern u1="&#xe3;" u2="&#xc9;" k="29" />
<hkern u1="&#xe3;" u2="&#xc8;" k="29" />
<hkern u1="&#xe3;" u2="&#xc7;" k="20" />
<hkern u1="&#xe3;" u2="&#xc5;" k="10" />
<hkern u1="&#xe3;" u2="&#xc4;" k="10" />
<hkern u1="&#xe3;" u2="&#xc3;" k="10" />
<hkern u1="&#xe3;" u2="&#xc2;" k="10" />
<hkern u1="&#xe3;" u2="&#xc1;" k="10" />
<hkern u1="&#xe3;" u2="&#xc0;" k="10" />
<hkern u1="&#xe3;" u2="&#x7d;" k="41" />
<hkern u1="&#xe3;" u2="y" k="14" />
<hkern u1="&#xe3;" u2="v" k="20" />
<hkern u1="&#xe3;" u2="u" k="8" />
<hkern u1="&#xe3;" u2="t" k="10" />
<hkern u1="&#xe3;" u2="f" k="12" />
<hkern u1="&#xe3;" u2="]" k="47" />
<hkern u1="&#xe3;" u2="\" k="47" />
<hkern u1="&#xe3;" u2="Y" k="176" />
<hkern u1="&#xe3;" u2="X" k="12" />
<hkern u1="&#xe3;" u2="V" k="109" />
<hkern u1="&#xe3;" u2="U" k="45" />
<hkern u1="&#xe3;" u2="T" k="186" />
<hkern u1="&#xe3;" u2="S" k="25" />
<hkern u1="&#xe3;" u2="R" k="29" />
<hkern u1="&#xe3;" u2="Q" k="20" />
<hkern u1="&#xe3;" u2="P" k="29" />
<hkern u1="&#xe3;" u2="O" k="20" />
<hkern u1="&#xe3;" u2="N" k="29" />
<hkern u1="&#xe3;" u2="M" k="29" />
<hkern u1="&#xe3;" u2="L" k="29" />
<hkern u1="&#xe3;" u2="K" k="29" />
<hkern u1="&#xe3;" u2="I" k="29" />
<hkern u1="&#xe3;" u2="H" k="29" />
<hkern u1="&#xe3;" u2="G" k="20" />
<hkern u1="&#xe3;" u2="F" k="29" />
<hkern u1="&#xe3;" u2="E" k="29" />
<hkern u1="&#xe3;" u2="D" k="29" />
<hkern u1="&#xe3;" u2="C" k="20" />
<hkern u1="&#xe3;" u2="B" k="29" />
<hkern u1="&#xe3;" u2="A" k="10" />
<hkern u1="&#xe3;" u2="&#x3f;" k="39" />
<hkern u1="&#xe3;" u2="&#x2a;" k="29" />
<hkern u1="&#xe3;" u2="&#x29;" k="41" />
<hkern u1="&#xe3;" u2="&#x27;" k="29" />
<hkern u1="&#xe3;" u2="&#x26;" k="12" />
<hkern u1="&#xe3;" u2="&#x22;" k="29" />
<hkern u1="&#xe4;" u2="&#xfb04;" k="12" />
<hkern u1="&#xe4;" u2="&#xfb03;" k="12" />
<hkern u1="&#xe4;" u2="&#xfb02;" k="12" />
<hkern u1="&#xe4;" u2="&#xfb01;" k="12" />
<hkern u1="&#xe4;" u2="&#x2122;" k="51" />
<hkern u1="&#xe4;" u2="&#x201d;" k="29" />
<hkern u1="&#xe4;" u2="&#x201c;" k="27" />
<hkern u1="&#xe4;" u2="&#x2019;" k="29" />
<hkern u1="&#xe4;" u2="&#x2018;" k="27" />
<hkern u1="&#xe4;" u2="&#xff;" k="14" />
<hkern u1="&#xe4;" u2="&#xfd;" k="14" />
<hkern u1="&#xe4;" u2="&#xfc;" k="8" />
<hkern u1="&#xe4;" u2="&#xfb;" k="8" />
<hkern u1="&#xe4;" u2="&#xfa;" k="8" />
<hkern u1="&#xe4;" u2="&#xf9;" k="8" />
<hkern u1="&#xe4;" u2="&#xdf;" k="12" />
<hkern u1="&#xe4;" u2="&#xde;" k="29" />
<hkern u1="&#xe4;" u2="&#xdd;" k="176" />
<hkern u1="&#xe4;" u2="&#xdc;" k="45" />
<hkern u1="&#xe4;" u2="&#xdb;" k="45" />
<hkern u1="&#xe4;" u2="&#xda;" k="45" />
<hkern u1="&#xe4;" u2="&#xd9;" k="45" />
<hkern u1="&#xe4;" u2="&#xd8;" k="20" />
<hkern u1="&#xe4;" u2="&#xd6;" k="20" />
<hkern u1="&#xe4;" u2="&#xd5;" k="20" />
<hkern u1="&#xe4;" u2="&#xd4;" k="20" />
<hkern u1="&#xe4;" u2="&#xd3;" k="20" />
<hkern u1="&#xe4;" u2="&#xd2;" k="20" />
<hkern u1="&#xe4;" u2="&#xd1;" k="29" />
<hkern u1="&#xe4;" u2="&#xd0;" k="29" />
<hkern u1="&#xe4;" u2="&#xcf;" k="29" />
<hkern u1="&#xe4;" u2="&#xce;" k="29" />
<hkern u1="&#xe4;" u2="&#xcd;" k="29" />
<hkern u1="&#xe4;" u2="&#xcc;" k="29" />
<hkern u1="&#xe4;" u2="&#xcb;" k="29" />
<hkern u1="&#xe4;" u2="&#xca;" k="29" />
<hkern u1="&#xe4;" u2="&#xc9;" k="29" />
<hkern u1="&#xe4;" u2="&#xc8;" k="29" />
<hkern u1="&#xe4;" u2="&#xc7;" k="20" />
<hkern u1="&#xe4;" u2="&#xc5;" k="10" />
<hkern u1="&#xe4;" u2="&#xc4;" k="10" />
<hkern u1="&#xe4;" u2="&#xc3;" k="10" />
<hkern u1="&#xe4;" u2="&#xc2;" k="10" />
<hkern u1="&#xe4;" u2="&#xc1;" k="10" />
<hkern u1="&#xe4;" u2="&#xc0;" k="10" />
<hkern u1="&#xe4;" u2="&#x7d;" k="41" />
<hkern u1="&#xe4;" u2="y" k="14" />
<hkern u1="&#xe4;" u2="v" k="20" />
<hkern u1="&#xe4;" u2="u" k="8" />
<hkern u1="&#xe4;" u2="t" k="10" />
<hkern u1="&#xe4;" u2="f" k="12" />
<hkern u1="&#xe4;" u2="]" k="47" />
<hkern u1="&#xe4;" u2="\" k="47" />
<hkern u1="&#xe4;" u2="Y" k="176" />
<hkern u1="&#xe4;" u2="X" k="12" />
<hkern u1="&#xe4;" u2="V" k="109" />
<hkern u1="&#xe4;" u2="U" k="45" />
<hkern u1="&#xe4;" u2="T" k="186" />
<hkern u1="&#xe4;" u2="S" k="25" />
<hkern u1="&#xe4;" u2="R" k="29" />
<hkern u1="&#xe4;" u2="Q" k="20" />
<hkern u1="&#xe4;" u2="P" k="29" />
<hkern u1="&#xe4;" u2="O" k="20" />
<hkern u1="&#xe4;" u2="N" k="29" />
<hkern u1="&#xe4;" u2="M" k="29" />
<hkern u1="&#xe4;" u2="L" k="29" />
<hkern u1="&#xe4;" u2="K" k="29" />
<hkern u1="&#xe4;" u2="I" k="29" />
<hkern u1="&#xe4;" u2="H" k="29" />
<hkern u1="&#xe4;" u2="G" k="20" />
<hkern u1="&#xe4;" u2="F" k="29" />
<hkern u1="&#xe4;" u2="E" k="29" />
<hkern u1="&#xe4;" u2="D" k="29" />
<hkern u1="&#xe4;" u2="C" k="20" />
<hkern u1="&#xe4;" u2="B" k="29" />
<hkern u1="&#xe4;" u2="A" k="10" />
<hkern u1="&#xe4;" u2="&#x3f;" k="39" />
<hkern u1="&#xe4;" u2="&#x2a;" k="29" />
<hkern u1="&#xe4;" u2="&#x29;" k="41" />
<hkern u1="&#xe4;" u2="&#x27;" k="29" />
<hkern u1="&#xe4;" u2="&#x26;" k="12" />
<hkern u1="&#xe4;" u2="&#x22;" k="29" />
<hkern u1="&#xe5;" u2="&#xfb04;" k="12" />
<hkern u1="&#xe5;" u2="&#xfb03;" k="12" />
<hkern u1="&#xe5;" u2="&#xfb02;" k="12" />
<hkern u1="&#xe5;" u2="&#xfb01;" k="12" />
<hkern u1="&#xe5;" u2="&#x2122;" k="51" />
<hkern u1="&#xe5;" u2="&#x201d;" k="29" />
<hkern u1="&#xe5;" u2="&#x201c;" k="27" />
<hkern u1="&#xe5;" u2="&#x2019;" k="29" />
<hkern u1="&#xe5;" u2="&#x2018;" k="27" />
<hkern u1="&#xe5;" u2="&#xff;" k="14" />
<hkern u1="&#xe5;" u2="&#xfd;" k="14" />
<hkern u1="&#xe5;" u2="&#xfc;" k="8" />
<hkern u1="&#xe5;" u2="&#xfb;" k="8" />
<hkern u1="&#xe5;" u2="&#xfa;" k="8" />
<hkern u1="&#xe5;" u2="&#xf9;" k="8" />
<hkern u1="&#xe5;" u2="&#xdf;" k="12" />
<hkern u1="&#xe5;" u2="&#xde;" k="29" />
<hkern u1="&#xe5;" u2="&#xdd;" k="176" />
<hkern u1="&#xe5;" u2="&#xdc;" k="45" />
<hkern u1="&#xe5;" u2="&#xdb;" k="45" />
<hkern u1="&#xe5;" u2="&#xda;" k="45" />
<hkern u1="&#xe5;" u2="&#xd9;" k="45" />
<hkern u1="&#xe5;" u2="&#xd8;" k="20" />
<hkern u1="&#xe5;" u2="&#xd6;" k="20" />
<hkern u1="&#xe5;" u2="&#xd5;" k="20" />
<hkern u1="&#xe5;" u2="&#xd4;" k="20" />
<hkern u1="&#xe5;" u2="&#xd3;" k="20" />
<hkern u1="&#xe5;" u2="&#xd2;" k="20" />
<hkern u1="&#xe5;" u2="&#xd1;" k="29" />
<hkern u1="&#xe5;" u2="&#xd0;" k="29" />
<hkern u1="&#xe5;" u2="&#xcf;" k="29" />
<hkern u1="&#xe5;" u2="&#xce;" k="29" />
<hkern u1="&#xe5;" u2="&#xcd;" k="29" />
<hkern u1="&#xe5;" u2="&#xcc;" k="29" />
<hkern u1="&#xe5;" u2="&#xcb;" k="29" />
<hkern u1="&#xe5;" u2="&#xca;" k="29" />
<hkern u1="&#xe5;" u2="&#xc9;" k="29" />
<hkern u1="&#xe5;" u2="&#xc8;" k="29" />
<hkern u1="&#xe5;" u2="&#xc7;" k="20" />
<hkern u1="&#xe5;" u2="&#xc5;" k="10" />
<hkern u1="&#xe5;" u2="&#xc4;" k="10" />
<hkern u1="&#xe5;" u2="&#xc3;" k="10" />
<hkern u1="&#xe5;" u2="&#xc2;" k="10" />
<hkern u1="&#xe5;" u2="&#xc1;" k="10" />
<hkern u1="&#xe5;" u2="&#xc0;" k="10" />
<hkern u1="&#xe5;" u2="&#x7d;" k="41" />
<hkern u1="&#xe5;" u2="y" k="14" />
<hkern u1="&#xe5;" u2="v" k="20" />
<hkern u1="&#xe5;" u2="u" k="8" />
<hkern u1="&#xe5;" u2="t" k="10" />
<hkern u1="&#xe5;" u2="f" k="12" />
<hkern u1="&#xe5;" u2="]" k="47" />
<hkern u1="&#xe5;" u2="\" k="47" />
<hkern u1="&#xe5;" u2="Y" k="176" />
<hkern u1="&#xe5;" u2="X" k="12" />
<hkern u1="&#xe5;" u2="V" k="109" />
<hkern u1="&#xe5;" u2="U" k="45" />
<hkern u1="&#xe5;" u2="T" k="186" />
<hkern u1="&#xe5;" u2="S" k="25" />
<hkern u1="&#xe5;" u2="R" k="29" />
<hkern u1="&#xe5;" u2="Q" k="20" />
<hkern u1="&#xe5;" u2="P" k="29" />
<hkern u1="&#xe5;" u2="O" k="20" />
<hkern u1="&#xe5;" u2="N" k="29" />
<hkern u1="&#xe5;" u2="M" k="29" />
<hkern u1="&#xe5;" u2="L" k="29" />
<hkern u1="&#xe5;" u2="K" k="29" />
<hkern u1="&#xe5;" u2="I" k="29" />
<hkern u1="&#xe5;" u2="H" k="29" />
<hkern u1="&#xe5;" u2="G" k="20" />
<hkern u1="&#xe5;" u2="F" k="29" />
<hkern u1="&#xe5;" u2="E" k="29" />
<hkern u1="&#xe5;" u2="D" k="29" />
<hkern u1="&#xe5;" u2="C" k="20" />
<hkern u1="&#xe5;" u2="B" k="29" />
<hkern u1="&#xe5;" u2="A" k="10" />
<hkern u1="&#xe5;" u2="&#x3f;" k="39" />
<hkern u1="&#xe5;" u2="&#x2a;" k="29" />
<hkern u1="&#xe5;" u2="&#x29;" k="41" />
<hkern u1="&#xe5;" u2="&#x27;" k="29" />
<hkern u1="&#xe5;" u2="&#x26;" k="12" />
<hkern u1="&#xe5;" u2="&#x22;" k="29" />
<hkern u1="&#xe6;" u2="&#x2122;" k="43" />
<hkern u1="&#xe6;" u2="&#x201d;" k="25" />
<hkern u1="&#xe6;" u2="&#x201c;" k="23" />
<hkern u1="&#xe6;" u2="&#x2019;" k="25" />
<hkern u1="&#xe6;" u2="&#x2018;" k="23" />
<hkern u1="&#xe6;" u2="&#xde;" k="23" />
<hkern u1="&#xe6;" u2="&#xdd;" k="170" />
<hkern u1="&#xe6;" u2="&#xdc;" k="27" />
<hkern u1="&#xe6;" u2="&#xdb;" k="27" />
<hkern u1="&#xe6;" u2="&#xda;" k="27" />
<hkern u1="&#xe6;" u2="&#xd9;" k="27" />
<hkern u1="&#xe6;" u2="&#xd8;" k="10" />
<hkern u1="&#xe6;" u2="&#xd6;" k="10" />
<hkern u1="&#xe6;" u2="&#xd5;" k="10" />
<hkern u1="&#xe6;" u2="&#xd4;" k="10" />
<hkern u1="&#xe6;" u2="&#xd3;" k="10" />
<hkern u1="&#xe6;" u2="&#xd2;" k="10" />
<hkern u1="&#xe6;" u2="&#xd1;" k="23" />
<hkern u1="&#xe6;" u2="&#xd0;" k="23" />
<hkern u1="&#xe6;" u2="&#xcf;" k="23" />
<hkern u1="&#xe6;" u2="&#xce;" k="23" />
<hkern u1="&#xe6;" u2="&#xcd;" k="23" />
<hkern u1="&#xe6;" u2="&#xcc;" k="23" />
<hkern u1="&#xe6;" u2="&#xcb;" k="23" />
<hkern u1="&#xe6;" u2="&#xca;" k="23" />
<hkern u1="&#xe6;" u2="&#xc9;" k="23" />
<hkern u1="&#xe6;" u2="&#xc8;" k="23" />
<hkern u1="&#xe6;" u2="&#xc7;" k="10" />
<hkern u1="&#xe6;" u2="&#xc5;" k="10" />
<hkern u1="&#xe6;" u2="&#xc4;" k="10" />
<hkern u1="&#xe6;" u2="&#xc3;" k="10" />
<hkern u1="&#xe6;" u2="&#xc2;" k="10" />
<hkern u1="&#xe6;" u2="&#xc1;" k="10" />
<hkern u1="&#xe6;" u2="&#xc0;" k="10" />
<hkern u1="&#xe6;" u2="&#x7d;" k="39" />
<hkern u1="&#xe6;" u2="x" k="12" />
<hkern u1="&#xe6;" u2="v" k="10" />
<hkern u1="&#xe6;" u2="]" k="43" />
<hkern u1="&#xe6;" u2="\" k="39" />
<hkern u1="&#xe6;" u2="Y" k="170" />
<hkern u1="&#xe6;" u2="X" k="33" />
<hkern u1="&#xe6;" u2="V" k="111" />
<hkern u1="&#xe6;" u2="U" k="27" />
<hkern u1="&#xe6;" u2="T" k="166" />
<hkern u1="&#xe6;" u2="S" k="23" />
<hkern u1="&#xe6;" u2="R" k="23" />
<hkern u1="&#xe6;" u2="Q" k="10" />
<hkern u1="&#xe6;" u2="P" k="23" />
<hkern u1="&#xe6;" u2="O" k="10" />
<hkern u1="&#xe6;" u2="N" k="23" />
<hkern u1="&#xe6;" u2="M" k="23" />
<hkern u1="&#xe6;" u2="L" k="23" />
<hkern u1="&#xe6;" u2="K" k="23" />
<hkern u1="&#xe6;" u2="I" k="23" />
<hkern u1="&#xe6;" u2="H" k="23" />
<hkern u1="&#xe6;" u2="G" k="10" />
<hkern u1="&#xe6;" u2="F" k="23" />
<hkern u1="&#xe6;" u2="E" k="23" />
<hkern u1="&#xe6;" u2="D" k="23" />
<hkern u1="&#xe6;" u2="C" k="10" />
<hkern u1="&#xe6;" u2="B" k="23" />
<hkern u1="&#xe6;" u2="A" k="10" />
<hkern u1="&#xe6;" u2="&#x3f;" k="35" />
<hkern u1="&#xe6;" u2="&#x2a;" k="23" />
<hkern u1="&#xe6;" u2="&#x29;" k="43" />
<hkern u1="&#xe6;" u2="&#x27;" k="25" />
<hkern u1="&#xe6;" u2="&#x26;" k="12" />
<hkern u1="&#xe6;" u2="&#x22;" k="25" />
<hkern u1="&#xe7;" u2="&#x2122;" k="25" />
<hkern u1="&#xe7;" u2="&#x2014;" k="25" />
<hkern u1="&#xe7;" u2="&#x2013;" k="25" />
<hkern u1="&#xe7;" u2="&#xf8;" k="8" />
<hkern u1="&#xe7;" u2="&#xf6;" k="8" />
<hkern u1="&#xe7;" u2="&#xf5;" k="8" />
<hkern u1="&#xe7;" u2="&#xf4;" k="8" />
<hkern u1="&#xe7;" u2="&#xf3;" k="8" />
<hkern u1="&#xe7;" u2="&#xf2;" k="8" />
<hkern u1="&#xe7;" u2="&#xf0;" k="8" />
<hkern u1="&#xe7;" u2="&#xeb;" k="8" />
<hkern u1="&#xe7;" u2="&#xea;" k="8" />
<hkern u1="&#xe7;" u2="&#xe9;" k="8" />
<hkern u1="&#xe7;" u2="&#xe8;" k="8" />
<hkern u1="&#xe7;" u2="&#xe7;" k="8" />
<hkern u1="&#xe7;" u2="&#xdd;" k="143" />
<hkern u1="&#xe7;" u2="&#xdc;" k="18" />
<hkern u1="&#xe7;" u2="&#xdb;" k="18" />
<hkern u1="&#xe7;" u2="&#xda;" k="18" />
<hkern u1="&#xe7;" u2="&#xd9;" k="18" />
<hkern u1="&#xe7;" u2="&#xd8;" k="23" />
<hkern u1="&#xe7;" u2="&#xd6;" k="23" />
<hkern u1="&#xe7;" u2="&#xd5;" k="23" />
<hkern u1="&#xe7;" u2="&#xd4;" k="23" />
<hkern u1="&#xe7;" u2="&#xd3;" k="23" />
<hkern u1="&#xe7;" u2="&#xd2;" k="23" />
<hkern u1="&#xe7;" u2="&#xc7;" k="23" />
<hkern u1="&#xe7;" u2="q" k="8" />
<hkern u1="&#xe7;" u2="o" k="8" />
<hkern u1="&#xe7;" u2="g" k="8" />
<hkern u1="&#xe7;" u2="e" k="8" />
<hkern u1="&#xe7;" u2="d" k="8" />
<hkern u1="&#xe7;" u2="c" k="8" />
<hkern u1="&#xe7;" u2="]" k="25" />
<hkern u1="&#xe7;" u2="Y" k="143" />
<hkern u1="&#xe7;" u2="V" k="63" />
<hkern u1="&#xe7;" u2="U" k="18" />
<hkern u1="&#xe7;" u2="T" k="197" />
<hkern u1="&#xe7;" u2="S" k="23" />
<hkern u1="&#xe7;" u2="Q" k="23" />
<hkern u1="&#xe7;" u2="O" k="23" />
<hkern u1="&#xe7;" u2="G" k="23" />
<hkern u1="&#xe7;" u2="C" k="23" />
<hkern u1="&#xe7;" u2="&#x2d;" k="25" />
<hkern u1="&#xe7;" u2="&#x29;" k="20" />
<hkern u1="&#xe7;" u2="&#x26;" k="35" />
<hkern u1="&#xe8;" u2="&#x2122;" k="43" />
<hkern u1="&#xe8;" u2="&#x201d;" k="25" />
<hkern u1="&#xe8;" u2="&#x201c;" k="23" />
<hkern u1="&#xe8;" u2="&#x2019;" k="25" />
<hkern u1="&#xe8;" u2="&#x2018;" k="23" />
<hkern u1="&#xe8;" u2="&#xde;" k="23" />
<hkern u1="&#xe8;" u2="&#xdd;" k="170" />
<hkern u1="&#xe8;" u2="&#xdc;" k="27" />
<hkern u1="&#xe8;" u2="&#xdb;" k="27" />
<hkern u1="&#xe8;" u2="&#xda;" k="27" />
<hkern u1="&#xe8;" u2="&#xd9;" k="27" />
<hkern u1="&#xe8;" u2="&#xd8;" k="10" />
<hkern u1="&#xe8;" u2="&#xd6;" k="10" />
<hkern u1="&#xe8;" u2="&#xd5;" k="10" />
<hkern u1="&#xe8;" u2="&#xd4;" k="10" />
<hkern u1="&#xe8;" u2="&#xd3;" k="10" />
<hkern u1="&#xe8;" u2="&#xd2;" k="10" />
<hkern u1="&#xe8;" u2="&#xd1;" k="23" />
<hkern u1="&#xe8;" u2="&#xd0;" k="23" />
<hkern u1="&#xe8;" u2="&#xcf;" k="23" />
<hkern u1="&#xe8;" u2="&#xce;" k="23" />
<hkern u1="&#xe8;" u2="&#xcd;" k="23" />
<hkern u1="&#xe8;" u2="&#xcc;" k="23" />
<hkern u1="&#xe8;" u2="&#xcb;" k="23" />
<hkern u1="&#xe8;" u2="&#xca;" k="23" />
<hkern u1="&#xe8;" u2="&#xc9;" k="23" />
<hkern u1="&#xe8;" u2="&#xc8;" k="23" />
<hkern u1="&#xe8;" u2="&#xc7;" k="10" />
<hkern u1="&#xe8;" u2="&#xc5;" k="10" />
<hkern u1="&#xe8;" u2="&#xc4;" k="10" />
<hkern u1="&#xe8;" u2="&#xc3;" k="10" />
<hkern u1="&#xe8;" u2="&#xc2;" k="10" />
<hkern u1="&#xe8;" u2="&#xc1;" k="10" />
<hkern u1="&#xe8;" u2="&#xc0;" k="10" />
<hkern u1="&#xe8;" u2="&#x7d;" k="39" />
<hkern u1="&#xe8;" u2="x" k="12" />
<hkern u1="&#xe8;" u2="v" k="10" />
<hkern u1="&#xe8;" u2="]" k="43" />
<hkern u1="&#xe8;" u2="\" k="39" />
<hkern u1="&#xe8;" u2="Y" k="170" />
<hkern u1="&#xe8;" u2="X" k="33" />
<hkern u1="&#xe8;" u2="V" k="111" />
<hkern u1="&#xe8;" u2="U" k="27" />
<hkern u1="&#xe8;" u2="T" k="166" />
<hkern u1="&#xe8;" u2="S" k="23" />
<hkern u1="&#xe8;" u2="R" k="23" />
<hkern u1="&#xe8;" u2="Q" k="10" />
<hkern u1="&#xe8;" u2="P" k="23" />
<hkern u1="&#xe8;" u2="O" k="10" />
<hkern u1="&#xe8;" u2="N" k="23" />
<hkern u1="&#xe8;" u2="M" k="23" />
<hkern u1="&#xe8;" u2="L" k="23" />
<hkern u1="&#xe8;" u2="K" k="23" />
<hkern u1="&#xe8;" u2="I" k="23" />
<hkern u1="&#xe8;" u2="H" k="23" />
<hkern u1="&#xe8;" u2="G" k="10" />
<hkern u1="&#xe8;" u2="F" k="23" />
<hkern u1="&#xe8;" u2="E" k="23" />
<hkern u1="&#xe8;" u2="D" k="23" />
<hkern u1="&#xe8;" u2="C" k="10" />
<hkern u1="&#xe8;" u2="B" k="23" />
<hkern u1="&#xe8;" u2="A" k="10" />
<hkern u1="&#xe8;" u2="&#x3f;" k="35" />
<hkern u1="&#xe8;" u2="&#x2a;" k="23" />
<hkern u1="&#xe8;" u2="&#x29;" k="43" />
<hkern u1="&#xe8;" u2="&#x27;" k="25" />
<hkern u1="&#xe8;" u2="&#x26;" k="12" />
<hkern u1="&#xe8;" u2="&#x22;" k="25" />
<hkern u1="&#xe9;" u2="&#x2122;" k="43" />
<hkern u1="&#xe9;" u2="&#x201d;" k="25" />
<hkern u1="&#xe9;" u2="&#x201c;" k="23" />
<hkern u1="&#xe9;" u2="&#x2019;" k="25" />
<hkern u1="&#xe9;" u2="&#x2018;" k="23" />
<hkern u1="&#xe9;" u2="&#xde;" k="23" />
<hkern u1="&#xe9;" u2="&#xdd;" k="170" />
<hkern u1="&#xe9;" u2="&#xdc;" k="27" />
<hkern u1="&#xe9;" u2="&#xdb;" k="27" />
<hkern u1="&#xe9;" u2="&#xda;" k="27" />
<hkern u1="&#xe9;" u2="&#xd9;" k="27" />
<hkern u1="&#xe9;" u2="&#xd8;" k="10" />
<hkern u1="&#xe9;" u2="&#xd6;" k="10" />
<hkern u1="&#xe9;" u2="&#xd5;" k="10" />
<hkern u1="&#xe9;" u2="&#xd4;" k="10" />
<hkern u1="&#xe9;" u2="&#xd3;" k="10" />
<hkern u1="&#xe9;" u2="&#xd2;" k="10" />
<hkern u1="&#xe9;" u2="&#xd1;" k="23" />
<hkern u1="&#xe9;" u2="&#xd0;" k="23" />
<hkern u1="&#xe9;" u2="&#xcf;" k="23" />
<hkern u1="&#xe9;" u2="&#xce;" k="23" />
<hkern u1="&#xe9;" u2="&#xcd;" k="23" />
<hkern u1="&#xe9;" u2="&#xcc;" k="23" />
<hkern u1="&#xe9;" u2="&#xcb;" k="23" />
<hkern u1="&#xe9;" u2="&#xca;" k="23" />
<hkern u1="&#xe9;" u2="&#xc9;" k="23" />
<hkern u1="&#xe9;" u2="&#xc8;" k="23" />
<hkern u1="&#xe9;" u2="&#xc7;" k="10" />
<hkern u1="&#xe9;" u2="&#xc5;" k="10" />
<hkern u1="&#xe9;" u2="&#xc4;" k="10" />
<hkern u1="&#xe9;" u2="&#xc3;" k="10" />
<hkern u1="&#xe9;" u2="&#xc2;" k="10" />
<hkern u1="&#xe9;" u2="&#xc1;" k="10" />
<hkern u1="&#xe9;" u2="&#xc0;" k="10" />
<hkern u1="&#xe9;" u2="&#x7d;" k="39" />
<hkern u1="&#xe9;" u2="x" k="12" />
<hkern u1="&#xe9;" u2="v" k="10" />
<hkern u1="&#xe9;" u2="]" k="43" />
<hkern u1="&#xe9;" u2="\" k="39" />
<hkern u1="&#xe9;" u2="Y" k="170" />
<hkern u1="&#xe9;" u2="X" k="33" />
<hkern u1="&#xe9;" u2="V" k="111" />
<hkern u1="&#xe9;" u2="U" k="27" />
<hkern u1="&#xe9;" u2="T" k="166" />
<hkern u1="&#xe9;" u2="S" k="23" />
<hkern u1="&#xe9;" u2="R" k="23" />
<hkern u1="&#xe9;" u2="Q" k="10" />
<hkern u1="&#xe9;" u2="P" k="23" />
<hkern u1="&#xe9;" u2="O" k="10" />
<hkern u1="&#xe9;" u2="N" k="23" />
<hkern u1="&#xe9;" u2="M" k="23" />
<hkern u1="&#xe9;" u2="L" k="23" />
<hkern u1="&#xe9;" u2="K" k="23" />
<hkern u1="&#xe9;" u2="I" k="23" />
<hkern u1="&#xe9;" u2="H" k="23" />
<hkern u1="&#xe9;" u2="G" k="10" />
<hkern u1="&#xe9;" u2="F" k="23" />
<hkern u1="&#xe9;" u2="E" k="23" />
<hkern u1="&#xe9;" u2="D" k="23" />
<hkern u1="&#xe9;" u2="C" k="10" />
<hkern u1="&#xe9;" u2="B" k="23" />
<hkern u1="&#xe9;" u2="A" k="10" />
<hkern u1="&#xe9;" u2="&#x3f;" k="35" />
<hkern u1="&#xe9;" u2="&#x2a;" k="23" />
<hkern u1="&#xe9;" u2="&#x29;" k="43" />
<hkern u1="&#xe9;" u2="&#x27;" k="25" />
<hkern u1="&#xe9;" u2="&#x26;" k="12" />
<hkern u1="&#xe9;" u2="&#x22;" k="25" />
<hkern u1="&#xea;" u2="&#x2122;" k="43" />
<hkern u1="&#xea;" u2="&#x201d;" k="25" />
<hkern u1="&#xea;" u2="&#x201c;" k="23" />
<hkern u1="&#xea;" u2="&#x2019;" k="25" />
<hkern u1="&#xea;" u2="&#x2018;" k="23" />
<hkern u1="&#xea;" u2="&#xde;" k="23" />
<hkern u1="&#xea;" u2="&#xdd;" k="170" />
<hkern u1="&#xea;" u2="&#xdc;" k="27" />
<hkern u1="&#xea;" u2="&#xdb;" k="27" />
<hkern u1="&#xea;" u2="&#xda;" k="27" />
<hkern u1="&#xea;" u2="&#xd9;" k="27" />
<hkern u1="&#xea;" u2="&#xd8;" k="10" />
<hkern u1="&#xea;" u2="&#xd6;" k="10" />
<hkern u1="&#xea;" u2="&#xd5;" k="10" />
<hkern u1="&#xea;" u2="&#xd4;" k="10" />
<hkern u1="&#xea;" u2="&#xd3;" k="10" />
<hkern u1="&#xea;" u2="&#xd2;" k="10" />
<hkern u1="&#xea;" u2="&#xd1;" k="23" />
<hkern u1="&#xea;" u2="&#xd0;" k="23" />
<hkern u1="&#xea;" u2="&#xcf;" k="23" />
<hkern u1="&#xea;" u2="&#xce;" k="23" />
<hkern u1="&#xea;" u2="&#xcd;" k="23" />
<hkern u1="&#xea;" u2="&#xcc;" k="23" />
<hkern u1="&#xea;" u2="&#xcb;" k="23" />
<hkern u1="&#xea;" u2="&#xca;" k="23" />
<hkern u1="&#xea;" u2="&#xc9;" k="23" />
<hkern u1="&#xea;" u2="&#xc8;" k="23" />
<hkern u1="&#xea;" u2="&#xc7;" k="10" />
<hkern u1="&#xea;" u2="&#xc5;" k="10" />
<hkern u1="&#xea;" u2="&#xc4;" k="10" />
<hkern u1="&#xea;" u2="&#xc3;" k="10" />
<hkern u1="&#xea;" u2="&#xc2;" k="10" />
<hkern u1="&#xea;" u2="&#xc1;" k="10" />
<hkern u1="&#xea;" u2="&#xc0;" k="10" />
<hkern u1="&#xea;" u2="&#x7d;" k="39" />
<hkern u1="&#xea;" u2="x" k="12" />
<hkern u1="&#xea;" u2="v" k="10" />
<hkern u1="&#xea;" u2="]" k="43" />
<hkern u1="&#xea;" u2="\" k="39" />
<hkern u1="&#xea;" u2="Y" k="170" />
<hkern u1="&#xea;" u2="X" k="33" />
<hkern u1="&#xea;" u2="V" k="111" />
<hkern u1="&#xea;" u2="U" k="27" />
<hkern u1="&#xea;" u2="T" k="166" />
<hkern u1="&#xea;" u2="S" k="23" />
<hkern u1="&#xea;" u2="R" k="23" />
<hkern u1="&#xea;" u2="Q" k="10" />
<hkern u1="&#xea;" u2="P" k="23" />
<hkern u1="&#xea;" u2="O" k="10" />
<hkern u1="&#xea;" u2="N" k="23" />
<hkern u1="&#xea;" u2="M" k="23" />
<hkern u1="&#xea;" u2="L" k="23" />
<hkern u1="&#xea;" u2="K" k="23" />
<hkern u1="&#xea;" u2="I" k="23" />
<hkern u1="&#xea;" u2="H" k="23" />
<hkern u1="&#xea;" u2="G" k="10" />
<hkern u1="&#xea;" u2="F" k="23" />
<hkern u1="&#xea;" u2="E" k="23" />
<hkern u1="&#xea;" u2="D" k="23" />
<hkern u1="&#xea;" u2="C" k="10" />
<hkern u1="&#xea;" u2="B" k="23" />
<hkern u1="&#xea;" u2="A" k="10" />
<hkern u1="&#xea;" u2="&#x3f;" k="35" />
<hkern u1="&#xea;" u2="&#x2a;" k="23" />
<hkern u1="&#xea;" u2="&#x29;" k="43" />
<hkern u1="&#xea;" u2="&#x27;" k="25" />
<hkern u1="&#xea;" u2="&#x26;" k="12" />
<hkern u1="&#xea;" u2="&#x22;" k="25" />
<hkern u1="&#xeb;" u2="&#x2122;" k="43" />
<hkern u1="&#xeb;" u2="&#x201d;" k="25" />
<hkern u1="&#xeb;" u2="&#x201c;" k="23" />
<hkern u1="&#xeb;" u2="&#x2019;" k="25" />
<hkern u1="&#xeb;" u2="&#x2018;" k="23" />
<hkern u1="&#xeb;" u2="&#xde;" k="23" />
<hkern u1="&#xeb;" u2="&#xdd;" k="170" />
<hkern u1="&#xeb;" u2="&#xdc;" k="27" />
<hkern u1="&#xeb;" u2="&#xdb;" k="27" />
<hkern u1="&#xeb;" u2="&#xda;" k="27" />
<hkern u1="&#xeb;" u2="&#xd9;" k="27" />
<hkern u1="&#xeb;" u2="&#xd8;" k="10" />
<hkern u1="&#xeb;" u2="&#xd6;" k="10" />
<hkern u1="&#xeb;" u2="&#xd5;" k="10" />
<hkern u1="&#xeb;" u2="&#xd4;" k="10" />
<hkern u1="&#xeb;" u2="&#xd3;" k="10" />
<hkern u1="&#xeb;" u2="&#xd2;" k="10" />
<hkern u1="&#xeb;" u2="&#xd1;" k="23" />
<hkern u1="&#xeb;" u2="&#xd0;" k="23" />
<hkern u1="&#xeb;" u2="&#xcf;" k="23" />
<hkern u1="&#xeb;" u2="&#xce;" k="23" />
<hkern u1="&#xeb;" u2="&#xcd;" k="23" />
<hkern u1="&#xeb;" u2="&#xcc;" k="23" />
<hkern u1="&#xeb;" u2="&#xcb;" k="23" />
<hkern u1="&#xeb;" u2="&#xca;" k="23" />
<hkern u1="&#xeb;" u2="&#xc9;" k="23" />
<hkern u1="&#xeb;" u2="&#xc8;" k="23" />
<hkern u1="&#xeb;" u2="&#xc7;" k="10" />
<hkern u1="&#xeb;" u2="&#xc5;" k="10" />
<hkern u1="&#xeb;" u2="&#xc4;" k="10" />
<hkern u1="&#xeb;" u2="&#xc3;" k="10" />
<hkern u1="&#xeb;" u2="&#xc2;" k="10" />
<hkern u1="&#xeb;" u2="&#xc1;" k="10" />
<hkern u1="&#xeb;" u2="&#xc0;" k="10" />
<hkern u1="&#xeb;" u2="&#x7d;" k="39" />
<hkern u1="&#xeb;" u2="x" k="12" />
<hkern u1="&#xeb;" u2="v" k="10" />
<hkern u1="&#xeb;" u2="]" k="43" />
<hkern u1="&#xeb;" u2="\" k="39" />
<hkern u1="&#xeb;" u2="Y" k="170" />
<hkern u1="&#xeb;" u2="X" k="33" />
<hkern u1="&#xeb;" u2="V" k="111" />
<hkern u1="&#xeb;" u2="U" k="27" />
<hkern u1="&#xeb;" u2="T" k="166" />
<hkern u1="&#xeb;" u2="S" k="23" />
<hkern u1="&#xeb;" u2="R" k="23" />
<hkern u1="&#xeb;" u2="Q" k="10" />
<hkern u1="&#xeb;" u2="P" k="23" />
<hkern u1="&#xeb;" u2="O" k="10" />
<hkern u1="&#xeb;" u2="N" k="23" />
<hkern u1="&#xeb;" u2="M" k="23" />
<hkern u1="&#xeb;" u2="L" k="23" />
<hkern u1="&#xeb;" u2="K" k="23" />
<hkern u1="&#xeb;" u2="I" k="23" />
<hkern u1="&#xeb;" u2="H" k="23" />
<hkern u1="&#xeb;" u2="G" k="10" />
<hkern u1="&#xeb;" u2="F" k="23" />
<hkern u1="&#xeb;" u2="E" k="23" />
<hkern u1="&#xeb;" u2="D" k="23" />
<hkern u1="&#xeb;" u2="C" k="10" />
<hkern u1="&#xeb;" u2="B" k="23" />
<hkern u1="&#xeb;" u2="A" k="10" />
<hkern u1="&#xeb;" u2="&#x3f;" k="35" />
<hkern u1="&#xeb;" u2="&#x2a;" k="23" />
<hkern u1="&#xeb;" u2="&#x29;" k="43" />
<hkern u1="&#xeb;" u2="&#x27;" k="25" />
<hkern u1="&#xeb;" u2="&#x26;" k="12" />
<hkern u1="&#xeb;" u2="&#x22;" k="25" />
<hkern u1="&#xec;" u2="&#xde;" k="20" />
<hkern u1="&#xec;" u2="&#xdd;" k="16" />
<hkern u1="&#xec;" u2="&#xdc;" k="31" />
<hkern u1="&#xec;" u2="&#xdb;" k="31" />
<hkern u1="&#xec;" u2="&#xda;" k="31" />
<hkern u1="&#xec;" u2="&#xd9;" k="31" />
<hkern u1="&#xec;" u2="&#xd8;" k="20" />
<hkern u1="&#xec;" u2="&#xd6;" k="20" />
<hkern u1="&#xec;" u2="&#xd5;" k="20" />
<hkern u1="&#xec;" u2="&#xd4;" k="20" />
<hkern u1="&#xec;" u2="&#xd3;" k="20" />
<hkern u1="&#xec;" u2="&#xd2;" k="20" />
<hkern u1="&#xec;" u2="&#xd1;" k="20" />
<hkern u1="&#xec;" u2="&#xd0;" k="20" />
<hkern u1="&#xec;" u2="&#xcf;" k="20" />
<hkern u1="&#xec;" u2="&#xce;" k="20" />
<hkern u1="&#xec;" u2="&#xcd;" k="20" />
<hkern u1="&#xec;" u2="&#xcc;" k="20" />
<hkern u1="&#xec;" u2="&#xcb;" k="20" />
<hkern u1="&#xec;" u2="&#xca;" k="20" />
<hkern u1="&#xec;" u2="&#xc9;" k="20" />
<hkern u1="&#xec;" u2="&#xc8;" k="20" />
<hkern u1="&#xec;" u2="&#xc7;" k="20" />
<hkern u1="&#xec;" u2="&#xc5;" k="14" />
<hkern u1="&#xec;" u2="&#xc4;" k="14" />
<hkern u1="&#xec;" u2="&#xc3;" k="14" />
<hkern u1="&#xec;" u2="&#xc2;" k="14" />
<hkern u1="&#xec;" u2="&#xc1;" k="14" />
<hkern u1="&#xec;" u2="&#xc0;" k="14" />
<hkern u1="&#xec;" u2="Y" k="16" />
<hkern u1="&#xec;" u2="V" k="18" />
<hkern u1="&#xec;" u2="U" k="31" />
<hkern u1="&#xec;" u2="T" k="20" />
<hkern u1="&#xec;" u2="S" k="18" />
<hkern u1="&#xec;" u2="R" k="20" />
<hkern u1="&#xec;" u2="Q" k="20" />
<hkern u1="&#xec;" u2="P" k="20" />
<hkern u1="&#xec;" u2="O" k="20" />
<hkern u1="&#xec;" u2="N" k="20" />
<hkern u1="&#xec;" u2="M" k="20" />
<hkern u1="&#xec;" u2="L" k="20" />
<hkern u1="&#xec;" u2="K" k="20" />
<hkern u1="&#xec;" u2="I" k="20" />
<hkern u1="&#xec;" u2="H" k="20" />
<hkern u1="&#xec;" u2="G" k="20" />
<hkern u1="&#xec;" u2="F" k="20" />
<hkern u1="&#xec;" u2="E" k="20" />
<hkern u1="&#xec;" u2="D" k="20" />
<hkern u1="&#xec;" u2="C" k="20" />
<hkern u1="&#xec;" u2="B" k="20" />
<hkern u1="&#xec;" u2="A" k="14" />
<hkern u1="&#xec;" u2="&#x26;" k="16" />
<hkern u1="&#xed;" u2="&#xde;" k="20" />
<hkern u1="&#xed;" u2="&#xdd;" k="16" />
<hkern u1="&#xed;" u2="&#xdc;" k="31" />
<hkern u1="&#xed;" u2="&#xdb;" k="31" />
<hkern u1="&#xed;" u2="&#xda;" k="31" />
<hkern u1="&#xed;" u2="&#xd9;" k="31" />
<hkern u1="&#xed;" u2="&#xd8;" k="20" />
<hkern u1="&#xed;" u2="&#xd6;" k="20" />
<hkern u1="&#xed;" u2="&#xd5;" k="20" />
<hkern u1="&#xed;" u2="&#xd4;" k="20" />
<hkern u1="&#xed;" u2="&#xd3;" k="20" />
<hkern u1="&#xed;" u2="&#xd2;" k="20" />
<hkern u1="&#xed;" u2="&#xd1;" k="20" />
<hkern u1="&#xed;" u2="&#xd0;" k="20" />
<hkern u1="&#xed;" u2="&#xcf;" k="20" />
<hkern u1="&#xed;" u2="&#xce;" k="20" />
<hkern u1="&#xed;" u2="&#xcd;" k="20" />
<hkern u1="&#xed;" u2="&#xcc;" k="20" />
<hkern u1="&#xed;" u2="&#xcb;" k="20" />
<hkern u1="&#xed;" u2="&#xca;" k="20" />
<hkern u1="&#xed;" u2="&#xc9;" k="20" />
<hkern u1="&#xed;" u2="&#xc8;" k="20" />
<hkern u1="&#xed;" u2="&#xc7;" k="20" />
<hkern u1="&#xed;" u2="&#xc5;" k="14" />
<hkern u1="&#xed;" u2="&#xc4;" k="14" />
<hkern u1="&#xed;" u2="&#xc3;" k="14" />
<hkern u1="&#xed;" u2="&#xc2;" k="14" />
<hkern u1="&#xed;" u2="&#xc1;" k="14" />
<hkern u1="&#xed;" u2="&#xc0;" k="14" />
<hkern u1="&#xed;" u2="Y" k="16" />
<hkern u1="&#xed;" u2="V" k="18" />
<hkern u1="&#xed;" u2="U" k="31" />
<hkern u1="&#xed;" u2="T" k="20" />
<hkern u1="&#xed;" u2="S" k="18" />
<hkern u1="&#xed;" u2="R" k="20" />
<hkern u1="&#xed;" u2="Q" k="20" />
<hkern u1="&#xed;" u2="P" k="20" />
<hkern u1="&#xed;" u2="O" k="20" />
<hkern u1="&#xed;" u2="N" k="20" />
<hkern u1="&#xed;" u2="M" k="20" />
<hkern u1="&#xed;" u2="L" k="20" />
<hkern u1="&#xed;" u2="K" k="20" />
<hkern u1="&#xed;" u2="I" k="20" />
<hkern u1="&#xed;" u2="H" k="20" />
<hkern u1="&#xed;" u2="G" k="20" />
<hkern u1="&#xed;" u2="F" k="20" />
<hkern u1="&#xed;" u2="E" k="20" />
<hkern u1="&#xed;" u2="D" k="20" />
<hkern u1="&#xed;" u2="C" k="20" />
<hkern u1="&#xed;" u2="B" k="20" />
<hkern u1="&#xed;" u2="A" k="14" />
<hkern u1="&#xed;" u2="&#x26;" k="16" />
<hkern u1="&#xee;" u2="&#xde;" k="20" />
<hkern u1="&#xee;" u2="&#xdd;" k="16" />
<hkern u1="&#xee;" u2="&#xdc;" k="31" />
<hkern u1="&#xee;" u2="&#xdb;" k="31" />
<hkern u1="&#xee;" u2="&#xda;" k="31" />
<hkern u1="&#xee;" u2="&#xd9;" k="31" />
<hkern u1="&#xee;" u2="&#xd8;" k="20" />
<hkern u1="&#xee;" u2="&#xd6;" k="20" />
<hkern u1="&#xee;" u2="&#xd5;" k="20" />
<hkern u1="&#xee;" u2="&#xd4;" k="20" />
<hkern u1="&#xee;" u2="&#xd3;" k="20" />
<hkern u1="&#xee;" u2="&#xd2;" k="20" />
<hkern u1="&#xee;" u2="&#xd1;" k="20" />
<hkern u1="&#xee;" u2="&#xd0;" k="20" />
<hkern u1="&#xee;" u2="&#xcf;" k="20" />
<hkern u1="&#xee;" u2="&#xce;" k="20" />
<hkern u1="&#xee;" u2="&#xcd;" k="20" />
<hkern u1="&#xee;" u2="&#xcc;" k="20" />
<hkern u1="&#xee;" u2="&#xcb;" k="20" />
<hkern u1="&#xee;" u2="&#xca;" k="20" />
<hkern u1="&#xee;" u2="&#xc9;" k="20" />
<hkern u1="&#xee;" u2="&#xc8;" k="20" />
<hkern u1="&#xee;" u2="&#xc7;" k="20" />
<hkern u1="&#xee;" u2="&#xc5;" k="14" />
<hkern u1="&#xee;" u2="&#xc4;" k="14" />
<hkern u1="&#xee;" u2="&#xc3;" k="14" />
<hkern u1="&#xee;" u2="&#xc2;" k="14" />
<hkern u1="&#xee;" u2="&#xc1;" k="14" />
<hkern u1="&#xee;" u2="&#xc0;" k="14" />
<hkern u1="&#xee;" u2="Y" k="16" />
<hkern u1="&#xee;" u2="V" k="18" />
<hkern u1="&#xee;" u2="U" k="31" />
<hkern u1="&#xee;" u2="T" k="20" />
<hkern u1="&#xee;" u2="S" k="18" />
<hkern u1="&#xee;" u2="R" k="20" />
<hkern u1="&#xee;" u2="Q" k="20" />
<hkern u1="&#xee;" u2="P" k="20" />
<hkern u1="&#xee;" u2="O" k="20" />
<hkern u1="&#xee;" u2="N" k="20" />
<hkern u1="&#xee;" u2="M" k="20" />
<hkern u1="&#xee;" u2="L" k="20" />
<hkern u1="&#xee;" u2="K" k="20" />
<hkern u1="&#xee;" u2="I" k="20" />
<hkern u1="&#xee;" u2="H" k="20" />
<hkern u1="&#xee;" u2="G" k="20" />
<hkern u1="&#xee;" u2="F" k="20" />
<hkern u1="&#xee;" u2="E" k="20" />
<hkern u1="&#xee;" u2="D" k="20" />
<hkern u1="&#xee;" u2="C" k="20" />
<hkern u1="&#xee;" u2="B" k="20" />
<hkern u1="&#xee;" u2="A" k="14" />
<hkern u1="&#xee;" u2="&#x3f;" k="-29" />
<hkern u1="&#xee;" u2="&#x2a;" k="-39" />
<hkern u1="&#xee;" u2="&#x26;" k="16" />
<hkern u1="&#xef;" u2="&#x2122;" k="-12" />
<hkern u1="&#xef;" u2="&#xde;" k="20" />
<hkern u1="&#xef;" u2="&#xdd;" k="16" />
<hkern u1="&#xef;" u2="&#xdc;" k="31" />
<hkern u1="&#xef;" u2="&#xdb;" k="31" />
<hkern u1="&#xef;" u2="&#xda;" k="31" />
<hkern u1="&#xef;" u2="&#xd9;" k="31" />
<hkern u1="&#xef;" u2="&#xd8;" k="20" />
<hkern u1="&#xef;" u2="&#xd6;" k="20" />
<hkern u1="&#xef;" u2="&#xd5;" k="20" />
<hkern u1="&#xef;" u2="&#xd4;" k="20" />
<hkern u1="&#xef;" u2="&#xd3;" k="20" />
<hkern u1="&#xef;" u2="&#xd2;" k="20" />
<hkern u1="&#xef;" u2="&#xd1;" k="20" />
<hkern u1="&#xef;" u2="&#xd0;" k="20" />
<hkern u1="&#xef;" u2="&#xcf;" k="20" />
<hkern u1="&#xef;" u2="&#xce;" k="20" />
<hkern u1="&#xef;" u2="&#xcd;" k="20" />
<hkern u1="&#xef;" u2="&#xcc;" k="20" />
<hkern u1="&#xef;" u2="&#xcb;" k="20" />
<hkern u1="&#xef;" u2="&#xca;" k="20" />
<hkern u1="&#xef;" u2="&#xc9;" k="20" />
<hkern u1="&#xef;" u2="&#xc8;" k="20" />
<hkern u1="&#xef;" u2="&#xc7;" k="20" />
<hkern u1="&#xef;" u2="&#xc5;" k="14" />
<hkern u1="&#xef;" u2="&#xc4;" k="14" />
<hkern u1="&#xef;" u2="&#xc3;" k="14" />
<hkern u1="&#xef;" u2="&#xc2;" k="14" />
<hkern u1="&#xef;" u2="&#xc1;" k="14" />
<hkern u1="&#xef;" u2="&#xc0;" k="14" />
<hkern u1="&#xef;" u2="Y" k="16" />
<hkern u1="&#xef;" u2="V" k="18" />
<hkern u1="&#xef;" u2="U" k="31" />
<hkern u1="&#xef;" u2="T" k="20" />
<hkern u1="&#xef;" u2="S" k="18" />
<hkern u1="&#xef;" u2="R" k="20" />
<hkern u1="&#xef;" u2="Q" k="20" />
<hkern u1="&#xef;" u2="P" k="20" />
<hkern u1="&#xef;" u2="O" k="20" />
<hkern u1="&#xef;" u2="N" k="20" />
<hkern u1="&#xef;" u2="M" k="20" />
<hkern u1="&#xef;" u2="L" k="20" />
<hkern u1="&#xef;" u2="K" k="20" />
<hkern u1="&#xef;" u2="I" k="20" />
<hkern u1="&#xef;" u2="H" k="20" />
<hkern u1="&#xef;" u2="G" k="20" />
<hkern u1="&#xef;" u2="F" k="20" />
<hkern u1="&#xef;" u2="E" k="20" />
<hkern u1="&#xef;" u2="D" k="20" />
<hkern u1="&#xef;" u2="C" k="20" />
<hkern u1="&#xef;" u2="B" k="20" />
<hkern u1="&#xef;" u2="A" k="14" />
<hkern u1="&#xef;" u2="&#x2a;" k="-20" />
<hkern u1="&#xef;" u2="&#x26;" k="16" />
<hkern u1="&#xf0;" u2="&#x2122;" k="35" />
<hkern u1="&#xf0;" u2="&#x201d;" k="27" />
<hkern u1="&#xf0;" u2="&#x201c;" k="25" />
<hkern u1="&#xf0;" u2="&#x2019;" k="27" />
<hkern u1="&#xf0;" u2="&#x2018;" k="25" />
<hkern u1="&#xf0;" u2="&#x7d;" k="37" />
<hkern u1="&#xf0;" u2="x" k="23" />
<hkern u1="&#xf0;" u2="v" k="8" />
<hkern u1="&#xf0;" u2="]" k="39" />
<hkern u1="&#xf0;" u2="\" k="25" />
<hkern u1="&#xf0;" u2="&#x3f;" k="25" />
<hkern u1="&#xf0;" u2="&#x2a;" k="20" />
<hkern u1="&#xf0;" u2="&#x29;" k="43" />
<hkern u1="&#xf0;" u2="&#x27;" k="27" />
<hkern u1="&#xf0;" u2="&#x26;" k="10" />
<hkern u1="&#xf0;" u2="&#x22;" k="27" />
<hkern u1="&#xf1;" u2="&#xfb04;" k="10" />
<hkern u1="&#xf1;" u2="&#xfb03;" k="10" />
<hkern u1="&#xf1;" u2="&#xfb02;" k="10" />
<hkern u1="&#xf1;" u2="&#xfb01;" k="10" />
<hkern u1="&#xf1;" u2="&#x2122;" k="49" />
<hkern u1="&#xf1;" u2="&#x201d;" k="29" />
<hkern u1="&#xf1;" u2="&#x201c;" k="23" />
<hkern u1="&#xf1;" u2="&#x2019;" k="29" />
<hkern u1="&#xf1;" u2="&#x2018;" k="23" />
<hkern u1="&#xf1;" u2="&#xff;" k="12" />
<hkern u1="&#xf1;" u2="&#xfd;" k="12" />
<hkern u1="&#xf1;" u2="&#xdf;" k="10" />
<hkern u1="&#xf1;" u2="&#xde;" k="29" />
<hkern u1="&#xf1;" u2="&#xdd;" k="166" />
<hkern u1="&#xf1;" u2="&#xdc;" k="43" />
<hkern u1="&#xf1;" u2="&#xdb;" k="43" />
<hkern u1="&#xf1;" u2="&#xda;" k="43" />
<hkern u1="&#xf1;" u2="&#xd9;" k="43" />
<hkern u1="&#xf1;" u2="&#xd8;" k="20" />
<hkern u1="&#xf1;" u2="&#xd6;" k="20" />
<hkern u1="&#xf1;" u2="&#xd5;" k="20" />
<hkern u1="&#xf1;" u2="&#xd4;" k="20" />
<hkern u1="&#xf1;" u2="&#xd3;" k="20" />
<hkern u1="&#xf1;" u2="&#xd2;" k="20" />
<hkern u1="&#xf1;" u2="&#xd1;" k="29" />
<hkern u1="&#xf1;" u2="&#xd0;" k="29" />
<hkern u1="&#xf1;" u2="&#xcf;" k="29" />
<hkern u1="&#xf1;" u2="&#xce;" k="29" />
<hkern u1="&#xf1;" u2="&#xcd;" k="29" />
<hkern u1="&#xf1;" u2="&#xcc;" k="29" />
<hkern u1="&#xf1;" u2="&#xcb;" k="29" />
<hkern u1="&#xf1;" u2="&#xca;" k="29" />
<hkern u1="&#xf1;" u2="&#xc9;" k="29" />
<hkern u1="&#xf1;" u2="&#xc8;" k="29" />
<hkern u1="&#xf1;" u2="&#xc7;" k="20" />
<hkern u1="&#xf1;" u2="&#xc5;" k="10" />
<hkern u1="&#xf1;" u2="&#xc4;" k="10" />
<hkern u1="&#xf1;" u2="&#xc3;" k="10" />
<hkern u1="&#xf1;" u2="&#xc2;" k="10" />
<hkern u1="&#xf1;" u2="&#xc1;" k="10" />
<hkern u1="&#xf1;" u2="&#xc0;" k="10" />
<hkern u1="&#xf1;" u2="&#x7d;" k="41" />
<hkern u1="&#xf1;" u2="y" k="12" />
<hkern u1="&#xf1;" u2="v" k="18" />
<hkern u1="&#xf1;" u2="t" k="8" />
<hkern u1="&#xf1;" u2="f" k="10" />
<hkern u1="&#xf1;" u2="]" k="47" />
<hkern u1="&#xf1;" u2="\" k="45" />
<hkern u1="&#xf1;" u2="Y" k="166" />
<hkern u1="&#xf1;" u2="X" k="12" />
<hkern u1="&#xf1;" u2="V" k="106" />
<hkern u1="&#xf1;" u2="U" k="43" />
<hkern u1="&#xf1;" u2="T" k="184" />
<hkern u1="&#xf1;" u2="S" k="23" />
<hkern u1="&#xf1;" u2="R" k="29" />
<hkern u1="&#xf1;" u2="Q" k="20" />
<hkern u1="&#xf1;" u2="P" k="29" />
<hkern u1="&#xf1;" u2="O" k="20" />
<hkern u1="&#xf1;" u2="N" k="29" />
<hkern u1="&#xf1;" u2="M" k="29" />
<hkern u1="&#xf1;" u2="L" k="29" />
<hkern u1="&#xf1;" u2="K" k="29" />
<hkern u1="&#xf1;" u2="I" k="29" />
<hkern u1="&#xf1;" u2="H" k="29" />
<hkern u1="&#xf1;" u2="G" k="20" />
<hkern u1="&#xf1;" u2="F" k="29" />
<hkern u1="&#xf1;" u2="E" k="29" />
<hkern u1="&#xf1;" u2="D" k="29" />
<hkern u1="&#xf1;" u2="C" k="20" />
<hkern u1="&#xf1;" u2="B" k="29" />
<hkern u1="&#xf1;" u2="A" k="10" />
<hkern u1="&#xf1;" u2="&#x3f;" k="37" />
<hkern u1="&#xf1;" u2="&#x2a;" k="27" />
<hkern u1="&#xf1;" u2="&#x29;" k="41" />
<hkern u1="&#xf1;" u2="&#x27;" k="29" />
<hkern u1="&#xf1;" u2="&#x26;" k="12" />
<hkern u1="&#xf1;" u2="&#x22;" k="29" />
<hkern u1="&#xf2;" u2="&#xfb04;" k="10" />
<hkern u1="&#xf2;" u2="&#xfb03;" k="10" />
<hkern u1="&#xf2;" u2="&#xfb02;" k="10" />
<hkern u1="&#xf2;" u2="&#xfb01;" k="10" />
<hkern u1="&#xf2;" u2="&#x2122;" k="47" />
<hkern u1="&#xf2;" u2="&#x201d;" k="33" />
<hkern u1="&#xf2;" u2="&#x201c;" k="31" />
<hkern u1="&#xf2;" u2="&#x2019;" k="33" />
<hkern u1="&#xf2;" u2="&#x2018;" k="31" />
<hkern u1="&#xf2;" u2="&#xff;" k="10" />
<hkern u1="&#xf2;" u2="&#xfd;" k="10" />
<hkern u1="&#xf2;" u2="&#xdf;" k="10" />
<hkern u1="&#xf2;" u2="&#xde;" k="29" />
<hkern u1="&#xf2;" u2="&#xdd;" k="178" />
<hkern u1="&#xf2;" u2="&#xdc;" k="31" />
<hkern u1="&#xf2;" u2="&#xdb;" k="31" />
<hkern u1="&#xf2;" u2="&#xda;" k="31" />
<hkern u1="&#xf2;" u2="&#xd9;" k="31" />
<hkern u1="&#xf2;" u2="&#xd8;" k="12" />
<hkern u1="&#xf2;" u2="&#xd6;" k="12" />
<hkern u1="&#xf2;" u2="&#xd5;" k="12" />
<hkern u1="&#xf2;" u2="&#xd4;" k="12" />
<hkern u1="&#xf2;" u2="&#xd3;" k="12" />
<hkern u1="&#xf2;" u2="&#xd2;" k="12" />
<hkern u1="&#xf2;" u2="&#xd1;" k="29" />
<hkern u1="&#xf2;" u2="&#xd0;" k="29" />
<hkern u1="&#xf2;" u2="&#xcf;" k="29" />
<hkern u1="&#xf2;" u2="&#xce;" k="29" />
<hkern u1="&#xf2;" u2="&#xcd;" k="29" />
<hkern u1="&#xf2;" u2="&#xcc;" k="29" />
<hkern u1="&#xf2;" u2="&#xcb;" k="29" />
<hkern u1="&#xf2;" u2="&#xca;" k="29" />
<hkern u1="&#xf2;" u2="&#xc9;" k="29" />
<hkern u1="&#xf2;" u2="&#xc8;" k="29" />
<hkern u1="&#xf2;" u2="&#xc7;" k="12" />
<hkern u1="&#xf2;" u2="&#xc5;" k="25" />
<hkern u1="&#xf2;" u2="&#xc4;" k="25" />
<hkern u1="&#xf2;" u2="&#xc3;" k="25" />
<hkern u1="&#xf2;" u2="&#xc2;" k="25" />
<hkern u1="&#xf2;" u2="&#xc1;" k="25" />
<hkern u1="&#xf2;" u2="&#xc0;" k="25" />
<hkern u1="&#xf2;" u2="&#x7d;" k="47" />
<hkern u1="&#xf2;" u2="y" k="10" />
<hkern u1="&#xf2;" u2="x" k="31" />
<hkern u1="&#xf2;" u2="v" k="14" />
<hkern u1="&#xf2;" u2="t" k="8" />
<hkern u1="&#xf2;" u2="f" k="10" />
<hkern u1="&#xf2;" u2="]" k="51" />
<hkern u1="&#xf2;" u2="\" k="45" />
<hkern u1="&#xf2;" u2="Y" k="178" />
<hkern u1="&#xf2;" u2="X" k="84" />
<hkern u1="&#xf2;" u2="V" k="109" />
<hkern u1="&#xf2;" u2="U" k="31" />
<hkern u1="&#xf2;" u2="T" k="176" />
<hkern u1="&#xf2;" u2="S" k="29" />
<hkern u1="&#xf2;" u2="R" k="29" />
<hkern u1="&#xf2;" u2="Q" k="12" />
<hkern u1="&#xf2;" u2="P" k="29" />
<hkern u1="&#xf2;" u2="O" k="12" />
<hkern u1="&#xf2;" u2="N" k="29" />
<hkern u1="&#xf2;" u2="M" k="29" />
<hkern u1="&#xf2;" u2="L" k="29" />
<hkern u1="&#xf2;" u2="K" k="29" />
<hkern u1="&#xf2;" u2="I" k="29" />
<hkern u1="&#xf2;" u2="H" k="29" />
<hkern u1="&#xf2;" u2="G" k="12" />
<hkern u1="&#xf2;" u2="F" k="29" />
<hkern u1="&#xf2;" u2="E" k="29" />
<hkern u1="&#xf2;" u2="D" k="29" />
<hkern u1="&#xf2;" u2="C" k="12" />
<hkern u1="&#xf2;" u2="B" k="29" />
<hkern u1="&#xf2;" u2="A" k="25" />
<hkern u1="&#xf2;" u2="&#x3f;" k="41" />
<hkern u1="&#xf2;" u2="&#x2a;" k="29" />
<hkern u1="&#xf2;" u2="&#x29;" k="55" />
<hkern u1="&#xf2;" u2="&#x27;" k="33" />
<hkern u1="&#xf2;" u2="&#x22;" k="33" />
<hkern u1="&#xf3;" u2="&#xfb04;" k="10" />
<hkern u1="&#xf3;" u2="&#xfb03;" k="10" />
<hkern u1="&#xf3;" u2="&#xfb02;" k="10" />
<hkern u1="&#xf3;" u2="&#xfb01;" k="10" />
<hkern u1="&#xf3;" u2="&#x2122;" k="47" />
<hkern u1="&#xf3;" u2="&#x201d;" k="33" />
<hkern u1="&#xf3;" u2="&#x201c;" k="31" />
<hkern u1="&#xf3;" u2="&#x2019;" k="33" />
<hkern u1="&#xf3;" u2="&#x2018;" k="31" />
<hkern u1="&#xf3;" u2="&#xff;" k="10" />
<hkern u1="&#xf3;" u2="&#xfd;" k="10" />
<hkern u1="&#xf3;" u2="&#xdf;" k="10" />
<hkern u1="&#xf3;" u2="&#xde;" k="29" />
<hkern u1="&#xf3;" u2="&#xdd;" k="178" />
<hkern u1="&#xf3;" u2="&#xdc;" k="31" />
<hkern u1="&#xf3;" u2="&#xdb;" k="31" />
<hkern u1="&#xf3;" u2="&#xda;" k="31" />
<hkern u1="&#xf3;" u2="&#xd9;" k="31" />
<hkern u1="&#xf3;" u2="&#xd8;" k="12" />
<hkern u1="&#xf3;" u2="&#xd6;" k="12" />
<hkern u1="&#xf3;" u2="&#xd5;" k="12" />
<hkern u1="&#xf3;" u2="&#xd4;" k="12" />
<hkern u1="&#xf3;" u2="&#xd3;" k="12" />
<hkern u1="&#xf3;" u2="&#xd2;" k="12" />
<hkern u1="&#xf3;" u2="&#xd1;" k="29" />
<hkern u1="&#xf3;" u2="&#xd0;" k="29" />
<hkern u1="&#xf3;" u2="&#xcf;" k="29" />
<hkern u1="&#xf3;" u2="&#xce;" k="29" />
<hkern u1="&#xf3;" u2="&#xcd;" k="29" />
<hkern u1="&#xf3;" u2="&#xcc;" k="29" />
<hkern u1="&#xf3;" u2="&#xcb;" k="29" />
<hkern u1="&#xf3;" u2="&#xca;" k="29" />
<hkern u1="&#xf3;" u2="&#xc9;" k="29" />
<hkern u1="&#xf3;" u2="&#xc8;" k="29" />
<hkern u1="&#xf3;" u2="&#xc7;" k="12" />
<hkern u1="&#xf3;" u2="&#xc5;" k="25" />
<hkern u1="&#xf3;" u2="&#xc4;" k="25" />
<hkern u1="&#xf3;" u2="&#xc3;" k="25" />
<hkern u1="&#xf3;" u2="&#xc2;" k="25" />
<hkern u1="&#xf3;" u2="&#xc1;" k="25" />
<hkern u1="&#xf3;" u2="&#xc0;" k="25" />
<hkern u1="&#xf3;" u2="&#x7d;" k="47" />
<hkern u1="&#xf3;" u2="y" k="10" />
<hkern u1="&#xf3;" u2="x" k="31" />
<hkern u1="&#xf3;" u2="v" k="14" />
<hkern u1="&#xf3;" u2="t" k="8" />
<hkern u1="&#xf3;" u2="f" k="10" />
<hkern u1="&#xf3;" u2="]" k="51" />
<hkern u1="&#xf3;" u2="\" k="45" />
<hkern u1="&#xf3;" u2="Y" k="178" />
<hkern u1="&#xf3;" u2="X" k="84" />
<hkern u1="&#xf3;" u2="V" k="109" />
<hkern u1="&#xf3;" u2="U" k="31" />
<hkern u1="&#xf3;" u2="T" k="176" />
<hkern u1="&#xf3;" u2="S" k="29" />
<hkern u1="&#xf3;" u2="R" k="29" />
<hkern u1="&#xf3;" u2="Q" k="12" />
<hkern u1="&#xf3;" u2="P" k="29" />
<hkern u1="&#xf3;" u2="O" k="12" />
<hkern u1="&#xf3;" u2="N" k="29" />
<hkern u1="&#xf3;" u2="M" k="29" />
<hkern u1="&#xf3;" u2="L" k="29" />
<hkern u1="&#xf3;" u2="K" k="29" />
<hkern u1="&#xf3;" u2="I" k="29" />
<hkern u1="&#xf3;" u2="H" k="29" />
<hkern u1="&#xf3;" u2="G" k="12" />
<hkern u1="&#xf3;" u2="F" k="29" />
<hkern u1="&#xf3;" u2="E" k="29" />
<hkern u1="&#xf3;" u2="D" k="29" />
<hkern u1="&#xf3;" u2="C" k="12" />
<hkern u1="&#xf3;" u2="B" k="29" />
<hkern u1="&#xf3;" u2="A" k="25" />
<hkern u1="&#xf3;" u2="&#x3f;" k="41" />
<hkern u1="&#xf3;" u2="&#x2a;" k="29" />
<hkern u1="&#xf3;" u2="&#x29;" k="55" />
<hkern u1="&#xf3;" u2="&#x27;" k="33" />
<hkern u1="&#xf3;" u2="&#x22;" k="33" />
<hkern u1="&#xf4;" u2="&#xfb04;" k="10" />
<hkern u1="&#xf4;" u2="&#xfb03;" k="10" />
<hkern u1="&#xf4;" u2="&#xfb02;" k="10" />
<hkern u1="&#xf4;" u2="&#xfb01;" k="10" />
<hkern u1="&#xf4;" u2="&#x2122;" k="47" />
<hkern u1="&#xf4;" u2="&#x201d;" k="33" />
<hkern u1="&#xf4;" u2="&#x201c;" k="31" />
<hkern u1="&#xf4;" u2="&#x2019;" k="33" />
<hkern u1="&#xf4;" u2="&#x2018;" k="31" />
<hkern u1="&#xf4;" u2="&#xff;" k="10" />
<hkern u1="&#xf4;" u2="&#xfd;" k="10" />
<hkern u1="&#xf4;" u2="&#xdf;" k="10" />
<hkern u1="&#xf4;" u2="&#xde;" k="29" />
<hkern u1="&#xf4;" u2="&#xdd;" k="178" />
<hkern u1="&#xf4;" u2="&#xdc;" k="31" />
<hkern u1="&#xf4;" u2="&#xdb;" k="31" />
<hkern u1="&#xf4;" u2="&#xda;" k="31" />
<hkern u1="&#xf4;" u2="&#xd9;" k="31" />
<hkern u1="&#xf4;" u2="&#xd8;" k="12" />
<hkern u1="&#xf4;" u2="&#xd6;" k="12" />
<hkern u1="&#xf4;" u2="&#xd5;" k="12" />
<hkern u1="&#xf4;" u2="&#xd4;" k="12" />
<hkern u1="&#xf4;" u2="&#xd3;" k="12" />
<hkern u1="&#xf4;" u2="&#xd2;" k="12" />
<hkern u1="&#xf4;" u2="&#xd1;" k="29" />
<hkern u1="&#xf4;" u2="&#xd0;" k="29" />
<hkern u1="&#xf4;" u2="&#xcf;" k="29" />
<hkern u1="&#xf4;" u2="&#xce;" k="29" />
<hkern u1="&#xf4;" u2="&#xcd;" k="29" />
<hkern u1="&#xf4;" u2="&#xcc;" k="29" />
<hkern u1="&#xf4;" u2="&#xcb;" k="29" />
<hkern u1="&#xf4;" u2="&#xca;" k="29" />
<hkern u1="&#xf4;" u2="&#xc9;" k="29" />
<hkern u1="&#xf4;" u2="&#xc8;" k="29" />
<hkern u1="&#xf4;" u2="&#xc7;" k="12" />
<hkern u1="&#xf4;" u2="&#xc5;" k="25" />
<hkern u1="&#xf4;" u2="&#xc4;" k="25" />
<hkern u1="&#xf4;" u2="&#xc3;" k="25" />
<hkern u1="&#xf4;" u2="&#xc2;" k="25" />
<hkern u1="&#xf4;" u2="&#xc1;" k="25" />
<hkern u1="&#xf4;" u2="&#xc0;" k="25" />
<hkern u1="&#xf4;" u2="&#x7d;" k="47" />
<hkern u1="&#xf4;" u2="y" k="10" />
<hkern u1="&#xf4;" u2="x" k="31" />
<hkern u1="&#xf4;" u2="v" k="14" />
<hkern u1="&#xf4;" u2="t" k="8" />
<hkern u1="&#xf4;" u2="f" k="10" />
<hkern u1="&#xf4;" u2="]" k="51" />
<hkern u1="&#xf4;" u2="\" k="45" />
<hkern u1="&#xf4;" u2="Y" k="178" />
<hkern u1="&#xf4;" u2="X" k="84" />
<hkern u1="&#xf4;" u2="V" k="109" />
<hkern u1="&#xf4;" u2="U" k="31" />
<hkern u1="&#xf4;" u2="T" k="176" />
<hkern u1="&#xf4;" u2="S" k="29" />
<hkern u1="&#xf4;" u2="R" k="29" />
<hkern u1="&#xf4;" u2="Q" k="12" />
<hkern u1="&#xf4;" u2="P" k="29" />
<hkern u1="&#xf4;" u2="O" k="12" />
<hkern u1="&#xf4;" u2="N" k="29" />
<hkern u1="&#xf4;" u2="M" k="29" />
<hkern u1="&#xf4;" u2="L" k="29" />
<hkern u1="&#xf4;" u2="K" k="29" />
<hkern u1="&#xf4;" u2="I" k="29" />
<hkern u1="&#xf4;" u2="H" k="29" />
<hkern u1="&#xf4;" u2="G" k="12" />
<hkern u1="&#xf4;" u2="F" k="29" />
<hkern u1="&#xf4;" u2="E" k="29" />
<hkern u1="&#xf4;" u2="D" k="29" />
<hkern u1="&#xf4;" u2="C" k="12" />
<hkern u1="&#xf4;" u2="B" k="29" />
<hkern u1="&#xf4;" u2="A" k="25" />
<hkern u1="&#xf4;" u2="&#x3f;" k="41" />
<hkern u1="&#xf4;" u2="&#x2a;" k="29" />
<hkern u1="&#xf4;" u2="&#x29;" k="55" />
<hkern u1="&#xf4;" u2="&#x27;" k="33" />
<hkern u1="&#xf4;" u2="&#x22;" k="33" />
<hkern u1="&#xf5;" u2="&#xfb04;" k="10" />
<hkern u1="&#xf5;" u2="&#xfb03;" k="10" />
<hkern u1="&#xf5;" u2="&#xfb02;" k="10" />
<hkern u1="&#xf5;" u2="&#xfb01;" k="10" />
<hkern u1="&#xf5;" u2="&#x2122;" k="47" />
<hkern u1="&#xf5;" u2="&#x201d;" k="33" />
<hkern u1="&#xf5;" u2="&#x201c;" k="31" />
<hkern u1="&#xf5;" u2="&#x2019;" k="33" />
<hkern u1="&#xf5;" u2="&#x2018;" k="31" />
<hkern u1="&#xf5;" u2="&#xff;" k="10" />
<hkern u1="&#xf5;" u2="&#xfd;" k="10" />
<hkern u1="&#xf5;" u2="&#xdf;" k="10" />
<hkern u1="&#xf5;" u2="&#xde;" k="29" />
<hkern u1="&#xf5;" u2="&#xdd;" k="178" />
<hkern u1="&#xf5;" u2="&#xdc;" k="31" />
<hkern u1="&#xf5;" u2="&#xdb;" k="31" />
<hkern u1="&#xf5;" u2="&#xda;" k="31" />
<hkern u1="&#xf5;" u2="&#xd9;" k="31" />
<hkern u1="&#xf5;" u2="&#xd8;" k="12" />
<hkern u1="&#xf5;" u2="&#xd6;" k="12" />
<hkern u1="&#xf5;" u2="&#xd5;" k="12" />
<hkern u1="&#xf5;" u2="&#xd4;" k="12" />
<hkern u1="&#xf5;" u2="&#xd3;" k="12" />
<hkern u1="&#xf5;" u2="&#xd2;" k="12" />
<hkern u1="&#xf5;" u2="&#xd1;" k="29" />
<hkern u1="&#xf5;" u2="&#xd0;" k="29" />
<hkern u1="&#xf5;" u2="&#xcf;" k="29" />
<hkern u1="&#xf5;" u2="&#xce;" k="29" />
<hkern u1="&#xf5;" u2="&#xcd;" k="29" />
<hkern u1="&#xf5;" u2="&#xcc;" k="29" />
<hkern u1="&#xf5;" u2="&#xcb;" k="29" />
<hkern u1="&#xf5;" u2="&#xca;" k="29" />
<hkern u1="&#xf5;" u2="&#xc9;" k="29" />
<hkern u1="&#xf5;" u2="&#xc8;" k="29" />
<hkern u1="&#xf5;" u2="&#xc7;" k="12" />
<hkern u1="&#xf5;" u2="&#xc5;" k="25" />
<hkern u1="&#xf5;" u2="&#xc4;" k="25" />
<hkern u1="&#xf5;" u2="&#xc3;" k="25" />
<hkern u1="&#xf5;" u2="&#xc2;" k="25" />
<hkern u1="&#xf5;" u2="&#xc1;" k="25" />
<hkern u1="&#xf5;" u2="&#xc0;" k="25" />
<hkern u1="&#xf5;" u2="&#x7d;" k="47" />
<hkern u1="&#xf5;" u2="y" k="10" />
<hkern u1="&#xf5;" u2="x" k="31" />
<hkern u1="&#xf5;" u2="v" k="14" />
<hkern u1="&#xf5;" u2="t" k="8" />
<hkern u1="&#xf5;" u2="f" k="10" />
<hkern u1="&#xf5;" u2="]" k="51" />
<hkern u1="&#xf5;" u2="\" k="45" />
<hkern u1="&#xf5;" u2="Y" k="178" />
<hkern u1="&#xf5;" u2="X" k="84" />
<hkern u1="&#xf5;" u2="V" k="109" />
<hkern u1="&#xf5;" u2="U" k="31" />
<hkern u1="&#xf5;" u2="T" k="176" />
<hkern u1="&#xf5;" u2="S" k="29" />
<hkern u1="&#xf5;" u2="R" k="29" />
<hkern u1="&#xf5;" u2="Q" k="12" />
<hkern u1="&#xf5;" u2="P" k="29" />
<hkern u1="&#xf5;" u2="O" k="12" />
<hkern u1="&#xf5;" u2="N" k="29" />
<hkern u1="&#xf5;" u2="M" k="29" />
<hkern u1="&#xf5;" u2="L" k="29" />
<hkern u1="&#xf5;" u2="K" k="29" />
<hkern u1="&#xf5;" u2="I" k="29" />
<hkern u1="&#xf5;" u2="H" k="29" />
<hkern u1="&#xf5;" u2="G" k="12" />
<hkern u1="&#xf5;" u2="F" k="29" />
<hkern u1="&#xf5;" u2="E" k="29" />
<hkern u1="&#xf5;" u2="D" k="29" />
<hkern u1="&#xf5;" u2="C" k="12" />
<hkern u1="&#xf5;" u2="B" k="29" />
<hkern u1="&#xf5;" u2="A" k="25" />
<hkern u1="&#xf5;" u2="&#x3f;" k="41" />
<hkern u1="&#xf5;" u2="&#x2a;" k="29" />
<hkern u1="&#xf5;" u2="&#x29;" k="55" />
<hkern u1="&#xf5;" u2="&#x27;" k="33" />
<hkern u1="&#xf5;" u2="&#x22;" k="33" />
<hkern u1="&#xf6;" u2="&#xfb04;" k="10" />
<hkern u1="&#xf6;" u2="&#xfb03;" k="10" />
<hkern u1="&#xf6;" u2="&#xfb02;" k="10" />
<hkern u1="&#xf6;" u2="&#xfb01;" k="10" />
<hkern u1="&#xf6;" u2="&#x2122;" k="47" />
<hkern u1="&#xf6;" u2="&#x201d;" k="33" />
<hkern u1="&#xf6;" u2="&#x201c;" k="31" />
<hkern u1="&#xf6;" u2="&#x2019;" k="33" />
<hkern u1="&#xf6;" u2="&#x2018;" k="31" />
<hkern u1="&#xf6;" u2="&#xff;" k="10" />
<hkern u1="&#xf6;" u2="&#xfd;" k="10" />
<hkern u1="&#xf6;" u2="&#xdf;" k="10" />
<hkern u1="&#xf6;" u2="&#xde;" k="29" />
<hkern u1="&#xf6;" u2="&#xdd;" k="178" />
<hkern u1="&#xf6;" u2="&#xdc;" k="31" />
<hkern u1="&#xf6;" u2="&#xdb;" k="31" />
<hkern u1="&#xf6;" u2="&#xda;" k="31" />
<hkern u1="&#xf6;" u2="&#xd9;" k="31" />
<hkern u1="&#xf6;" u2="&#xd8;" k="12" />
<hkern u1="&#xf6;" u2="&#xd6;" k="12" />
<hkern u1="&#xf6;" u2="&#xd5;" k="12" />
<hkern u1="&#xf6;" u2="&#xd4;" k="12" />
<hkern u1="&#xf6;" u2="&#xd3;" k="12" />
<hkern u1="&#xf6;" u2="&#xd2;" k="12" />
<hkern u1="&#xf6;" u2="&#xd1;" k="29" />
<hkern u1="&#xf6;" u2="&#xd0;" k="29" />
<hkern u1="&#xf6;" u2="&#xcf;" k="29" />
<hkern u1="&#xf6;" u2="&#xce;" k="29" />
<hkern u1="&#xf6;" u2="&#xcd;" k="29" />
<hkern u1="&#xf6;" u2="&#xcc;" k="29" />
<hkern u1="&#xf6;" u2="&#xcb;" k="29" />
<hkern u1="&#xf6;" u2="&#xca;" k="29" />
<hkern u1="&#xf6;" u2="&#xc9;" k="29" />
<hkern u1="&#xf6;" u2="&#xc8;" k="29" />
<hkern u1="&#xf6;" u2="&#xc7;" k="12" />
<hkern u1="&#xf6;" u2="&#xc5;" k="25" />
<hkern u1="&#xf6;" u2="&#xc4;" k="25" />
<hkern u1="&#xf6;" u2="&#xc3;" k="25" />
<hkern u1="&#xf6;" u2="&#xc2;" k="25" />
<hkern u1="&#xf6;" u2="&#xc1;" k="25" />
<hkern u1="&#xf6;" u2="&#xc0;" k="25" />
<hkern u1="&#xf6;" u2="&#x7d;" k="47" />
<hkern u1="&#xf6;" u2="y" k="10" />
<hkern u1="&#xf6;" u2="x" k="31" />
<hkern u1="&#xf6;" u2="v" k="14" />
<hkern u1="&#xf6;" u2="t" k="8" />
<hkern u1="&#xf6;" u2="f" k="10" />
<hkern u1="&#xf6;" u2="]" k="51" />
<hkern u1="&#xf6;" u2="\" k="45" />
<hkern u1="&#xf6;" u2="Y" k="178" />
<hkern u1="&#xf6;" u2="X" k="84" />
<hkern u1="&#xf6;" u2="V" k="109" />
<hkern u1="&#xf6;" u2="U" k="31" />
<hkern u1="&#xf6;" u2="T" k="176" />
<hkern u1="&#xf6;" u2="S" k="29" />
<hkern u1="&#xf6;" u2="R" k="29" />
<hkern u1="&#xf6;" u2="Q" k="12" />
<hkern u1="&#xf6;" u2="P" k="29" />
<hkern u1="&#xf6;" u2="O" k="12" />
<hkern u1="&#xf6;" u2="N" k="29" />
<hkern u1="&#xf6;" u2="M" k="29" />
<hkern u1="&#xf6;" u2="L" k="29" />
<hkern u1="&#xf6;" u2="K" k="29" />
<hkern u1="&#xf6;" u2="I" k="29" />
<hkern u1="&#xf6;" u2="H" k="29" />
<hkern u1="&#xf6;" u2="G" k="12" />
<hkern u1="&#xf6;" u2="F" k="29" />
<hkern u1="&#xf6;" u2="E" k="29" />
<hkern u1="&#xf6;" u2="D" k="29" />
<hkern u1="&#xf6;" u2="C" k="12" />
<hkern u1="&#xf6;" u2="B" k="29" />
<hkern u1="&#xf6;" u2="A" k="25" />
<hkern u1="&#xf6;" u2="&#x3f;" k="41" />
<hkern u1="&#xf6;" u2="&#x2a;" k="29" />
<hkern u1="&#xf6;" u2="&#x29;" k="55" />
<hkern u1="&#xf6;" u2="&#x27;" k="33" />
<hkern u1="&#xf6;" u2="&#x22;" k="33" />
<hkern u1="&#xf8;" u2="&#xfb04;" k="10" />
<hkern u1="&#xf8;" u2="&#xfb03;" k="10" />
<hkern u1="&#xf8;" u2="&#xfb02;" k="10" />
<hkern u1="&#xf8;" u2="&#xfb01;" k="10" />
<hkern u1="&#xf8;" u2="&#x2122;" k="47" />
<hkern u1="&#xf8;" u2="&#x201d;" k="33" />
<hkern u1="&#xf8;" u2="&#x201c;" k="31" />
<hkern u1="&#xf8;" u2="&#x2019;" k="33" />
<hkern u1="&#xf8;" u2="&#x2018;" k="31" />
<hkern u1="&#xf8;" u2="&#xff;" k="10" />
<hkern u1="&#xf8;" u2="&#xfd;" k="10" />
<hkern u1="&#xf8;" u2="&#xdf;" k="10" />
<hkern u1="&#xf8;" u2="&#xde;" k="29" />
<hkern u1="&#xf8;" u2="&#xdd;" k="178" />
<hkern u1="&#xf8;" u2="&#xdc;" k="31" />
<hkern u1="&#xf8;" u2="&#xdb;" k="31" />
<hkern u1="&#xf8;" u2="&#xda;" k="31" />
<hkern u1="&#xf8;" u2="&#xd9;" k="31" />
<hkern u1="&#xf8;" u2="&#xd8;" k="12" />
<hkern u1="&#xf8;" u2="&#xd6;" k="12" />
<hkern u1="&#xf8;" u2="&#xd5;" k="12" />
<hkern u1="&#xf8;" u2="&#xd4;" k="12" />
<hkern u1="&#xf8;" u2="&#xd3;" k="12" />
<hkern u1="&#xf8;" u2="&#xd2;" k="12" />
<hkern u1="&#xf8;" u2="&#xd1;" k="29" />
<hkern u1="&#xf8;" u2="&#xd0;" k="29" />
<hkern u1="&#xf8;" u2="&#xcf;" k="29" />
<hkern u1="&#xf8;" u2="&#xce;" k="29" />
<hkern u1="&#xf8;" u2="&#xcd;" k="29" />
<hkern u1="&#xf8;" u2="&#xcc;" k="29" />
<hkern u1="&#xf8;" u2="&#xcb;" k="29" />
<hkern u1="&#xf8;" u2="&#xca;" k="29" />
<hkern u1="&#xf8;" u2="&#xc9;" k="29" />
<hkern u1="&#xf8;" u2="&#xc8;" k="29" />
<hkern u1="&#xf8;" u2="&#xc7;" k="12" />
<hkern u1="&#xf8;" u2="&#xc5;" k="25" />
<hkern u1="&#xf8;" u2="&#xc4;" k="25" />
<hkern u1="&#xf8;" u2="&#xc3;" k="25" />
<hkern u1="&#xf8;" u2="&#xc2;" k="25" />
<hkern u1="&#xf8;" u2="&#xc1;" k="25" />
<hkern u1="&#xf8;" u2="&#xc0;" k="25" />
<hkern u1="&#xf8;" u2="&#x7d;" k="47" />
<hkern u1="&#xf8;" u2="y" k="10" />
<hkern u1="&#xf8;" u2="x" k="31" />
<hkern u1="&#xf8;" u2="v" k="14" />
<hkern u1="&#xf8;" u2="t" k="8" />
<hkern u1="&#xf8;" u2="f" k="10" />
<hkern u1="&#xf8;" u2="]" k="51" />
<hkern u1="&#xf8;" u2="\" k="45" />
<hkern u1="&#xf8;" u2="Y" k="178" />
<hkern u1="&#xf8;" u2="X" k="84" />
<hkern u1="&#xf8;" u2="V" k="109" />
<hkern u1="&#xf8;" u2="U" k="31" />
<hkern u1="&#xf8;" u2="T" k="176" />
<hkern u1="&#xf8;" u2="S" k="29" />
<hkern u1="&#xf8;" u2="R" k="29" />
<hkern u1="&#xf8;" u2="Q" k="12" />
<hkern u1="&#xf8;" u2="P" k="29" />
<hkern u1="&#xf8;" u2="O" k="12" />
<hkern u1="&#xf8;" u2="N" k="29" />
<hkern u1="&#xf8;" u2="M" k="29" />
<hkern u1="&#xf8;" u2="L" k="29" />
<hkern u1="&#xf8;" u2="K" k="29" />
<hkern u1="&#xf8;" u2="I" k="29" />
<hkern u1="&#xf8;" u2="H" k="29" />
<hkern u1="&#xf8;" u2="G" k="12" />
<hkern u1="&#xf8;" u2="F" k="29" />
<hkern u1="&#xf8;" u2="E" k="29" />
<hkern u1="&#xf8;" u2="D" k="29" />
<hkern u1="&#xf8;" u2="C" k="12" />
<hkern u1="&#xf8;" u2="B" k="29" />
<hkern u1="&#xf8;" u2="A" k="25" />
<hkern u1="&#xf8;" u2="&#x3f;" k="41" />
<hkern u1="&#xf8;" u2="&#x2a;" k="29" />
<hkern u1="&#xf8;" u2="&#x29;" k="55" />
<hkern u1="&#xf8;" u2="&#x27;" k="33" />
<hkern u1="&#xf8;" u2="&#x22;" k="33" />
<hkern u1="&#xf9;" u2="&#x2122;" k="37" />
<hkern u1="&#xf9;" u2="&#xde;" k="20" />
<hkern u1="&#xf9;" u2="&#xdd;" k="143" />
<hkern u1="&#xf9;" u2="&#xdc;" k="37" />
<hkern u1="&#xf9;" u2="&#xdb;" k="37" />
<hkern u1="&#xf9;" u2="&#xda;" k="37" />
<hkern u1="&#xf9;" u2="&#xd9;" k="37" />
<hkern u1="&#xf9;" u2="&#xd8;" k="18" />
<hkern u1="&#xf9;" u2="&#xd6;" k="18" />
<hkern u1="&#xf9;" u2="&#xd5;" k="18" />
<hkern u1="&#xf9;" u2="&#xd4;" k="18" />
<hkern u1="&#xf9;" u2="&#xd3;" k="18" />
<hkern u1="&#xf9;" u2="&#xd2;" k="18" />
<hkern u1="&#xf9;" u2="&#xd1;" k="20" />
<hkern u1="&#xf9;" u2="&#xd0;" k="20" />
<hkern u1="&#xf9;" u2="&#xcf;" k="20" />
<hkern u1="&#xf9;" u2="&#xce;" k="20" />
<hkern u1="&#xf9;" u2="&#xcd;" k="20" />
<hkern u1="&#xf9;" u2="&#xcc;" k="20" />
<hkern u1="&#xf9;" u2="&#xcb;" k="20" />
<hkern u1="&#xf9;" u2="&#xca;" k="20" />
<hkern u1="&#xf9;" u2="&#xc9;" k="20" />
<hkern u1="&#xf9;" u2="&#xc8;" k="20" />
<hkern u1="&#xf9;" u2="&#xc7;" k="18" />
<hkern u1="&#xf9;" u2="&#xc5;" k="12" />
<hkern u1="&#xf9;" u2="&#xc4;" k="12" />
<hkern u1="&#xf9;" u2="&#xc3;" k="12" />
<hkern u1="&#xf9;" u2="&#xc2;" k="12" />
<hkern u1="&#xf9;" u2="&#xc1;" k="12" />
<hkern u1="&#xf9;" u2="&#xc0;" k="12" />
<hkern u1="&#xf9;" u2="&#x7d;" k="37" />
<hkern u1="&#xf9;" u2="]" k="41" />
<hkern u1="&#xf9;" u2="\" k="31" />
<hkern u1="&#xf9;" u2="Y" k="143" />
<hkern u1="&#xf9;" u2="X" k="16" />
<hkern u1="&#xf9;" u2="V" k="90" />
<hkern u1="&#xf9;" u2="U" k="37" />
<hkern u1="&#xf9;" u2="T" k="137" />
<hkern u1="&#xf9;" u2="S" k="16" />
<hkern u1="&#xf9;" u2="R" k="20" />
<hkern u1="&#xf9;" u2="Q" k="18" />
<hkern u1="&#xf9;" u2="P" k="20" />
<hkern u1="&#xf9;" u2="O" k="18" />
<hkern u1="&#xf9;" u2="N" k="20" />
<hkern u1="&#xf9;" u2="M" k="20" />
<hkern u1="&#xf9;" u2="L" k="20" />
<hkern u1="&#xf9;" u2="K" k="20" />
<hkern u1="&#xf9;" u2="I" k="20" />
<hkern u1="&#xf9;" u2="H" k="20" />
<hkern u1="&#xf9;" u2="G" k="18" />
<hkern u1="&#xf9;" u2="F" k="20" />
<hkern u1="&#xf9;" u2="E" k="20" />
<hkern u1="&#xf9;" u2="D" k="20" />
<hkern u1="&#xf9;" u2="C" k="18" />
<hkern u1="&#xf9;" u2="B" k="20" />
<hkern u1="&#xf9;" u2="A" k="12" />
<hkern u1="&#xf9;" u2="&#x29;" k="37" />
<hkern u1="&#xf9;" u2="&#x26;" k="14" />
<hkern u1="&#xfa;" u2="&#x2122;" k="37" />
<hkern u1="&#xfa;" u2="&#xde;" k="20" />
<hkern u1="&#xfa;" u2="&#xdd;" k="143" />
<hkern u1="&#xfa;" u2="&#xdc;" k="37" />
<hkern u1="&#xfa;" u2="&#xdb;" k="37" />
<hkern u1="&#xfa;" u2="&#xda;" k="37" />
<hkern u1="&#xfa;" u2="&#xd9;" k="37" />
<hkern u1="&#xfa;" u2="&#xd8;" k="18" />
<hkern u1="&#xfa;" u2="&#xd6;" k="18" />
<hkern u1="&#xfa;" u2="&#xd5;" k="18" />
<hkern u1="&#xfa;" u2="&#xd4;" k="18" />
<hkern u1="&#xfa;" u2="&#xd3;" k="18" />
<hkern u1="&#xfa;" u2="&#xd2;" k="18" />
<hkern u1="&#xfa;" u2="&#xd1;" k="20" />
<hkern u1="&#xfa;" u2="&#xd0;" k="20" />
<hkern u1="&#xfa;" u2="&#xcf;" k="20" />
<hkern u1="&#xfa;" u2="&#xce;" k="20" />
<hkern u1="&#xfa;" u2="&#xcd;" k="20" />
<hkern u1="&#xfa;" u2="&#xcc;" k="20" />
<hkern u1="&#xfa;" u2="&#xcb;" k="20" />
<hkern u1="&#xfa;" u2="&#xca;" k="20" />
<hkern u1="&#xfa;" u2="&#xc9;" k="20" />
<hkern u1="&#xfa;" u2="&#xc8;" k="20" />
<hkern u1="&#xfa;" u2="&#xc7;" k="18" />
<hkern u1="&#xfa;" u2="&#xc5;" k="12" />
<hkern u1="&#xfa;" u2="&#xc4;" k="12" />
<hkern u1="&#xfa;" u2="&#xc3;" k="12" />
<hkern u1="&#xfa;" u2="&#xc2;" k="12" />
<hkern u1="&#xfa;" u2="&#xc1;" k="12" />
<hkern u1="&#xfa;" u2="&#xc0;" k="12" />
<hkern u1="&#xfa;" u2="&#x7d;" k="37" />
<hkern u1="&#xfa;" u2="]" k="41" />
<hkern u1="&#xfa;" u2="\" k="31" />
<hkern u1="&#xfa;" u2="Y" k="143" />
<hkern u1="&#xfa;" u2="X" k="16" />
<hkern u1="&#xfa;" u2="V" k="90" />
<hkern u1="&#xfa;" u2="U" k="37" />
<hkern u1="&#xfa;" u2="T" k="137" />
<hkern u1="&#xfa;" u2="S" k="16" />
<hkern u1="&#xfa;" u2="R" k="20" />
<hkern u1="&#xfa;" u2="Q" k="18" />
<hkern u1="&#xfa;" u2="P" k="20" />
<hkern u1="&#xfa;" u2="O" k="18" />
<hkern u1="&#xfa;" u2="N" k="20" />
<hkern u1="&#xfa;" u2="M" k="20" />
<hkern u1="&#xfa;" u2="L" k="20" />
<hkern u1="&#xfa;" u2="K" k="20" />
<hkern u1="&#xfa;" u2="I" k="20" />
<hkern u1="&#xfa;" u2="H" k="20" />
<hkern u1="&#xfa;" u2="G" k="18" />
<hkern u1="&#xfa;" u2="F" k="20" />
<hkern u1="&#xfa;" u2="E" k="20" />
<hkern u1="&#xfa;" u2="D" k="20" />
<hkern u1="&#xfa;" u2="C" k="18" />
<hkern u1="&#xfa;" u2="B" k="20" />
<hkern u1="&#xfa;" u2="A" k="12" />
<hkern u1="&#xfa;" u2="&#x29;" k="37" />
<hkern u1="&#xfa;" u2="&#x26;" k="14" />
<hkern u1="&#xfb;" u2="&#x2122;" k="37" />
<hkern u1="&#xfb;" u2="&#xde;" k="20" />
<hkern u1="&#xfb;" u2="&#xdd;" k="143" />
<hkern u1="&#xfb;" u2="&#xdc;" k="37" />
<hkern u1="&#xfb;" u2="&#xdb;" k="37" />
<hkern u1="&#xfb;" u2="&#xda;" k="37" />
<hkern u1="&#xfb;" u2="&#xd9;" k="37" />
<hkern u1="&#xfb;" u2="&#xd8;" k="18" />
<hkern u1="&#xfb;" u2="&#xd6;" k="18" />
<hkern u1="&#xfb;" u2="&#xd5;" k="18" />
<hkern u1="&#xfb;" u2="&#xd4;" k="18" />
<hkern u1="&#xfb;" u2="&#xd3;" k="18" />
<hkern u1="&#xfb;" u2="&#xd2;" k="18" />
<hkern u1="&#xfb;" u2="&#xd1;" k="20" />
<hkern u1="&#xfb;" u2="&#xd0;" k="20" />
<hkern u1="&#xfb;" u2="&#xcf;" k="20" />
<hkern u1="&#xfb;" u2="&#xce;" k="20" />
<hkern u1="&#xfb;" u2="&#xcd;" k="20" />
<hkern u1="&#xfb;" u2="&#xcc;" k="20" />
<hkern u1="&#xfb;" u2="&#xcb;" k="20" />
<hkern u1="&#xfb;" u2="&#xca;" k="20" />
<hkern u1="&#xfb;" u2="&#xc9;" k="20" />
<hkern u1="&#xfb;" u2="&#xc8;" k="20" />
<hkern u1="&#xfb;" u2="&#xc7;" k="18" />
<hkern u1="&#xfb;" u2="&#xc5;" k="12" />
<hkern u1="&#xfb;" u2="&#xc4;" k="12" />
<hkern u1="&#xfb;" u2="&#xc3;" k="12" />
<hkern u1="&#xfb;" u2="&#xc2;" k="12" />
<hkern u1="&#xfb;" u2="&#xc1;" k="12" />
<hkern u1="&#xfb;" u2="&#xc0;" k="12" />
<hkern u1="&#xfb;" u2="&#x7d;" k="37" />
<hkern u1="&#xfb;" u2="]" k="41" />
<hkern u1="&#xfb;" u2="\" k="31" />
<hkern u1="&#xfb;" u2="Y" k="143" />
<hkern u1="&#xfb;" u2="X" k="16" />
<hkern u1="&#xfb;" u2="V" k="90" />
<hkern u1="&#xfb;" u2="U" k="37" />
<hkern u1="&#xfb;" u2="T" k="137" />
<hkern u1="&#xfb;" u2="S" k="16" />
<hkern u1="&#xfb;" u2="R" k="20" />
<hkern u1="&#xfb;" u2="Q" k="18" />
<hkern u1="&#xfb;" u2="P" k="20" />
<hkern u1="&#xfb;" u2="O" k="18" />
<hkern u1="&#xfb;" u2="N" k="20" />
<hkern u1="&#xfb;" u2="M" k="20" />
<hkern u1="&#xfb;" u2="L" k="20" />
<hkern u1="&#xfb;" u2="K" k="20" />
<hkern u1="&#xfb;" u2="I" k="20" />
<hkern u1="&#xfb;" u2="H" k="20" />
<hkern u1="&#xfb;" u2="G" k="18" />
<hkern u1="&#xfb;" u2="F" k="20" />
<hkern u1="&#xfb;" u2="E" k="20" />
<hkern u1="&#xfb;" u2="D" k="20" />
<hkern u1="&#xfb;" u2="C" k="18" />
<hkern u1="&#xfb;" u2="B" k="20" />
<hkern u1="&#xfb;" u2="A" k="12" />
<hkern u1="&#xfb;" u2="&#x29;" k="37" />
<hkern u1="&#xfb;" u2="&#x26;" k="14" />
<hkern u1="&#xfc;" u2="&#x2122;" k="37" />
<hkern u1="&#xfc;" u2="&#xde;" k="20" />
<hkern u1="&#xfc;" u2="&#xdd;" k="143" />
<hkern u1="&#xfc;" u2="&#xdc;" k="37" />
<hkern u1="&#xfc;" u2="&#xdb;" k="37" />
<hkern u1="&#xfc;" u2="&#xda;" k="37" />
<hkern u1="&#xfc;" u2="&#xd9;" k="37" />
<hkern u1="&#xfc;" u2="&#xd8;" k="18" />
<hkern u1="&#xfc;" u2="&#xd6;" k="18" />
<hkern u1="&#xfc;" u2="&#xd5;" k="18" />
<hkern u1="&#xfc;" u2="&#xd4;" k="18" />
<hkern u1="&#xfc;" u2="&#xd3;" k="18" />
<hkern u1="&#xfc;" u2="&#xd2;" k="18" />
<hkern u1="&#xfc;" u2="&#xd1;" k="20" />
<hkern u1="&#xfc;" u2="&#xd0;" k="20" />
<hkern u1="&#xfc;" u2="&#xcf;" k="20" />
<hkern u1="&#xfc;" u2="&#xce;" k="20" />
<hkern u1="&#xfc;" u2="&#xcd;" k="20" />
<hkern u1="&#xfc;" u2="&#xcc;" k="20" />
<hkern u1="&#xfc;" u2="&#xcb;" k="20" />
<hkern u1="&#xfc;" u2="&#xca;" k="20" />
<hkern u1="&#xfc;" u2="&#xc9;" k="20" />
<hkern u1="&#xfc;" u2="&#xc8;" k="20" />
<hkern u1="&#xfc;" u2="&#xc7;" k="18" />
<hkern u1="&#xfc;" u2="&#xc5;" k="12" />
<hkern u1="&#xfc;" u2="&#xc4;" k="12" />
<hkern u1="&#xfc;" u2="&#xc3;" k="12" />
<hkern u1="&#xfc;" u2="&#xc2;" k="12" />
<hkern u1="&#xfc;" u2="&#xc1;" k="12" />
<hkern u1="&#xfc;" u2="&#xc0;" k="12" />
<hkern u1="&#xfc;" u2="&#x7d;" k="37" />
<hkern u1="&#xfc;" u2="]" k="41" />
<hkern u1="&#xfc;" u2="\" k="31" />
<hkern u1="&#xfc;" u2="Y" k="143" />
<hkern u1="&#xfc;" u2="X" k="16" />
<hkern u1="&#xfc;" u2="V" k="90" />
<hkern u1="&#xfc;" u2="U" k="37" />
<hkern u1="&#xfc;" u2="T" k="137" />
<hkern u1="&#xfc;" u2="S" k="16" />
<hkern u1="&#xfc;" u2="R" k="20" />
<hkern u1="&#xfc;" u2="Q" k="18" />
<hkern u1="&#xfc;" u2="P" k="20" />
<hkern u1="&#xfc;" u2="O" k="18" />
<hkern u1="&#xfc;" u2="N" k="20" />
<hkern u1="&#xfc;" u2="M" k="20" />
<hkern u1="&#xfc;" u2="L" k="20" />
<hkern u1="&#xfc;" u2="K" k="20" />
<hkern u1="&#xfc;" u2="I" k="20" />
<hkern u1="&#xfc;" u2="H" k="20" />
<hkern u1="&#xfc;" u2="G" k="18" />
<hkern u1="&#xfc;" u2="F" k="20" />
<hkern u1="&#xfc;" u2="E" k="20" />
<hkern u1="&#xfc;" u2="D" k="20" />
<hkern u1="&#xfc;" u2="C" k="18" />
<hkern u1="&#xfc;" u2="B" k="20" />
<hkern u1="&#xfc;" u2="A" k="12" />
<hkern u1="&#xfc;" u2="&#x29;" k="37" />
<hkern u1="&#xfc;" u2="&#x26;" k="14" />
<hkern u1="&#xfd;" u2="&#x2026;" k="57" />
<hkern u1="&#xfd;" u2="&#x201e;" k="57" />
<hkern u1="&#xfd;" u2="&#x201a;" k="57" />
<hkern u1="&#xfd;" u2="&#xff;" k="-59" />
<hkern u1="&#xfd;" u2="&#xfd;" k="-59" />
<hkern u1="&#xfd;" u2="&#xf8;" k="10" />
<hkern u1="&#xfd;" u2="&#xf6;" k="10" />
<hkern u1="&#xfd;" u2="&#xf5;" k="10" />
<hkern u1="&#xfd;" u2="&#xf4;" k="10" />
<hkern u1="&#xfd;" u2="&#xf3;" k="10" />
<hkern u1="&#xfd;" u2="&#xf2;" k="10" />
<hkern u1="&#xfd;" u2="&#xf0;" k="10" />
<hkern u1="&#xfd;" u2="&#xeb;" k="10" />
<hkern u1="&#xfd;" u2="&#xea;" k="10" />
<hkern u1="&#xfd;" u2="&#xe9;" k="10" />
<hkern u1="&#xfd;" u2="&#xe8;" k="10" />
<hkern u1="&#xfd;" u2="&#xe7;" k="10" />
<hkern u1="&#xfd;" u2="&#xe6;" k="8" />
<hkern u1="&#xfd;" u2="&#xe5;" k="8" />
<hkern u1="&#xfd;" u2="&#xe4;" k="8" />
<hkern u1="&#xfd;" u2="&#xe3;" k="8" />
<hkern u1="&#xfd;" u2="&#xe2;" k="8" />
<hkern u1="&#xfd;" u2="&#xe1;" k="8" />
<hkern u1="&#xfd;" u2="&#xe0;" k="8" />
<hkern u1="&#xfd;" u2="&#xde;" k="14" />
<hkern u1="&#xfd;" u2="&#xdd;" k="68" />
<hkern u1="&#xfd;" u2="&#xdc;" k="12" />
<hkern u1="&#xfd;" u2="&#xdb;" k="12" />
<hkern u1="&#xfd;" u2="&#xda;" k="12" />
<hkern u1="&#xfd;" u2="&#xd9;" k="12" />
<hkern u1="&#xfd;" u2="&#xd1;" k="14" />
<hkern u1="&#xfd;" u2="&#xd0;" k="14" />
<hkern u1="&#xfd;" u2="&#xcf;" k="14" />
<hkern u1="&#xfd;" u2="&#xce;" k="14" />
<hkern u1="&#xfd;" u2="&#xcd;" k="14" />
<hkern u1="&#xfd;" u2="&#xcc;" k="14" />
<hkern u1="&#xfd;" u2="&#xcb;" k="14" />
<hkern u1="&#xfd;" u2="&#xca;" k="14" />
<hkern u1="&#xfd;" u2="&#xc9;" k="14" />
<hkern u1="&#xfd;" u2="&#xc8;" k="14" />
<hkern u1="&#xfd;" u2="&#xc5;" k="70" />
<hkern u1="&#xfd;" u2="&#xc4;" k="70" />
<hkern u1="&#xfd;" u2="&#xc3;" k="70" />
<hkern u1="&#xfd;" u2="&#xc2;" k="70" />
<hkern u1="&#xfd;" u2="&#xc1;" k="70" />
<hkern u1="&#xfd;" u2="&#xc0;" k="70" />
<hkern u1="&#xfd;" u2="&#x7d;" k="27" />
<hkern u1="&#xfd;" u2="y" k="-59" />
<hkern u1="&#xfd;" u2="x" k="-57" />
<hkern u1="&#xfd;" u2="v" k="-63" />
<hkern u1="&#xfd;" u2="t" k="-45" />
<hkern u1="&#xfd;" u2="q" k="10" />
<hkern u1="&#xfd;" u2="o" k="10" />
<hkern u1="&#xfd;" u2="g" k="10" />
<hkern u1="&#xfd;" u2="e" k="10" />
<hkern u1="&#xfd;" u2="d" k="10" />
<hkern u1="&#xfd;" u2="c" k="10" />
<hkern u1="&#xfd;" u2="a" k="8" />
<hkern u1="&#xfd;" u2="]" k="37" />
<hkern u1="&#xfd;" u2="Y" k="68" />
<hkern u1="&#xfd;" u2="X" k="74" />
<hkern u1="&#xfd;" u2="V" k="20" />
<hkern u1="&#xfd;" u2="U" k="12" />
<hkern u1="&#xfd;" u2="T" k="98" />
<hkern u1="&#xfd;" u2="R" k="14" />
<hkern u1="&#xfd;" u2="P" k="14" />
<hkern u1="&#xfd;" u2="N" k="14" />
<hkern u1="&#xfd;" u2="M" k="14" />
<hkern u1="&#xfd;" u2="L" k="14" />
<hkern u1="&#xfd;" u2="K" k="14" />
<hkern u1="&#xfd;" u2="I" k="14" />
<hkern u1="&#xfd;" u2="H" k="14" />
<hkern u1="&#xfd;" u2="F" k="14" />
<hkern u1="&#xfd;" u2="E" k="14" />
<hkern u1="&#xfd;" u2="D" k="14" />
<hkern u1="&#xfd;" u2="B" k="14" />
<hkern u1="&#xfd;" u2="A" k="70" />
<hkern u1="&#xfd;" u2="&#x2f;" k="31" />
<hkern u1="&#xfd;" u2="&#x2e;" k="57" />
<hkern u1="&#xfd;" u2="&#x2c;" k="57" />
<hkern u1="&#xfd;" u2="&#x29;" k="35" />
<hkern u1="&#xfd;" u2="&#x26;" k="25" />
<hkern u1="&#xfe;" u2="&#xfb04;" k="10" />
<hkern u1="&#xfe;" u2="&#xfb03;" k="10" />
<hkern u1="&#xfe;" u2="&#xfb02;" k="10" />
<hkern u1="&#xfe;" u2="&#xfb01;" k="10" />
<hkern u1="&#xfe;" u2="&#x2122;" k="47" />
<hkern u1="&#xfe;" u2="&#x201d;" k="33" />
<hkern u1="&#xfe;" u2="&#x201c;" k="31" />
<hkern u1="&#xfe;" u2="&#x2019;" k="33" />
<hkern u1="&#xfe;" u2="&#x2018;" k="31" />
<hkern u1="&#xfe;" u2="&#xff;" k="10" />
<hkern u1="&#xfe;" u2="&#xfd;" k="10" />
<hkern u1="&#xfe;" u2="&#xdf;" k="10" />
<hkern u1="&#xfe;" u2="&#xde;" k="29" />
<hkern u1="&#xfe;" u2="&#xdd;" k="178" />
<hkern u1="&#xfe;" u2="&#xdc;" k="31" />
<hkern u1="&#xfe;" u2="&#xdb;" k="31" />
<hkern u1="&#xfe;" u2="&#xda;" k="31" />
<hkern u1="&#xfe;" u2="&#xd9;" k="31" />
<hkern u1="&#xfe;" u2="&#xd8;" k="12" />
<hkern u1="&#xfe;" u2="&#xd6;" k="12" />
<hkern u1="&#xfe;" u2="&#xd5;" k="12" />
<hkern u1="&#xfe;" u2="&#xd4;" k="12" />
<hkern u1="&#xfe;" u2="&#xd3;" k="12" />
<hkern u1="&#xfe;" u2="&#xd2;" k="12" />
<hkern u1="&#xfe;" u2="&#xd1;" k="29" />
<hkern u1="&#xfe;" u2="&#xd0;" k="29" />
<hkern u1="&#xfe;" u2="&#xcf;" k="29" />
<hkern u1="&#xfe;" u2="&#xce;" k="29" />
<hkern u1="&#xfe;" u2="&#xcd;" k="29" />
<hkern u1="&#xfe;" u2="&#xcc;" k="29" />
<hkern u1="&#xfe;" u2="&#xcb;" k="29" />
<hkern u1="&#xfe;" u2="&#xca;" k="29" />
<hkern u1="&#xfe;" u2="&#xc9;" k="29" />
<hkern u1="&#xfe;" u2="&#xc8;" k="29" />
<hkern u1="&#xfe;" u2="&#xc7;" k="12" />
<hkern u1="&#xfe;" u2="&#xc5;" k="25" />
<hkern u1="&#xfe;" u2="&#xc4;" k="25" />
<hkern u1="&#xfe;" u2="&#xc3;" k="25" />
<hkern u1="&#xfe;" u2="&#xc2;" k="25" />
<hkern u1="&#xfe;" u2="&#xc1;" k="25" />
<hkern u1="&#xfe;" u2="&#xc0;" k="25" />
<hkern u1="&#xfe;" u2="&#x7d;" k="47" />
<hkern u1="&#xfe;" u2="y" k="10" />
<hkern u1="&#xfe;" u2="x" k="31" />
<hkern u1="&#xfe;" u2="v" k="14" />
<hkern u1="&#xfe;" u2="t" k="8" />
<hkern u1="&#xfe;" u2="f" k="10" />
<hkern u1="&#xfe;" u2="]" k="51" />
<hkern u1="&#xfe;" u2="\" k="45" />
<hkern u1="&#xfe;" u2="Y" k="178" />
<hkern u1="&#xfe;" u2="X" k="84" />
<hkern u1="&#xfe;" u2="V" k="109" />
<hkern u1="&#xfe;" u2="U" k="31" />
<hkern u1="&#xfe;" u2="T" k="176" />
<hkern u1="&#xfe;" u2="S" k="29" />
<hkern u1="&#xfe;" u2="R" k="29" />
<hkern u1="&#xfe;" u2="Q" k="12" />
<hkern u1="&#xfe;" u2="P" k="29" />
<hkern u1="&#xfe;" u2="O" k="12" />
<hkern u1="&#xfe;" u2="N" k="29" />
<hkern u1="&#xfe;" u2="M" k="29" />
<hkern u1="&#xfe;" u2="L" k="29" />
<hkern u1="&#xfe;" u2="K" k="29" />
<hkern u1="&#xfe;" u2="I" k="29" />
<hkern u1="&#xfe;" u2="H" k="29" />
<hkern u1="&#xfe;" u2="G" k="12" />
<hkern u1="&#xfe;" u2="F" k="29" />
<hkern u1="&#xfe;" u2="E" k="29" />
<hkern u1="&#xfe;" u2="D" k="29" />
<hkern u1="&#xfe;" u2="C" k="12" />
<hkern u1="&#xfe;" u2="B" k="29" />
<hkern u1="&#xfe;" u2="A" k="25" />
<hkern u1="&#xfe;" u2="&#x3f;" k="41" />
<hkern u1="&#xfe;" u2="&#x2a;" k="29" />
<hkern u1="&#xfe;" u2="&#x29;" k="55" />
<hkern u1="&#xfe;" u2="&#x27;" k="33" />
<hkern u1="&#xfe;" u2="&#x22;" k="33" />
<hkern u1="&#xff;" u2="&#x2026;" k="57" />
<hkern u1="&#xff;" u2="&#x201e;" k="57" />
<hkern u1="&#xff;" u2="&#x201a;" k="57" />
<hkern u1="&#xff;" u2="&#xff;" k="-59" />
<hkern u1="&#xff;" u2="&#xfd;" k="-59" />
<hkern u1="&#xff;" u2="&#xf8;" k="10" />
<hkern u1="&#xff;" u2="&#xf6;" k="10" />
<hkern u1="&#xff;" u2="&#xf5;" k="10" />
<hkern u1="&#xff;" u2="&#xf4;" k="10" />
<hkern u1="&#xff;" u2="&#xf3;" k="10" />
<hkern u1="&#xff;" u2="&#xf2;" k="10" />
<hkern u1="&#xff;" u2="&#xf0;" k="10" />
<hkern u1="&#xff;" u2="&#xeb;" k="10" />
<hkern u1="&#xff;" u2="&#xea;" k="10" />
<hkern u1="&#xff;" u2="&#xe9;" k="10" />
<hkern u1="&#xff;" u2="&#xe8;" k="10" />
<hkern u1="&#xff;" u2="&#xe7;" k="10" />
<hkern u1="&#xff;" u2="&#xe6;" k="8" />
<hkern u1="&#xff;" u2="&#xe5;" k="8" />
<hkern u1="&#xff;" u2="&#xe4;" k="8" />
<hkern u1="&#xff;" u2="&#xe3;" k="8" />
<hkern u1="&#xff;" u2="&#xe2;" k="8" />
<hkern u1="&#xff;" u2="&#xe1;" k="8" />
<hkern u1="&#xff;" u2="&#xe0;" k="8" />
<hkern u1="&#xff;" u2="&#xde;" k="14" />
<hkern u1="&#xff;" u2="&#xdd;" k="68" />
<hkern u1="&#xff;" u2="&#xdc;" k="12" />
<hkern u1="&#xff;" u2="&#xdb;" k="12" />
<hkern u1="&#xff;" u2="&#xda;" k="12" />
<hkern u1="&#xff;" u2="&#xd9;" k="12" />
<hkern u1="&#xff;" u2="&#xd1;" k="14" />
<hkern u1="&#xff;" u2="&#xd0;" k="14" />
<hkern u1="&#xff;" u2="&#xcf;" k="14" />
<hkern u1="&#xff;" u2="&#xce;" k="14" />
<hkern u1="&#xff;" u2="&#xcd;" k="14" />
<hkern u1="&#xff;" u2="&#xcc;" k="14" />
<hkern u1="&#xff;" u2="&#xcb;" k="14" />
<hkern u1="&#xff;" u2="&#xca;" k="14" />
<hkern u1="&#xff;" u2="&#xc9;" k="14" />
<hkern u1="&#xff;" u2="&#xc8;" k="14" />
<hkern u1="&#xff;" u2="&#xc5;" k="70" />
<hkern u1="&#xff;" u2="&#xc4;" k="70" />
<hkern u1="&#xff;" u2="&#xc3;" k="70" />
<hkern u1="&#xff;" u2="&#xc2;" k="70" />
<hkern u1="&#xff;" u2="&#xc1;" k="70" />
<hkern u1="&#xff;" u2="&#xc0;" k="70" />
<hkern u1="&#xff;" u2="&#x7d;" k="27" />
<hkern u1="&#xff;" u2="y" k="-59" />
<hkern u1="&#xff;" u2="x" k="-57" />
<hkern u1="&#xff;" u2="v" k="-63" />
<hkern u1="&#xff;" u2="t" k="-45" />
<hkern u1="&#xff;" u2="q" k="10" />
<hkern u1="&#xff;" u2="o" k="10" />
<hkern u1="&#xff;" u2="g" k="10" />
<hkern u1="&#xff;" u2="e" k="10" />
<hkern u1="&#xff;" u2="d" k="10" />
<hkern u1="&#xff;" u2="c" k="10" />
<hkern u1="&#xff;" u2="a" k="8" />
<hkern u1="&#xff;" u2="]" k="37" />
<hkern u1="&#xff;" u2="Y" k="68" />
<hkern u1="&#xff;" u2="X" k="74" />
<hkern u1="&#xff;" u2="V" k="20" />
<hkern u1="&#xff;" u2="U" k="12" />
<hkern u1="&#xff;" u2="T" k="98" />
<hkern u1="&#xff;" u2="R" k="14" />
<hkern u1="&#xff;" u2="P" k="14" />
<hkern u1="&#xff;" u2="N" k="14" />
<hkern u1="&#xff;" u2="M" k="14" />
<hkern u1="&#xff;" u2="L" k="14" />
<hkern u1="&#xff;" u2="K" k="14" />
<hkern u1="&#xff;" u2="I" k="14" />
<hkern u1="&#xff;" u2="H" k="14" />
<hkern u1="&#xff;" u2="F" k="14" />
<hkern u1="&#xff;" u2="E" k="14" />
<hkern u1="&#xff;" u2="D" k="14" />
<hkern u1="&#xff;" u2="B" k="14" />
<hkern u1="&#xff;" u2="A" k="70" />
<hkern u1="&#xff;" u2="&#x2f;" k="31" />
<hkern u1="&#xff;" u2="&#x2e;" k="57" />
<hkern u1="&#xff;" u2="&#x2c;" k="57" />
<hkern u1="&#xff;" u2="&#x29;" k="35" />
<hkern u1="&#xff;" u2="&#x26;" k="25" />
<hkern u1="&#x2013;" u2="&#xfb04;" k="27" />
<hkern u1="&#x2013;" u2="&#xfb03;" k="27" />
<hkern u1="&#x2013;" u2="&#xfb02;" k="27" />
<hkern u1="&#x2013;" u2="&#xfb01;" k="27" />
<hkern u1="&#x2013;" u2="&#xdf;" k="27" />
<hkern u1="&#x2013;" u2="&#xdd;" k="135" />
<hkern u1="&#x2013;" u2="&#xc5;" k="29" />
<hkern u1="&#x2013;" u2="&#xc4;" k="29" />
<hkern u1="&#x2013;" u2="&#xc3;" k="29" />
<hkern u1="&#x2013;" u2="&#xc2;" k="29" />
<hkern u1="&#x2013;" u2="&#xc1;" k="29" />
<hkern u1="&#x2013;" u2="&#xc0;" k="29" />
<hkern u1="&#x2013;" u2="x" k="45" />
<hkern u1="&#x2013;" u2="v" k="16" />
<hkern u1="&#x2013;" u2="t" k="25" />
<hkern u1="&#x2013;" u2="f" k="27" />
<hkern u1="&#x2013;" u2="Y" k="135" />
<hkern u1="&#x2013;" u2="X" k="63" />
<hkern u1="&#x2013;" u2="V" k="68" />
<hkern u1="&#x2013;" u2="T" k="131" />
<hkern u1="&#x2013;" u2="S" k="39" />
<hkern u1="&#x2013;" u2="A" k="29" />
<hkern u1="&#x2014;" u2="&#xfb04;" k="27" />
<hkern u1="&#x2014;" u2="&#xfb03;" k="27" />
<hkern u1="&#x2014;" u2="&#xfb02;" k="27" />
<hkern u1="&#x2014;" u2="&#xfb01;" k="27" />
<hkern u1="&#x2014;" u2="&#xdf;" k="27" />
<hkern u1="&#x2014;" u2="&#xdd;" k="135" />
<hkern u1="&#x2014;" u2="&#xc5;" k="29" />
<hkern u1="&#x2014;" u2="&#xc4;" k="29" />
<hkern u1="&#x2014;" u2="&#xc3;" k="29" />
<hkern u1="&#x2014;" u2="&#xc2;" k="29" />
<hkern u1="&#x2014;" u2="&#xc1;" k="29" />
<hkern u1="&#x2014;" u2="&#xc0;" k="29" />
<hkern u1="&#x2014;" u2="x" k="45" />
<hkern u1="&#x2014;" u2="v" k="16" />
<hkern u1="&#x2014;" u2="t" k="25" />
<hkern u1="&#x2014;" u2="f" k="27" />
<hkern u1="&#x2014;" u2="Y" k="135" />
<hkern u1="&#x2014;" u2="X" k="63" />
<hkern u1="&#x2014;" u2="V" k="68" />
<hkern u1="&#x2014;" u2="T" k="131" />
<hkern u1="&#x2014;" u2="S" k="39" />
<hkern u1="&#x2014;" u2="A" k="29" />
<hkern u1="&#x2018;" u2="&#x2026;" k="254" />
<hkern u1="&#x2018;" u2="&#x201e;" k="254" />
<hkern u1="&#x2018;" u2="&#x201a;" k="254" />
<hkern u1="&#x2018;" u2="&#xf8;" k="31" />
<hkern u1="&#x2018;" u2="&#xf6;" k="31" />
<hkern u1="&#x2018;" u2="&#xf5;" k="31" />
<hkern u1="&#x2018;" u2="&#xf4;" k="31" />
<hkern u1="&#x2018;" u2="&#xf3;" k="31" />
<hkern u1="&#x2018;" u2="&#xf2;" k="31" />
<hkern u1="&#x2018;" u2="&#xf0;" k="31" />
<hkern u1="&#x2018;" u2="&#xeb;" k="31" />
<hkern u1="&#x2018;" u2="&#xea;" k="31" />
<hkern u1="&#x2018;" u2="&#xe9;" k="31" />
<hkern u1="&#x2018;" u2="&#xe8;" k="31" />
<hkern u1="&#x2018;" u2="&#xe7;" k="31" />
<hkern u1="&#x2018;" u2="&#xc6;" k="117" />
<hkern u1="&#x2018;" u2="&#xc5;" k="92" />
<hkern u1="&#x2018;" u2="&#xc4;" k="92" />
<hkern u1="&#x2018;" u2="&#xc3;" k="92" />
<hkern u1="&#x2018;" u2="&#xc2;" k="92" />
<hkern u1="&#x2018;" u2="&#xc1;" k="92" />
<hkern u1="&#x2018;" u2="&#xc0;" k="92" />
<hkern u1="&#x2018;" u2="q" k="31" />
<hkern u1="&#x2018;" u2="o" k="31" />
<hkern u1="&#x2018;" u2="g" k="31" />
<hkern u1="&#x2018;" u2="e" k="31" />
<hkern u1="&#x2018;" u2="d" k="31" />
<hkern u1="&#x2018;" u2="c" k="31" />
<hkern u1="&#x2018;" u2="A" k="92" />
<hkern u1="&#x2018;" u2="&#x2e;" k="254" />
<hkern u1="&#x2018;" u2="&#x2c;" k="254" />
<hkern u1="&#x2019;" u2="&#x2039;" k="70" />
<hkern u1="&#x2019;" u2="&#x2026;" k="244" />
<hkern u1="&#x2019;" u2="&#x201e;" k="244" />
<hkern u1="&#x2019;" u2="&#x201a;" k="244" />
<hkern u1="&#x2019;" u2="&#xf8;" k="57" />
<hkern u1="&#x2019;" u2="&#xf6;" k="57" />
<hkern u1="&#x2019;" u2="&#xf5;" k="57" />
<hkern u1="&#x2019;" u2="&#xf4;" k="57" />
<hkern u1="&#x2019;" u2="&#xf3;" k="57" />
<hkern u1="&#x2019;" u2="&#xf2;" k="57" />
<hkern u1="&#x2019;" u2="&#xf0;" k="57" />
<hkern u1="&#x2019;" u2="&#xeb;" k="57" />
<hkern u1="&#x2019;" u2="&#xea;" k="57" />
<hkern u1="&#x2019;" u2="&#xe9;" k="57" />
<hkern u1="&#x2019;" u2="&#xe8;" k="57" />
<hkern u1="&#x2019;" u2="&#xe7;" k="57" />
<hkern u1="&#x2019;" u2="&#xe6;" k="29" />
<hkern u1="&#x2019;" u2="&#xe5;" k="29" />
<hkern u1="&#x2019;" u2="&#xe4;" k="29" />
<hkern u1="&#x2019;" u2="&#xe3;" k="29" />
<hkern u1="&#x2019;" u2="&#xe2;" k="29" />
<hkern u1="&#x2019;" u2="&#xe1;" k="29" />
<hkern u1="&#x2019;" u2="&#xe0;" k="29" />
<hkern u1="&#x2019;" u2="&#xc6;" k="131" />
<hkern u1="&#x2019;" u2="&#xc5;" k="104" />
<hkern u1="&#x2019;" u2="&#xc4;" k="104" />
<hkern u1="&#x2019;" u2="&#xc3;" k="104" />
<hkern u1="&#x2019;" u2="&#xc2;" k="104" />
<hkern u1="&#x2019;" u2="&#xc1;" k="104" />
<hkern u1="&#x2019;" u2="&#xc0;" k="104" />
<hkern u1="&#x2019;" u2="&#xab;" k="70" />
<hkern u1="&#x2019;" u2="s" k="33" />
<hkern u1="&#x2019;" u2="q" k="57" />
<hkern u1="&#x2019;" u2="o" k="57" />
<hkern u1="&#x2019;" u2="g" k="57" />
<hkern u1="&#x2019;" u2="e" k="57" />
<hkern u1="&#x2019;" u2="d" k="57" />
<hkern u1="&#x2019;" u2="c" k="57" />
<hkern u1="&#x2019;" u2="a" k="29" />
<hkern u1="&#x2019;" u2="A" k="104" />
<hkern u1="&#x2019;" u2="&#x40;" k="41" />
<hkern u1="&#x2019;" u2="&#x2f;" k="88" />
<hkern u1="&#x2019;" u2="&#x2e;" k="244" />
<hkern u1="&#x2019;" u2="&#x2c;" k="244" />
<hkern u1="&#x201a;" u2="&#xfb04;" k="20" />
<hkern u1="&#x201a;" u2="&#xfb03;" k="20" />
<hkern u1="&#x201a;" u2="&#xfb02;" k="20" />
<hkern u1="&#x201a;" u2="&#xfb01;" k="20" />
<hkern u1="&#x201a;" u2="&#x201d;" k="256" />
<hkern u1="&#x201a;" u2="&#x201c;" k="254" />
<hkern u1="&#x201a;" u2="&#x2019;" k="244" />
<hkern u1="&#x201a;" u2="&#x2018;" k="254" />
<hkern u1="&#x201a;" u2="&#xff;" k="57" />
<hkern u1="&#x201a;" u2="&#xfd;" k="57" />
<hkern u1="&#x201a;" u2="&#xdf;" k="20" />
<hkern u1="&#x201a;" u2="&#xdd;" k="154" />
<hkern u1="&#x201a;" u2="&#xdc;" k="37" />
<hkern u1="&#x201a;" u2="&#xdb;" k="37" />
<hkern u1="&#x201a;" u2="&#xda;" k="37" />
<hkern u1="&#x201a;" u2="&#xd9;" k="37" />
<hkern u1="&#x201a;" u2="&#xd8;" k="39" />
<hkern u1="&#x201a;" u2="&#xd6;" k="39" />
<hkern u1="&#x201a;" u2="&#xd5;" k="39" />
<hkern u1="&#x201a;" u2="&#xd4;" k="39" />
<hkern u1="&#x201a;" u2="&#xd3;" k="39" />
<hkern u1="&#x201a;" u2="&#xd2;" k="39" />
<hkern u1="&#x201a;" u2="&#xc7;" k="39" />
<hkern u1="&#x201a;" u2="y" k="57" />
<hkern u1="&#x201a;" u2="v" k="63" />
<hkern u1="&#x201a;" u2="t" k="25" />
<hkern u1="&#x201a;" u2="f" k="20" />
<hkern u1="&#x201a;" u2="Y" k="154" />
<hkern u1="&#x201a;" u2="V" k="131" />
<hkern u1="&#x201a;" u2="U" k="37" />
<hkern u1="&#x201a;" u2="T" k="131" />
<hkern u1="&#x201a;" u2="Q" k="39" />
<hkern u1="&#x201a;" u2="O" k="39" />
<hkern u1="&#x201a;" u2="G" k="39" />
<hkern u1="&#x201a;" u2="C" k="39" />
<hkern u1="&#x201a;" u2="&#x27;" k="244" />
<hkern u1="&#x201a;" u2="&#x22;" k="256" />
<hkern u1="&#x201c;" u2="&#x2026;" k="254" />
<hkern u1="&#x201c;" u2="&#x201e;" k="254" />
<hkern u1="&#x201c;" u2="&#x201a;" k="254" />
<hkern u1="&#x201c;" u2="&#xf8;" k="31" />
<hkern u1="&#x201c;" u2="&#xf6;" k="31" />
<hkern u1="&#x201c;" u2="&#xf5;" k="31" />
<hkern u1="&#x201c;" u2="&#xf4;" k="31" />
<hkern u1="&#x201c;" u2="&#xf3;" k="31" />
<hkern u1="&#x201c;" u2="&#xf2;" k="31" />
<hkern u1="&#x201c;" u2="&#xf0;" k="31" />
<hkern u1="&#x201c;" u2="&#xeb;" k="31" />
<hkern u1="&#x201c;" u2="&#xea;" k="31" />
<hkern u1="&#x201c;" u2="&#xe9;" k="31" />
<hkern u1="&#x201c;" u2="&#xe8;" k="31" />
<hkern u1="&#x201c;" u2="&#xe7;" k="31" />
<hkern u1="&#x201c;" u2="&#xc6;" k="117" />
<hkern u1="&#x201c;" u2="&#xc5;" k="92" />
<hkern u1="&#x201c;" u2="&#xc4;" k="92" />
<hkern u1="&#x201c;" u2="&#xc3;" k="92" />
<hkern u1="&#x201c;" u2="&#xc2;" k="92" />
<hkern u1="&#x201c;" u2="&#xc1;" k="92" />
<hkern u1="&#x201c;" u2="&#xc0;" k="92" />
<hkern u1="&#x201c;" u2="q" k="31" />
<hkern u1="&#x201c;" u2="o" k="31" />
<hkern u1="&#x201c;" u2="g" k="31" />
<hkern u1="&#x201c;" u2="e" k="31" />
<hkern u1="&#x201c;" u2="d" k="31" />
<hkern u1="&#x201c;" u2="c" k="31" />
<hkern u1="&#x201c;" u2="A" k="92" />
<hkern u1="&#x201c;" u2="&#x2e;" k="254" />
<hkern u1="&#x201c;" u2="&#x2c;" k="254" />
<hkern u1="&#x201d;" u2="&#x2039;" k="70" />
<hkern u1="&#x201d;" u2="&#x2026;" k="266" />
<hkern u1="&#x201d;" u2="&#x201e;" k="266" />
<hkern u1="&#x201d;" u2="&#x201a;" k="266" />
<hkern u1="&#x201d;" u2="&#xf8;" k="57" />
<hkern u1="&#x201d;" u2="&#xf6;" k="57" />
<hkern u1="&#x201d;" u2="&#xf5;" k="57" />
<hkern u1="&#x201d;" u2="&#xf4;" k="57" />
<hkern u1="&#x201d;" u2="&#xf3;" k="57" />
<hkern u1="&#x201d;" u2="&#xf2;" k="57" />
<hkern u1="&#x201d;" u2="&#xf0;" k="57" />
<hkern u1="&#x201d;" u2="&#xeb;" k="57" />
<hkern u1="&#x201d;" u2="&#xea;" k="57" />
<hkern u1="&#x201d;" u2="&#xe9;" k="57" />
<hkern u1="&#x201d;" u2="&#xe8;" k="57" />
<hkern u1="&#x201d;" u2="&#xe7;" k="57" />
<hkern u1="&#x201d;" u2="&#xe6;" k="29" />
<hkern u1="&#x201d;" u2="&#xe5;" k="29" />
<hkern u1="&#x201d;" u2="&#xe4;" k="29" />
<hkern u1="&#x201d;" u2="&#xe3;" k="29" />
<hkern u1="&#x201d;" u2="&#xe2;" k="29" />
<hkern u1="&#x201d;" u2="&#xe1;" k="29" />
<hkern u1="&#x201d;" u2="&#xe0;" k="29" />
<hkern u1="&#x201d;" u2="&#xc6;" k="131" />
<hkern u1="&#x201d;" u2="&#xc5;" k="104" />
<hkern u1="&#x201d;" u2="&#xc4;" k="104" />
<hkern u1="&#x201d;" u2="&#xc3;" k="104" />
<hkern u1="&#x201d;" u2="&#xc2;" k="104" />
<hkern u1="&#x201d;" u2="&#xc1;" k="104" />
<hkern u1="&#x201d;" u2="&#xc0;" k="104" />
<hkern u1="&#x201d;" u2="&#xab;" k="70" />
<hkern u1="&#x201d;" u2="s" k="33" />
<hkern u1="&#x201d;" u2="q" k="57" />
<hkern u1="&#x201d;" u2="o" k="57" />
<hkern u1="&#x201d;" u2="g" k="57" />
<hkern u1="&#x201d;" u2="e" k="57" />
<hkern u1="&#x201d;" u2="d" k="57" />
<hkern u1="&#x201d;" u2="c" k="57" />
<hkern u1="&#x201d;" u2="a" k="29" />
<hkern u1="&#x201d;" u2="A" k="104" />
<hkern u1="&#x201d;" u2="&#x40;" k="41" />
<hkern u1="&#x201d;" u2="&#x2f;" k="88" />
<hkern u1="&#x201d;" u2="&#x2e;" k="266" />
<hkern u1="&#x201d;" u2="&#x2c;" k="266" />
<hkern u1="&#x201e;" u2="&#xfb04;" k="20" />
<hkern u1="&#x201e;" u2="&#xfb03;" k="20" />
<hkern u1="&#x201e;" u2="&#xfb02;" k="20" />
<hkern u1="&#x201e;" u2="&#xfb01;" k="20" />
<hkern u1="&#x201e;" u2="&#x201d;" k="256" />
<hkern u1="&#x201e;" u2="&#x201c;" k="254" />
<hkern u1="&#x201e;" u2="&#x2019;" k="244" />
<hkern u1="&#x201e;" u2="&#x2018;" k="254" />
<hkern u1="&#x201e;" u2="&#xff;" k="57" />
<hkern u1="&#x201e;" u2="&#xfd;" k="57" />
<hkern u1="&#x201e;" u2="&#xdf;" k="20" />
<hkern u1="&#x201e;" u2="&#xdd;" k="154" />
<hkern u1="&#x201e;" u2="&#xdc;" k="37" />
<hkern u1="&#x201e;" u2="&#xdb;" k="37" />
<hkern u1="&#x201e;" u2="&#xda;" k="37" />
<hkern u1="&#x201e;" u2="&#xd9;" k="37" />
<hkern u1="&#x201e;" u2="&#xd8;" k="39" />
<hkern u1="&#x201e;" u2="&#xd6;" k="39" />
<hkern u1="&#x201e;" u2="&#xd5;" k="39" />
<hkern u1="&#x201e;" u2="&#xd4;" k="39" />
<hkern u1="&#x201e;" u2="&#xd3;" k="39" />
<hkern u1="&#x201e;" u2="&#xd2;" k="39" />
<hkern u1="&#x201e;" u2="&#xc7;" k="39" />
<hkern u1="&#x201e;" u2="y" k="57" />
<hkern u1="&#x201e;" u2="v" k="63" />
<hkern u1="&#x201e;" u2="t" k="25" />
<hkern u1="&#x201e;" u2="f" k="20" />
<hkern u1="&#x201e;" u2="Y" k="154" />
<hkern u1="&#x201e;" u2="V" k="131" />
<hkern u1="&#x201e;" u2="U" k="37" />
<hkern u1="&#x201e;" u2="T" k="131" />
<hkern u1="&#x201e;" u2="Q" k="39" />
<hkern u1="&#x201e;" u2="O" k="39" />
<hkern u1="&#x201e;" u2="G" k="39" />
<hkern u1="&#x201e;" u2="C" k="39" />
<hkern u1="&#x201e;" u2="&#x27;" k="244" />
<hkern u1="&#x201e;" u2="&#x22;" k="256" />
<hkern u1="&#x2039;" u2="&#xdd;" k="66" />
<hkern u1="&#x2039;" u2="Y" k="66" />
<hkern u1="&#x2039;" u2="V" k="25" />
<hkern u1="&#x2039;" u2="T" k="104" />
<hkern u1="&#x203a;" u2="&#x201d;" k="45" />
<hkern u1="&#x203a;" u2="&#x2019;" k="45" />
<hkern u1="&#x203a;" u2="&#xdd;" k="121" />
<hkern u1="&#x203a;" u2="x" k="27" />
<hkern u1="&#x203a;" u2="Y" k="121" />
<hkern u1="&#x203a;" u2="X" k="31" />
<hkern u1="&#x203a;" u2="V" k="63" />
<hkern u1="&#x203a;" u2="T" k="121" />
<hkern u1="&#x203a;" u2="&#x27;" k="45" />
<hkern u1="&#x203a;" u2="&#x22;" k="45" />
<hkern u1="&#x2122;" u2="&#xc6;" k="61" />
<hkern u1="&#x2122;" u2="&#xc5;" k="61" />
<hkern u1="&#x2122;" u2="&#xc4;" k="61" />
<hkern u1="&#x2122;" u2="&#xc3;" k="61" />
<hkern u1="&#x2122;" u2="&#xc2;" k="61" />
<hkern u1="&#x2122;" u2="&#xc1;" k="61" />
<hkern u1="&#x2122;" u2="&#xc0;" k="61" />
<hkern u1="&#x2122;" u2="A" k="61" />
<hkern u1="&#xfb01;" u2="&#xde;" k="20" />
<hkern u1="&#xfb01;" u2="&#xdd;" k="16" />
<hkern u1="&#xfb01;" u2="&#xdc;" k="31" />
<hkern u1="&#xfb01;" u2="&#xdb;" k="31" />
<hkern u1="&#xfb01;" u2="&#xda;" k="31" />
<hkern u1="&#xfb01;" u2="&#xd9;" k="31" />
<hkern u1="&#xfb01;" u2="&#xd8;" k="20" />
<hkern u1="&#xfb01;" u2="&#xd6;" k="20" />
<hkern u1="&#xfb01;" u2="&#xd5;" k="20" />
<hkern u1="&#xfb01;" u2="&#xd4;" k="20" />
<hkern u1="&#xfb01;" u2="&#xd3;" k="20" />
<hkern u1="&#xfb01;" u2="&#xd2;" k="20" />
<hkern u1="&#xfb01;" u2="&#xd1;" k="20" />
<hkern u1="&#xfb01;" u2="&#xd0;" k="20" />
<hkern u1="&#xfb01;" u2="&#xcf;" k="20" />
<hkern u1="&#xfb01;" u2="&#xce;" k="20" />
<hkern u1="&#xfb01;" u2="&#xcd;" k="20" />
<hkern u1="&#xfb01;" u2="&#xcc;" k="20" />
<hkern u1="&#xfb01;" u2="&#xcb;" k="20" />
<hkern u1="&#xfb01;" u2="&#xca;" k="20" />
<hkern u1="&#xfb01;" u2="&#xc9;" k="20" />
<hkern u1="&#xfb01;" u2="&#xc8;" k="20" />
<hkern u1="&#xfb01;" u2="&#xc7;" k="20" />
<hkern u1="&#xfb01;" u2="&#xc5;" k="14" />
<hkern u1="&#xfb01;" u2="&#xc4;" k="14" />
<hkern u1="&#xfb01;" u2="&#xc3;" k="14" />
<hkern u1="&#xfb01;" u2="&#xc2;" k="14" />
<hkern u1="&#xfb01;" u2="&#xc1;" k="14" />
<hkern u1="&#xfb01;" u2="&#xc0;" k="14" />
<hkern u1="&#xfb01;" u2="Y" k="16" />
<hkern u1="&#xfb01;" u2="V" k="18" />
<hkern u1="&#xfb01;" u2="U" k="31" />
<hkern u1="&#xfb01;" u2="T" k="20" />
<hkern u1="&#xfb01;" u2="S" k="18" />
<hkern u1="&#xfb01;" u2="R" k="20" />
<hkern u1="&#xfb01;" u2="Q" k="20" />
<hkern u1="&#xfb01;" u2="P" k="20" />
<hkern u1="&#xfb01;" u2="O" k="20" />
<hkern u1="&#xfb01;" u2="N" k="20" />
<hkern u1="&#xfb01;" u2="M" k="20" />
<hkern u1="&#xfb01;" u2="L" k="20" />
<hkern u1="&#xfb01;" u2="K" k="20" />
<hkern u1="&#xfb01;" u2="I" k="20" />
<hkern u1="&#xfb01;" u2="H" k="20" />
<hkern u1="&#xfb01;" u2="G" k="20" />
<hkern u1="&#xfb01;" u2="F" k="20" />
<hkern u1="&#xfb01;" u2="E" k="20" />
<hkern u1="&#xfb01;" u2="D" k="20" />
<hkern u1="&#xfb01;" u2="C" k="20" />
<hkern u1="&#xfb01;" u2="B" k="20" />
<hkern u1="&#xfb01;" u2="A" k="14" />
<hkern u1="&#xfb01;" u2="&#x26;" k="16" />
<hkern u1="&#xfb02;" u2="&#xde;" k="20" />
<hkern u1="&#xfb02;" u2="&#xdd;" k="23" />
<hkern u1="&#xfb02;" u2="&#xdc;" k="37" />
<hkern u1="&#xfb02;" u2="&#xdb;" k="37" />
<hkern u1="&#xfb02;" u2="&#xda;" k="37" />
<hkern u1="&#xfb02;" u2="&#xd9;" k="37" />
<hkern u1="&#xfb02;" u2="&#xd8;" k="23" />
<hkern u1="&#xfb02;" u2="&#xd6;" k="23" />
<hkern u1="&#xfb02;" u2="&#xd5;" k="23" />
<hkern u1="&#xfb02;" u2="&#xd4;" k="23" />
<hkern u1="&#xfb02;" u2="&#xd3;" k="23" />
<hkern u1="&#xfb02;" u2="&#xd2;" k="23" />
<hkern u1="&#xfb02;" u2="&#xd1;" k="20" />
<hkern u1="&#xfb02;" u2="&#xd0;" k="20" />
<hkern u1="&#xfb02;" u2="&#xcf;" k="20" />
<hkern u1="&#xfb02;" u2="&#xce;" k="20" />
<hkern u1="&#xfb02;" u2="&#xcd;" k="20" />
<hkern u1="&#xfb02;" u2="&#xcc;" k="20" />
<hkern u1="&#xfb02;" u2="&#xcb;" k="20" />
<hkern u1="&#xfb02;" u2="&#xca;" k="20" />
<hkern u1="&#xfb02;" u2="&#xc9;" k="20" />
<hkern u1="&#xfb02;" u2="&#xc8;" k="20" />
<hkern u1="&#xfb02;" u2="&#xc7;" k="23" />
<hkern u1="&#xfb02;" u2="&#xc5;" k="16" />
<hkern u1="&#xfb02;" u2="&#xc4;" k="16" />
<hkern u1="&#xfb02;" u2="&#xc3;" k="16" />
<hkern u1="&#xfb02;" u2="&#xc2;" k="16" />
<hkern u1="&#xfb02;" u2="&#xc1;" k="16" />
<hkern u1="&#xfb02;" u2="&#xc0;" k="16" />
<hkern u1="&#xfb02;" u2="&#xb7;" k="123" />
<hkern u1="&#xfb02;" u2="Y" k="23" />
<hkern u1="&#xfb02;" u2="V" k="23" />
<hkern u1="&#xfb02;" u2="U" k="37" />
<hkern u1="&#xfb02;" u2="T" k="18" />
<hkern u1="&#xfb02;" u2="S" k="18" />
<hkern u1="&#xfb02;" u2="R" k="20" />
<hkern u1="&#xfb02;" u2="Q" k="23" />
<hkern u1="&#xfb02;" u2="P" k="20" />
<hkern u1="&#xfb02;" u2="O" k="23" />
<hkern u1="&#xfb02;" u2="N" k="20" />
<hkern u1="&#xfb02;" u2="M" k="20" />
<hkern u1="&#xfb02;" u2="L" k="20" />
<hkern u1="&#xfb02;" u2="K" k="20" />
<hkern u1="&#xfb02;" u2="I" k="20" />
<hkern u1="&#xfb02;" u2="H" k="20" />
<hkern u1="&#xfb02;" u2="G" k="23" />
<hkern u1="&#xfb02;" u2="F" k="20" />
<hkern u1="&#xfb02;" u2="E" k="20" />
<hkern u1="&#xfb02;" u2="D" k="20" />
<hkern u1="&#xfb02;" u2="C" k="23" />
<hkern u1="&#xfb02;" u2="B" k="20" />
<hkern u1="&#xfb02;" u2="A" k="16" />
<hkern u1="&#xfb02;" u2="&#x26;" k="18" />
<hkern u1="&#xfb03;" u2="&#xde;" k="20" />
<hkern u1="&#xfb03;" u2="&#xdd;" k="16" />
<hkern u1="&#xfb03;" u2="&#xdc;" k="31" />
<hkern u1="&#xfb03;" u2="&#xdb;" k="31" />
<hkern u1="&#xfb03;" u2="&#xda;" k="31" />
<hkern u1="&#xfb03;" u2="&#xd9;" k="31" />
<hkern u1="&#xfb03;" u2="&#xd8;" k="20" />
<hkern u1="&#xfb03;" u2="&#xd6;" k="20" />
<hkern u1="&#xfb03;" u2="&#xd5;" k="20" />
<hkern u1="&#xfb03;" u2="&#xd4;" k="20" />
<hkern u1="&#xfb03;" u2="&#xd3;" k="20" />
<hkern u1="&#xfb03;" u2="&#xd2;" k="20" />
<hkern u1="&#xfb03;" u2="&#xd1;" k="20" />
<hkern u1="&#xfb03;" u2="&#xd0;" k="20" />
<hkern u1="&#xfb03;" u2="&#xcf;" k="20" />
<hkern u1="&#xfb03;" u2="&#xce;" k="20" />
<hkern u1="&#xfb03;" u2="&#xcd;" k="20" />
<hkern u1="&#xfb03;" u2="&#xcc;" k="20" />
<hkern u1="&#xfb03;" u2="&#xcb;" k="20" />
<hkern u1="&#xfb03;" u2="&#xca;" k="20" />
<hkern u1="&#xfb03;" u2="&#xc9;" k="20" />
<hkern u1="&#xfb03;" u2="&#xc8;" k="20" />
<hkern u1="&#xfb03;" u2="&#xc7;" k="20" />
<hkern u1="&#xfb03;" u2="&#xc5;" k="14" />
<hkern u1="&#xfb03;" u2="&#xc4;" k="14" />
<hkern u1="&#xfb03;" u2="&#xc3;" k="14" />
<hkern u1="&#xfb03;" u2="&#xc2;" k="14" />
<hkern u1="&#xfb03;" u2="&#xc1;" k="14" />
<hkern u1="&#xfb03;" u2="&#xc0;" k="14" />
<hkern u1="&#xfb03;" u2="Y" k="16" />
<hkern u1="&#xfb03;" u2="V" k="18" />
<hkern u1="&#xfb03;" u2="U" k="31" />
<hkern u1="&#xfb03;" u2="T" k="20" />
<hkern u1="&#xfb03;" u2="S" k="18" />
<hkern u1="&#xfb03;" u2="R" k="20" />
<hkern u1="&#xfb03;" u2="Q" k="20" />
<hkern u1="&#xfb03;" u2="P" k="20" />
<hkern u1="&#xfb03;" u2="O" k="20" />
<hkern u1="&#xfb03;" u2="N" k="20" />
<hkern u1="&#xfb03;" u2="M" k="20" />
<hkern u1="&#xfb03;" u2="L" k="20" />
<hkern u1="&#xfb03;" u2="K" k="20" />
<hkern u1="&#xfb03;" u2="I" k="20" />
<hkern u1="&#xfb03;" u2="H" k="20" />
<hkern u1="&#xfb03;" u2="G" k="20" />
<hkern u1="&#xfb03;" u2="F" k="20" />
<hkern u1="&#xfb03;" u2="E" k="20" />
<hkern u1="&#xfb03;" u2="D" k="20" />
<hkern u1="&#xfb03;" u2="C" k="20" />
<hkern u1="&#xfb03;" u2="B" k="20" />
<hkern u1="&#xfb03;" u2="A" k="14" />
<hkern u1="&#xfb03;" u2="&#x26;" k="16" />
<hkern u1="&#xfb04;" u2="&#xde;" k="20" />
<hkern u1="&#xfb04;" u2="&#xdd;" k="23" />
<hkern u1="&#xfb04;" u2="&#xdc;" k="37" />
<hkern u1="&#xfb04;" u2="&#xdb;" k="37" />
<hkern u1="&#xfb04;" u2="&#xda;" k="37" />
<hkern u1="&#xfb04;" u2="&#xd9;" k="37" />
<hkern u1="&#xfb04;" u2="&#xd8;" k="23" />
<hkern u1="&#xfb04;" u2="&#xd6;" k="23" />
<hkern u1="&#xfb04;" u2="&#xd5;" k="23" />
<hkern u1="&#xfb04;" u2="&#xd4;" k="23" />
<hkern u1="&#xfb04;" u2="&#xd3;" k="23" />
<hkern u1="&#xfb04;" u2="&#xd2;" k="23" />
<hkern u1="&#xfb04;" u2="&#xd1;" k="20" />
<hkern u1="&#xfb04;" u2="&#xd0;" k="20" />
<hkern u1="&#xfb04;" u2="&#xcf;" k="20" />
<hkern u1="&#xfb04;" u2="&#xce;" k="20" />
<hkern u1="&#xfb04;" u2="&#xcd;" k="20" />
<hkern u1="&#xfb04;" u2="&#xcc;" k="20" />
<hkern u1="&#xfb04;" u2="&#xcb;" k="20" />
<hkern u1="&#xfb04;" u2="&#xca;" k="20" />
<hkern u1="&#xfb04;" u2="&#xc9;" k="20" />
<hkern u1="&#xfb04;" u2="&#xc8;" k="20" />
<hkern u1="&#xfb04;" u2="&#xc7;" k="23" />
<hkern u1="&#xfb04;" u2="&#xc5;" k="16" />
<hkern u1="&#xfb04;" u2="&#xc4;" k="16" />
<hkern u1="&#xfb04;" u2="&#xc3;" k="16" />
<hkern u1="&#xfb04;" u2="&#xc2;" k="16" />
<hkern u1="&#xfb04;" u2="&#xc1;" k="16" />
<hkern u1="&#xfb04;" u2="&#xc0;" k="16" />
<hkern u1="&#xfb04;" u2="&#xb7;" k="123" />
<hkern u1="&#xfb04;" u2="Y" k="23" />
<hkern u1="&#xfb04;" u2="V" k="23" />
<hkern u1="&#xfb04;" u2="U" k="37" />
<hkern u1="&#xfb04;" u2="T" k="18" />
<hkern u1="&#xfb04;" u2="S" k="18" />
<hkern u1="&#xfb04;" u2="R" k="20" />
<hkern u1="&#xfb04;" u2="Q" k="23" />
<hkern u1="&#xfb04;" u2="P" k="20" />
<hkern u1="&#xfb04;" u2="O" k="23" />
<hkern u1="&#xfb04;" u2="N" k="20" />
<hkern u1="&#xfb04;" u2="M" k="20" />
<hkern u1="&#xfb04;" u2="L" k="20" />
<hkern u1="&#xfb04;" u2="K" k="20" />
<hkern u1="&#xfb04;" u2="I" k="20" />
<hkern u1="&#xfb04;" u2="H" k="20" />
<hkern u1="&#xfb04;" u2="G" k="23" />
<hkern u1="&#xfb04;" u2="F" k="20" />
<hkern u1="&#xfb04;" u2="E" k="20" />
<hkern u1="&#xfb04;" u2="D" k="20" />
<hkern u1="&#xfb04;" u2="C" k="23" />
<hkern u1="&#xfb04;" u2="B" k="20" />
<hkern u1="&#xfb04;" u2="A" k="16" />
<hkern u1="&#xfb04;" u2="&#x26;" k="18" />
</font>
</defs></svg> 