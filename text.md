# Augment Agent 工具列表

## 文件操作工具

### 1. str-replace-editor
**功能**: 编辑文件的核心工具
- 支持字符串替换 (`str_replace`) 和插入 (`insert`) 操作
- 每次调用最多编辑150行
- 需要指定精确的行号范围来避免歧义
- 支持多个替换操作在一次调用中完成

**参数**:
- `command`: "str_replace" 或 "insert"
- `path`: 相对于工作区根目录的文件路径
- `old_str_1`, `new_str_1`: 要替换的旧字符串和新字符串
- `old_str_start_line_number_1`, `old_str_end_line_number_1`: 替换范围的行号
- `insert_line_1`, `new_str_1`: 插入位置和内容

### 2. save-file
**功能**: 创建新文件
- 只能创建新文件，不能修改现有文件
- 单个文件最多300行内容
- 默认在文件末尾添加换行符

**参数**:
- `path`: 文件路径
- `file_content`: 文件内容
- `add_last_line_newline`: 是否在末尾添加换行符

### 3. view
**功能**: 查看文件和目录，支持正则表达式搜索
- 查看文件内容（带行号）
- 列出目录结构（最多2层深度）
- 支持正则表达式搜索和上下文显示
- 支持指定行范围查看

**参数**:
- `path`: 文件或目录路径
- `type`: "file" 或 "directory"
- `search_query_regex`: 正则表达式搜索模式
- `view_range`: 查看的行号范围 [start, end]
- `case_sensitive`: 是否区分大小写
- `context_lines_before/after`: 搜索结果的上下文行数

### 4. remove-files
**功能**: 安全删除文件
- 只能删除用户工作区中的文件
- 支持撤销操作

**参数**:
- `file_paths`: 要删除的文件路径数组

## 代码库分析工具

### 5. codebase-retrieval
**功能**: Augment的专有代码库上下文引擎
- 使用自然语言描述查找相关代码片段
- 实时索引，反映代码库当前状态
- 支持多种编程语言
- 高质量的代码检索和嵌入模型

**参数**:
- `information_request`: 需要查找的信息的自然语言描述

### 6. diagnostics
**功能**: 获取IDE中的问题诊断
- 获取错误、警告等问题信息
- 需要指定要检查的文件路径

**参数**:
- `paths`: 要获取诊断信息的文件路径数组

## 进程和终端工具

### 7. launch-process
**功能**: 启动新进程执行shell命令
- 支持等待模式和后台模式
- 等待模式：在交互式终端中运行，等待完成
- 后台模式：在单独终端中运行，立即返回

**参数**:
- `command`: 要执行的shell命令
- `cwd`: 工作目录的绝对路径
- `wait`: 是否等待命令完成
- `max_wait_seconds`: 最大等待时间（秒）

### 8. read-process
**功能**: 读取终端输出
- 可以等待进程完成或立即返回当前输出

**参数**:
- `terminal_id`: 终端ID
- `wait`: 是否等待命令完成
- `max_wait_seconds`: 最大等待时间

### 9. write-process
**功能**: 向终端写入输入
**参数**:
- `terminal_id`: 终端ID
- `input_text`: 要写入的文本

### 10. kill-process
**功能**: 终止进程
**参数**:
- `terminal_id`: 要终止的终端ID

### 11. list-processes
**功能**: 列出所有已知终端及其状态

### 12. read-terminal
**功能**: 读取当前活动终端的输出
- 默认读取终端中所有可见文本
- 可选择只读取选中的文本

**参数**:
- `only_selected`: 是否只读取选中文本

## 网络和浏览器工具

### 13. web-search
**功能**: 使用Google自定义搜索API搜索网络
- 返回Markdown格式的搜索结果
- 包含URL、标题和页面摘要

**参数**:
- `query`: 搜索查询
- `num_results`: 返回结果数量（1-10，默认5）

### 14. web-fetch
**功能**: 获取网页内容并转换为Markdown格式
**参数**:
- `url`: 要获取的网页URL

### 15. open-browser
**功能**: 在默认浏览器中打开URL
- 不返回内容，用于用户视觉检查和交互
- 避免重复打开同一URL

**参数**:
- `url`: 要打开的URL

## GitHub集成工具

### 16. github-api
**功能**: 调用GitHub API
- 支持GET、POST、PATCH、PUT方法
- 自动处理查询参数和JSON请求体
- 限制在当前仓库范围内

**参数**:
- `path`: GitHub API路径
- `method`: HTTP方法（默认GET）
- `data`: 请求数据
- `details`: 是否返回详细字段
- `summary`: API调用的简短描述

**常用路径示例**:
- `/repos/{owner}/{repo}/issues` - 列出问题
- `/repos/{owner}/{repo}/pulls` - 列出PR
- `/search/issues` - 搜索问题和PR
- `/repos/{owner}/{repo}/commits/{sha}/status` - 检查CI状态

## 任务管理工具

### 17. view_tasklist
**功能**: 查看当前对话的任务列表

### 18. add_tasks
**功能**: 添加新任务
- 支持单个或批量添加任务
- 可以创建子任务或在特定任务后插入

**参数**:
- `name`: 任务名称
- `description`: 任务描述
- `parent_task_id`: 父任务ID（创建子任务时）
- `after_task_id`: 在此任务后插入
- `state`: 初始状态
- `tasks`: 批量创建任务数组

### 19. update_tasks
**功能**: 更新任务属性
- 支持单个或批量更新
- 可更新状态、名称、描述

**参数**:
- `task_id`: 要更新的任务ID
- `state`: 新状态（NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE）
- `name`: 新名称
- `description`: 新描述
- `tasks`: 批量更新任务数组

### 20. reorganize_tasklist
**功能**: 重组任务列表结构
- 用于重大结构调整，如重新排序、改变层次结构

**参数**:
- `markdown`: 任务列表的Markdown表示

## 内容查看工具

### 21. view-range-untruncated
**功能**: 查看被截断内容的特定行范围
**参数**:
- `reference_id`: 截断内容的引用ID
- `start_line`: 起始行号
- `end_line`: 结束行号

### 22. search-untruncated
**功能**: 在被截断的内容中搜索
**参数**:
- `reference_id`: 截断内容的引用ID
- `search_term`: 搜索词
- `context_lines`: 上下文行数

## 其他工具

### 23. remember
**功能**: 创建长期记忆
- 用于记住可长期使用的信息
- 不用于临时信息

**参数**:
- `memory`: 要记住的简洁记忆（1句话）

### 24. render-mermaid
**功能**: 渲染Mermaid图表
- 创建交互式图表，支持平移/缩放
- 提供复制功能

**参数**:
- `diagram_definition`: Mermaid图表定义代码
- `title`: 图表标题（可选）

## 使用指南

1. **文件编辑**: 优先使用 `str-replace-editor`，避免重写整个文件
2. **信息收集**: 编辑前先使用 `codebase-retrieval` 获取详细信息
3. **包管理**: 使用适当的包管理器而非手动编辑配置文件
4. **任务管理**: 复杂工作考虑使用任务管理工具进行结构化规划
5. **代码显示**: 使用 `<augment_code_snippet>` 标签包装代码片段
